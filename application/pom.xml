<project>
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ctrip.dcs.ops</groupId>
        <artifactId>dcs-ops-platform</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>application</artifactId>

    <packaging>war</packaging>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.dcs.ops</groupId>
            <artifactId>infrastructure</artifactId>
        </dependency>
        <!--SOA契约-->
        <dependency>
            <groupId>com.ctrip.dcs.ops</groupId>
            <artifactId>ops-platform-service</artifactId>
            <version>1.1.4-SNAPSHOT</version>
        </dependency>
        <!--SOA服务端-->
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>soa-server</artifactId>
        </dependency>
        <!--QSchedule-->
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>qschedule</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.tour</groupId>
            <artifactId>tour.auth.soa</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.geo</groupId>
            <artifactId>geo-platform-sdk</artifactId>
        </dependency>
    </dependencies>

</project>
