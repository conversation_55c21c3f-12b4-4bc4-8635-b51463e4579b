package com.ctrip.dcs.ops.application.soa;

import com.ctrip.dcs.go.soa.server.Executors;
import com.ctrip.dcs.go.soa.server.SoaServer;
import com.ctrip.dcs.ops.application.soa.filter.OpsAuthTourConfigFilter;
import com.ctrip.model.*;
import com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType;
import com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType;
import com.ctriposs.baiji.rpc.server.CustomServerConfigurer;
import com.ctriposs.baiji.rpc.server.HostConfig;

@SoaServer
public class Server implements Dcsopsservice, CustomServerConfigurer {
    @Override
    public CheckHealthResponseType checkHealth(CheckHealthRequestType request) {
        return new CheckHealthResponseType();
    }

    @Override
    public void config(HostConfig hostConfig) {
        OpsAuthTourConfigFilter.registerFilter(hostConfig);
    }

    @Override
    public QueryResultdetailResponseType queryResultdetail(QueryResultdetailRequestType request) {
        return Executors.run(request, QueryResultdetailResponseType.class);
    }

    @Override
    public QueryCityListResponseType queryCityList(QueryCityListRequestType request) {
        return Executors.run(request, QueryCityListResponseType.class);
    }

    @Override
    public CheckCycleListResponseType checkCycleList(CheckCycleListRequestType request) {
        return Executors.run(request, CheckCycleListResponseType.class);
    }

    @Override
    public QuerySiteListResponseType querySiteList(QuerySiteListRequestType request) {
        return Executors.run(request, QuerySiteListResponseType.class);
    }

    @Override
    public QueryGuideInfoResponseType queryDayrentGuideInfo(QueryGuideInfoRequestType request) {
        return Executors.run(request, QueryGuideInfoResponseType.class);
    }

    @Override
    public HistoryScoreResponseType historyScore(HistoryScoreRequestType request) {
        return Executors.run(request, HistoryScoreResponseType.class);
    }

    @Override
    public TeamRankListResponseType teamRankList(TeamRankListRequestType request) {
        return Executors.run(request, TeamRankListResponseType.class);
    }

    @Override
    public ScoreDetailResponseType dayRentScoreDetail(ScoreDetailRequestType request) {
        return Executors.run(request, ScoreDetailResponseType.class);
    }

    @Override
    public IndexDetailResponseType dayRentIndexDetail(IndexDetailRequestType request) {
        return Executors.run(request, IndexDetailResponseType.class);
    }

    @Override
    public DriverOverviewResponseType driverOverview(DriverOverviewRequestType request) throws Exception {
        return Executors.run(request, DriverOverviewResponseType.class);
    }

    @Override
    public DriverListResponseType driverList(DriverListRequestType request) throws Exception {
        return Executors.run(request, DriverListResponseType.class);
    }

    @Override
    public DriverDetailResponseType driverDetail(DriverDetailRequestType request) throws Exception {
        return Executors.run(request, DriverDetailResponseType.class);
    }

    @Override
    public ShowDataResponseType showData(ShowDataRequestType request) throws Exception {
        return Executors.run(request, ShowDataResponseType.class);
    }

    @Override
    public QueryCircuitOverviewDataResponseType queryCircuitOverviewData(QueryCircuitOverviewDataRequestType request) throws Exception {
        return Executors.run(request, QueryCircuitOverviewDataResponseType.class);
    }

    @Override
    public QueryProblemOverviewDataResponseType queryProblemOverviewData(QueryProblemOverviewDataRequestType request) throws Exception {
        return Executors.run(request, QueryProblemOverviewDataResponseType.class);
    }

    @Override
    public QueryDrvProblemListResponseType queryDrvProblemList(QueryDrvProblemListRequestType request) throws Exception {
        return Executors.run(request, QueryDrvProblemListResponseType.class);
    }

    @Override
    public QueryOrderProblemListResponseType queryOrderProblemList(QueryOrderProblemListRequestType request) throws Exception {
        return Executors.run(request, QueryOrderProblemListResponseType.class);
    }

    @Override
    public ExportListResponseType exportList(ExportListRequestType request) throws Exception {
        return Executors.run(request, ExportListResponseType.class);
    }

}