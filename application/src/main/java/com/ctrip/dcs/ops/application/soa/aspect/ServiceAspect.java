package com.ctrip.dcs.ops.application.soa.aspect;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.ops.application.util.OpsContextHolder;
import com.ctrip.igt.HasIGTRestRequestHeader;

@Component
@Aspect
public class ServiceAspect {
    @Pointcut("execution(public * com.ctrip.model.Dcsopsservice.*(..))")
    public void cutPoint() {}

    @Before("cutPoint()")
    public void before(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length < 1) {
            return;
        }
        Object args0 = args[0];
        if (args0 instanceof HasIGTRestRequestHeader) {
            OpsContextHolder.setRequestCache((HasIGTRestRequestHeader)args0);
        }
    }

    @AfterReturning(pointcut = "cutPoint()")
    public void afterReturn() {
        OpsContextHolder.clear();
    }
}
