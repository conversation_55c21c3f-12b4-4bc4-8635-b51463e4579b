package com.ctrip.dcs.ops.application.soa.converter;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.ctrip.dcs.ops.infrastructure.value.*;
import com.ctrip.dcs.ops.service.domain.*;
import com.ctrip.model.IndexDetailResponseType;
import com.ctrip.model.QueryGuideInfoResponseType;
import com.google.common.collect.Lists;

public final class DataConvertUtil {
    public static Summary convert(SummaryDTO summaryDTO) {
        Summary summary = new Summary();
        summary.setExamScope(summaryDTO.getExamScope());
        summary.setUpdateTime(summaryDTO.getUpdateTime());
        summary.setExamStatus(summaryDTO.getExamStatus());
        summary.setRemainingDays(summaryDTO.getRemainingDays());
        summary.setOverallScore(summaryDTO.getOverallScore());
        summary.setScoreLevel(summaryDTO.getScoreLevel());
        summary.setDownloadUrl(summaryDTO.getDownloadUrl());
        return summary;
    }

    public static OpsExamResultData convert(OpsExamResultDTO opsExamResultDTO) {
        OpsExamResultData opsExamResultData = new OpsExamResultData();
        opsExamResultData.setScore(opsExamResultDTO.getScore());
        opsExamResultData.setDayToDayRatio(opsExamResultDTO.getDayToDayRatio());
        opsExamResultData.setItemName(opsExamResultDTO.getItemName());
        opsExamResultData.setItemkey(opsExamResultDTO.getItemkey());
        opsExamResultData.setParentKey(opsExamResultDTO.getParentKey());
        opsExamResultData.setDate(opsExamResultDTO.getDate());
        opsExamResultData.setHistoryList(opsExamResultDTO.getHistoryList() == null ? null : opsExamResultDTO.getHistoryList().stream().map(DataConvertUtil::convert).toList());
        return opsExamResultData;
    }

    public static OpsRatio convert(OpsRatioDTO opsRatioDTO) {
        OpsRatio opsRatio = new OpsRatio();
        opsRatio.setScore(opsRatioDTO.getScore());
        opsRatio.setDayToDayRatio(opsRatioDTO.getDayToDayRatio());
        opsRatio.setDate(opsRatioDTO.getDate());
        return opsRatio;
    }

    public static List<SiteInfo> convertSite(List<SiteInfoDTO> siteInfoDTOS) {
        return Optional.ofNullable(siteInfoDTOS).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    public static SiteInfo convert(SiteInfoDTO siteInfoDTO) {
        SiteInfo siteInfo = new SiteInfo();
        siteInfo.setCountryId(siteInfoDTO.getCountryId());
        siteInfo.setCountryName(siteInfoDTO.getCountryName());
        siteInfo.setProvinceInfoList(DataConvertUtil.convertProvince(siteInfoDTO.getProvinceList()));
        siteInfo.setDisable(siteInfoDTO.getDisable());
        return siteInfo;
    }

    public static List<ProvinceInfo> convertProvince(List<ProvinceInfoDTO> provinceInfos) {
        return Optional.ofNullable(provinceInfos).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    public static ProvinceInfo convert(ProvinceInfoDTO provinceinfo) {
        ProvinceInfo provinceInfo = new ProvinceInfo();
        provinceInfo.setProvinceId(provinceinfo.getProvinceId());
        provinceInfo.setProvinceName(provinceinfo.getProvinceName());
        provinceInfo.setCityList(DataConvertUtil.convertCity(provinceinfo.getCityDTOS()));
        provinceInfo.setDisable(provinceinfo.getDisable());
        return provinceInfo;

    }

    public static List<City> convertCity(List<CityDTO> cities) {
        return Optional.ofNullable(cities).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    public static City convert(CityDTO cityDTO) {
        City city = new City();
        city.setCityId(cityDTO.getCityId());
        city.setCityName(cityDTO.getCityName());
        city.setDisable(cityDTO.getDisable());
        return city;
    }

    public static QueryGuideInfoResponseType convert(DayRentGuideInfoDTO dayRentGuideInfoDTO) {
        if (Objects.isNull(dayRentGuideInfoDTO)) {
            return null;
        }
        QueryGuideInfoResponseType queryGuideInfoResponseType = new QueryGuideInfoResponseType();
        queryGuideInfoResponseType.setTeamName(dayRentGuideInfoDTO.getTeamName());
        queryGuideInfoResponseType.setScore(dayRentGuideInfoDTO.getScore());
        queryGuideInfoResponseType.setTotalScore(dayRentGuideInfoDTO.getTotalScore());
        queryGuideInfoResponseType.setScoreCompareToYestday(dayRentGuideInfoDTO.getScoreCompareToYestday());
        queryGuideInfoResponseType.setRank(dayRentGuideInfoDTO.getRank());
        queryGuideInfoResponseType.setTotalRank(dayRentGuideInfoDTO.getTotalRank());
        queryGuideInfoResponseType.setRankCompareToYestday(dayRentGuideInfoDTO.getRankCompareToYestday());
        queryGuideInfoResponseType.setRankInfo(DataConvertUtil.convertRankInfo(dayRentGuideInfoDTO.getRankInfo()));
        queryGuideInfoResponseType.setOptimization(dayRentGuideInfoDTO.getOptimization());
        queryGuideInfoResponseType.setIsInWork(dayRentGuideInfoDTO.getIsInWork());
        queryGuideInfoResponseType.setLogoImgUrl(dayRentGuideInfoDTO.getLogoImgUrl());
        return queryGuideInfoResponseType;
    }

    public static RankInfo convert(RankInfoDTO rankInfoDTO) {
        if (Objects.isNull(rankInfoDTO)) {
            return null;
        }
        RankInfo rankInfo = new RankInfo();
        rankInfo.setTeamName(rankInfoDTO.getTeamName());
        rankInfo.setRank(rankInfoDTO.getRank());
        rankInfo.setScore(rankInfoDTO.getScore());
        rankInfo.setRankingComparison(rankInfoDTO.getRankingComparison());
        rankInfo.setLogoImgUrl(rankInfoDTO.getLogoImgUrl());
        rankInfo.setTop(rankInfoDTO.getTop());
        rankInfo.setIsSelfTeam(rankInfoDTO.getIsSelfTeam());
        return rankInfo;
    }

    public static List<RankInfo> convertRankInfo(List<RankInfoDTO> rankInfoDTOS) {
        return Optional.ofNullable(rankInfoDTOS).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    public static List<ScoreDetail> convertScoreList(List<ScoreDetailDTO> scoreDetailDTOS) {
        return Optional.ofNullable(scoreDetailDTOS).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    private static ScoreDetail convert(ScoreDetailDTO scoreDetailDTO) {
        if (Objects.isNull(scoreDetailDTO)) {
            return null;
        }
        ScoreDetail scoreDetail = new ScoreDetail();
        scoreDetail.setItemName(scoreDetailDTO.getItemName());
        scoreDetail.setWeight(scoreDetailDTO.getWeight());
        scoreDetail.setPeriod(scoreDetailDTO.getPeriod());
        scoreDetail.setRank(scoreDetailDTO.getRank());
        scoreDetail.setScore(scoreDetailDTO.getScore());
        scoreDetail.setScoreCompareToYestday(scoreDetailDTO.getScoreCompareToYestday());
        scoreDetail.setLevel(scoreDetailDTO.getLevel());
        scoreDetail.setParentKey(scoreDetailDTO.getParentKey());
        scoreDetail.setOrder(scoreDetailDTO.getOrder());
        scoreDetail.setPreTeamScore(scoreDetailDTO.getPreTeamScore());
        scoreDetail.setMedianTeamScore(scoreDetailDTO.getMedianTeamScore());
        return scoreDetail;
    }

    public static IndexDetailResponseType convert(IndexScoreInfoDTO indexScoreInfoDTO) {
        IndexDetailResponseType indexDetailResponseType = new IndexDetailResponseType();
        indexDetailResponseType.setIndexRuleList(DataConvertUtil.convertIndexRule(indexScoreInfoDTO.getIndexRuleList()));
        indexDetailResponseType.setScore(indexScoreInfoDTO.getScore());
        indexDetailResponseType.setRank(indexScoreInfoDTO.getRank());
        indexDetailResponseType.setAbsoluteValue(indexScoreInfoDTO.getAbsoluteValue());
        indexDetailResponseType.setFactorDetails(DataConvertUtil.convertFactorDetail(indexScoreInfoDTO.getFactorDetails()));
        indexDetailResponseType.setSummary(indexScoreInfoDTO.getSummary());
        indexDetailResponseType.setBusinessGuide(DataConvertUtil.convert(indexScoreInfoDTO.getBusinessGuide()));
        indexDetailResponseType.setType(indexScoreInfoDTO.getType());
        return indexDetailResponseType;
    }

    private static BusinessGuide convert(BusinessGuideDTO businessGuideDTO) {
        if (Objects.isNull(businessGuideDTO)) {
            return null;
        }
        BusinessGuide businessGuide = new BusinessGuide();
        businessGuide.setTextValue(businessGuideDTO.getTextValue());
        businessGuide.setUrlDto(DataConvertUtil.convert(businessGuideDTO.getUrlDto()));
        businessGuide.setTableInfo(DataConvertUtil.convert(businessGuideDTO.getTableInfoDTO()));
        businessGuide.setType(businessGuideDTO.getType());
        return businessGuide;
    }

    private static TableInfo convert(TableInfoDTO tableInfoDTO) {
        TableInfo tableInfo = new TableInfo();
        tableInfo.setTableType(tableInfoDTO.getTableType());
        tableInfo.setTableList(DataConvertUtil.convertTableList(tableInfoDTO.getTableDTOS()));
        return tableInfo;
    }

    private static List<URLDTO> convert(List<URLInfoDTO> urlDto) {
        return Optional.ofNullable(urlDto).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    private static URLDTO convert(URLInfoDTO urlInfoDTO) {
        URLDTO uRLDTO = new URLDTO();
        uRLDTO.setName(urlInfoDTO.getName());
        uRLDTO.setUrl(urlInfoDTO.getUrl());
        uRLDTO.setUrlName(urlInfoDTO.getUrlName());
        return uRLDTO;
    }

    private static List<Table> convertTableList(List<TableDTO> tableList) {
        return Optional.ofNullable(tableList).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    private static Table convert(TableDTO tableInfoDTO) {
        Table tableDTO = new Table();
        tableDTO.setTagName(tableInfoDTO.getTagName());
        tableDTO.setCategory(tableInfoDTO.getCategory());
        tableDTO.setRules(tableInfoDTO.getRules());
        tableDTO.setLabelLocation(tableInfoDTO.getLabelLocation());
        return tableDTO;
    }

    private static List<FactorDetail> convertFactorDetail(List<FactorDetailDTO> factorDetails) {
        return Optional.ofNullable(factorDetails).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    private static FactorDetail convert(FactorDetailDTO factorDetailDTO) {
        FactorDetail factorDetail = new FactorDetail();
        factorDetail.setKey(factorDetailDTO.getKey());
        factorDetail.setValue(factorDetailDTO.getValue());
        return factorDetail;
    }

    private static List<IndexRule> convertIndexRule(List<IndexRuleDTO> indexRuleList) {
        return Optional.ofNullable(indexRuleList).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    private static IndexRule convert(IndexRuleDTO indexRuleDTO) {
        IndexRule indexRule = new IndexRule();
        indexRule.setIndexRank(indexRuleDTO.getIndexRank());
        indexRule.setScoreRange(indexRuleDTO.getScoreRange());
        indexRule.setLevel(indexRuleDTO.getLevel());
        return indexRule;
    }

    public static List<HistoryScoreInfo> convertHistoryScoreInfo(List<HistoryScoreInfoDTO> historyScoreInfoDTOS) {
        return Optional.ofNullable(historyScoreInfoDTOS).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    private static HistoryScoreInfo convert(HistoryScoreInfoDTO historyScoreInfoDTO) {
        HistoryScoreInfo historyScoreInfo = new HistoryScoreInfo();
        historyScoreInfo.setScore(historyScoreInfoDTO.getScore());
        historyScoreInfo.setDate(historyScoreInfoDTO.getDate());
        return historyScoreInfo;
    }

    public static List<TeamInfo> convertTeam(List<TeamInfoDTO> teamInfoDTOS) {
        return Optional.ofNullable(teamInfoDTOS).orElse(Lists.newArrayList()).stream().map(DataConvertUtil::convert).toList();
    }

    private static TeamInfo convert(TeamInfoDTO teamInfoDTO) {
        TeamInfo teamInfo = new TeamInfo();
        teamInfo.setTeamName(teamInfoDTO.getTeamName());
        teamInfo.setRank(teamInfoDTO.getRank());
        teamInfo.setIsSelfTeam(teamInfoDTO.getIsSelfTeam());
        teamInfo.setIsInWork(teamInfoDTO.getIsInWork());
        return teamInfo;
    }

    public static DriverResultData convert(DriverResultDTO driverResultDTO){
        DriverResultData driverResultData = new DriverResultData();
        driverResultData.setItemkey(driverResultDTO.getItemkey());
        driverResultData.setScore(driverResultDTO.getScore());
        driverResultData.setDayToDayRatio(driverResultDTO.getDayToDayRatio());
        driverResultData.setHistoryList(driverResultDTO.getHistoryList() == null ? null : driverResultDTO.getHistoryList().stream().map(DataConvertUtil::convert).toList());
        return driverResultData;
    }


    public static DriverSummary convert2DriverSummary(DriverDetailRespDTO driverDetailDTO) {
        DriverSummary summary = new DriverSummary();
        summary.setDriverId(driverDetailDTO.getDriverId());
        summary.setDriverLevel(driverDetailDTO.getDriverLevel());
        summary.setSopPassRate(driverDetailDTO.getSopPassRate());
        summary.setTrackPassRate(driverDetailDTO.getTrackPassRate());
        return summary;
    }

    public static DriverOpsResultData convertDriverScoreDetail(DriverDetailRespDTO.DriverScoreDetail scoreDetail) {
        if (Objects.isNull(scoreDetail)) {
            return null;
        }
        DriverOpsResultData opsResultData = new DriverOpsResultData();
        opsResultData.setScore(scoreDetail.getCurrScore());
        opsResultData.setDayToDayRatio(scoreDetail.fetchDayToDayRatio());
        opsResultData.setItemKey(scoreDetail.getItemScoreTypeEnum().getKey());
        opsResultData.setParentKey(scoreDetail.getItemScoreTypeEnum().getParentKey());
        return opsResultData;
    }
}
