package com.ctrip.dcs.ops.application.soa.executor;

import java.util.Comparator;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.go.soa.server.Validator;
import com.ctrip.dcs.ops.infrastructure.service.CheckCycleService;
import com.ctrip.model.CheckCycleListRequestType;
import com.ctrip.model.CheckCycleListResponseType;

import tour.auth.soa.session.SessionContext;

/**
 * 考核周期列表
 *
 * <AUTHOR>
 * @date 2024/10/07
 */
@Component
public class CheckCycleListExecutor implements Executor<CheckCycleListRequestType, CheckCycleListResponseType> {

    @Autowired
    private CheckCycleService checkCycleService;

    @Override
    public CheckCycleListResponseType execute(CheckCycleListRequestType requestType) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        Set<String> resultMonth = checkCycleService.queryPeriodList(requestType.getType(), partyId);
        CheckCycleListResponseType responseType = new CheckCycleListResponseType();
        if (CollectionUtils.isNotEmpty(resultMonth)) {
            responseType.setPeriodList(resultMonth.stream().sorted(Comparator.reverseOrder()).toList());
        }
        return responseType;
    }

    @Override
    public void validate(Validator<CheckCycleListRequestType> validator) {
        validator.ruleFor("type").notNull();
        validator.ruleFor("type").notEmpty();
    }
}
