package com.ctrip.dcs.ops.application.soa.executor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.go.soa.server.Validator;
import com.ctrip.dcs.ops.application.soa.converter.DataConvertUtil;
import com.ctrip.dcs.ops.application.util.LocaleUtil;
import com.ctrip.dcs.ops.infrastructure.service.DayRentIndexService;
import com.ctrip.model.IndexDetailRequestType;
import com.ctrip.model.IndexDetailResponseType;

import tour.auth.soa.session.SessionContext;

@Service
public class DayRentIndexDetailExecutor implements Executor<IndexDetailRequestType, IndexDetailResponseType> {
    @Autowired
    private DayRentIndexService dayRentIndexService;

    @Override
    public IndexDetailResponseType execute(IndexDetailRequestType requestType) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        return DataConvertUtil.convert(dayRentIndexService.queryData(Long.valueOf(partyId), requestType.getSiteId(), requestType.getIndexName(), LocaleUtil.getLocale()));
    }

    @Override
    public void validate(Validator<IndexDetailRequestType> validator) {
        validator.ruleFor("indexName").notNull();
        validator.ruleFor("indexName").notEmpty();
        validator.ruleFor("siteId").notNull();
    }
}
