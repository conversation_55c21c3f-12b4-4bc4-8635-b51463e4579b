package com.ctrip.dcs.ops.application.soa.executor;

import com.ctrip.dcs.go.soa.server.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.ops.application.soa.converter.DataConvertUtil;
import com.ctrip.dcs.ops.infrastructure.service.DayrentScoreService;
import com.ctrip.dcs.ops.infrastructure.value.OpsScoreDetailDTO;
import com.ctrip.model.ScoreDetailRequestType;
import com.ctrip.model.ScoreDetailResponseType;

import tour.auth.soa.session.SessionContext;

@Service
public class DayRentScoreDetailExecutor implements Executor<ScoreDetailRequestType, ScoreDetailResponseType> {

    @Autowired
    private DayrentScoreService dayrentScoreService;

    @Override
    public ScoreDetailResponseType execute(ScoreDetailRequestType request) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        OpsScoreDetailDTO opsScoreDetailDTO = dayrentScoreService.queryData(partyId, request.getSiteId());
        ScoreDetailResponseType scoreDetailResponseType = new ScoreDetailResponseType();
        scoreDetailResponseType.setScoreDetail(DataConvertUtil.convertScoreList(opsScoreDetailDTO.getScoreDetailDTOS()));
        scoreDetailResponseType.setUpdateTime(opsScoreDetailDTO.getUpdateTime());
        return scoreDetailResponseType;
    }

    @Override
    public void validate(Validator<ScoreDetailRequestType> validator) {
        validator.ruleFor("siteId").notNull();
    }
}
