package com.ctrip.dcs.ops.application.soa.executor;

import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.ops.application.soa.converter.DataConvertUtil;
import com.ctrip.dcs.ops.application.util.LocaleUtil;
import com.ctrip.dcs.ops.infrastructure.service.DriverService;
import com.ctrip.dcs.ops.infrastructure.value.DriverDetailQueryReq;
import com.ctrip.dcs.ops.infrastructure.value.DriverDetailRespDTO;
import com.ctrip.model.DriverDetailRequestType;
import com.ctrip.model.DriverDetailResponseType;
import com.google.common.collect.Lists;

import tour.auth.soa.session.SessionContext;

@Service
public class DriverDetailExecutor implements Executor<DriverDetailRequestType, DriverDetailResponseType> {

    @Resource
    private DriverService driverService;

    @Override
    public DriverDetailResponseType execute(DriverDetailRequestType requestType) {
        String partyId = getPartyId(); // 供应商id

        DriverDetailRespDTO driverDetailDTO = driverService.queryDriverDetail(buildParam(requestType, partyId));

        DriverDetailResponseType responseType = new DriverDetailResponseType();
        responseType.setDriverSummary(DataConvertUtil.convert2DriverSummary(driverDetailDTO));
        responseType.setResult(Optional.ofNullable(driverDetailDTO.getDriverScoreDetails()).orElse(Lists.newArrayList())
                .stream().map(DataConvertUtil::convertDriverScoreDetail).toList());
        return responseType;
    }

    public String getPartyId() {
        return SessionContext.getInstance().getUserInfo().getPartyId();
    }

    public DriverDetailQueryReq buildParam(DriverDetailRequestType requestType, String partyId) {
        DriverDetailQueryReq detailQueryReq = new DriverDetailQueryReq();
        detailQueryReq.setStartDate(requestType.getStartDate());
        detailQueryReq.setEndDate(requestType.getEndDate());
        detailQueryReq.setCityId(requestType.getCityId());
        detailQueryReq.setSupplierId(Long.valueOf(partyId));
        detailQueryReq.setDriverId(requestType.getDriverId());
        detailQueryReq.setLocale(LocaleUtil.getLocale());
        return detailQueryReq;
    }
}
