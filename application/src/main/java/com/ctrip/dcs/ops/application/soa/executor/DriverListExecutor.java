package com.ctrip.dcs.ops.application.soa.executor;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.ctrip.dcs.ops.application.util.LocaleUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.ops.infrastructure.service.DriverService;
import com.ctrip.dcs.ops.infrastructure.value.DriverListDTO;
import com.ctrip.dcs.ops.infrastructure.value.DriverListParam;
import com.ctrip.dcs.ops.infrastructure.value.DriverListResultDTO;
import com.ctrip.dcs.ops.infrastructure.value.PageDTO;
import com.ctrip.dcs.ops.service.domain.DriverListResultData;
import com.ctrip.dcs.ops.service.domain.PageResponse;
import com.ctrip.model.DriverListRequestType;
import com.ctrip.model.DriverListResponseType;
import com.google.common.collect.Lists;

import tour.auth.soa.session.SessionContext;

@Service
public class DriverListExecutor implements Executor<DriverListRequestType, DriverListResponseType> {

    @Autowired
    private DriverService driverService;

    @Override
    public DriverListResponseType execute(DriverListRequestType requestType) {
        DriverListResponseType responseType = new DriverListResponseType();

        boolean check = check(requestType);
        if (!check) {
            return responseType;
        }
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        DriverListDTO driverListDTO = driverService.queryDriverList(buildParam(requestType, partyId));
        PageDTO pageDTO = driverListDTO.getPageDTO();
        List<DriverListResultDTO> resultDTOS = driverListDTO.getResultDTOS();
        PageResponse pageResponse = new PageResponse();
        pageResponse.setPageNo(pageDTO.getPageNo());
        pageResponse.setPageSize(pageDTO.getPageSize());
        pageResponse.setTotalSize(pageDTO.getTotalSize());
        pageResponse.setTotalPages(pageDTO.getTotalPages());
        responseType.setPagination(pageResponse);
        responseType.setResult(Optional.ofNullable(resultDTOS).orElseGet(Lists::newArrayList).stream().map(DriverListExecutor::convert).toList());
        return responseType;
    }

    private boolean check(DriverListRequestType requestType) {
        return StringUtils.isNotBlank(requestType.getStartDate()) && StringUtils.isNotBlank(requestType.getEndDate()) && Objects.nonNull(requestType.getCityId());
    }

    private DriverListParam buildParam(DriverListRequestType requestType, String partyId) {
        DriverListParam driverListParam = new DriverListParam();
        driverListParam.setStartDate(requestType.getStartDate());
        driverListParam.setEndDate(requestType.getEndDate());
        driverListParam.setCityId(requestType.getCityId());
        driverListParam.setSupplierId(Long.valueOf(partyId));
        driverListParam.setDriverId(requestType.getDriverId());
        driverListParam.setPageNo(requestType.getPaginator().getPageNo());
        driverListParam.setPageSize(requestType.getPaginator().getPageSize());
        driverListParam.setSortField(Objects.isNull(requestType.getSort())|| StringUtils.isBlank(requestType.getSort().getField() ) ? "": requestType.getSort().getField());
        driverListParam.setSortOrder(Objects.isNull(requestType.getSort()) || StringUtils.isBlank(requestType.getSort().getSortOrder()) ? "" : requestType.getSort().getSortOrder());
        driverListParam.setLocale(LocaleUtil.getLocale());
        return driverListParam;
    }

    public static DriverListResultData convert(DriverListResultDTO driverListResultDTO) {
        DriverListResultData driverListResultData = new DriverListResultData();
        driverListResultData.setDriverId(driverListResultDTO.getDriverId());
        driverListResultData.setDriverName(driverListResultDTO.getDriverName());
        driverListResultData.setDriverScore(driverListResultDTO.getDriverScore());
        driverListResultData.setDriverServiceScore(driverListResultDTO.getDriverServiceScore());
        driverListResultData.setDriverLevel(driverListResultDTO.getDriverLevel());
        driverListResultData.setSopPassRate(driverListResultDTO.getSopPassRate());
        driverListResultData.setTrackPassRate(driverListResultDTO.getTrackPassRate());
        driverListResultData.setDriverActiveScore(driverListResultDTO.getDriverActiveScore());
        return driverListResultData;
    }
}
