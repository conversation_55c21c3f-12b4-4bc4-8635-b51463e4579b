package com.ctrip.dcs.ops.application.soa.executor;

import com.ctrip.dcs.ops.application.soa.converter.DataConvertUtil;
import com.ctrip.dcs.ops.infrastructure.value.DriverResultDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.ops.infrastructure.service.DriverSupplierService;
import com.ctrip.model.DriverOverviewRequestType;
import com.ctrip.model.DriverOverviewResponseType;

import tour.auth.soa.session.SessionContext;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DriverOverviewExecutor implements Executor<DriverOverviewRequestType, DriverOverviewResponseType> {

    @Autowired
    private DriverSupplierService driverSupplierService;

    @Override
    public DriverOverviewResponseType execute(DriverOverviewRequestType requestType) {
        DriverOverviewResponseType responseType = new DriverOverviewResponseType();

        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        boolean check = check(requestType.getCityId(), requestType.getStartDate(), requestType.getEndDate());
        if (!check) {
            return responseType;
        }
        List<DriverResultDTO> driverResultDTOS = driverSupplierService.querySupplierInfo(partyId, requestType.getCityId(), requestType.getStartDate(), requestType.getEndDate());
        responseType.setResultList(CollectionUtils.isNotEmpty(driverResultDTOS) ? driverResultDTOS.stream().map(DataConvertUtil::convert).collect(Collectors.toList()) : null);
        return responseType;
    }

    private boolean check(Long cityId, String startDate, String endDate) {
        return Objects.nonNull(cityId) && StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate);
    }
}
