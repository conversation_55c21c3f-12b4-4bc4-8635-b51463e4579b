package com.ctrip.dcs.ops.application.soa.executor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.go.soa.server.Validator;
import com.ctrip.dcs.luna.Log;
import com.ctrip.dcs.ops.application.util.LocaleUtil;
import com.ctrip.dcs.ops.infrastructure.service.CircuitService;
import com.ctrip.model.ExportListRequestType;
import com.ctrip.model.ExportListResponseType;

import tour.auth.soa.session.SessionContext;

@Service
public class ExportListExecutor implements Executor<ExportListRequestType, ExportListResponseType> {

    private static final Log log = Log.getInstance(ExportListExecutor.class);

    @Autowired
    private CircuitService circuitService;

    @Override
    public ExportListResponseType execute(ExportListRequestType request) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        ExportListResponseType response = new ExportListResponseType();

        try {
            // 调用CircuitService的导出方法
            String downloadUrl = circuitService.uploadOrderProblemList(partyId, request.getQueryDate(), request.getCityId(), request.getProductLine(), LocaleUtil.getLocale());
            response.setExcelUrl(downloadUrl);
            return response;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return response;
    }

    @Override
    public void validate(Validator<ExportListRequestType> validator) {
        validator.ruleFor("queryDate").notEmpty();
        validator.ruleFor("cityId").notNull();
        validator.ruleFor("productLine").notEmpty();
    }
}
