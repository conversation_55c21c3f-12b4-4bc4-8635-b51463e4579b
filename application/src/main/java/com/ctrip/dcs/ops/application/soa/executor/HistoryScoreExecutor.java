package com.ctrip.dcs.ops.application.soa.executor;

import com.ctrip.dcs.go.soa.server.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.ops.application.soa.converter.DataConvertUtil;
import com.ctrip.dcs.ops.infrastructure.service.HistoryDataService;
import com.ctrip.model.HistoryScoreRequestType;
import com.ctrip.model.HistoryScoreResponseType;

import tour.auth.soa.session.SessionContext;

@Service
public class HistoryScoreExecutor implements Executor<HistoryScoreRequestType, HistoryScoreResponseType> {
    @Autowired
    private HistoryDataService historyDataService;

    @Override
    public HistoryScoreResponseType execute(HistoryScoreRequestType requestType) {
        HistoryScoreResponseType historyScoreResponseType = new HistoryScoreResponseType();
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        historyScoreResponseType.setHistoryScoreInfoList(DataConvertUtil.convertHistoryScoreInfo(historyDataService.queryHistoryDataList(requestType.getSiteId(), Long.valueOf(partyId))));
        return historyScoreResponseType;
    }

    @Override
    public void validate(Validator<HistoryScoreRequestType> validator) {
        validator.ruleFor("productLine").notNull();
        validator.ruleFor("productLine").notEmpty();
        validator.ruleFor("siteId").notNull();
    }
}
