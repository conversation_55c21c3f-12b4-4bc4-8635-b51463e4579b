package com.ctrip.dcs.ops.application.soa.executor;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.go.soa.server.Validator;
import com.ctrip.dcs.ops.infrastructure.service.CircuitService;
import com.ctrip.dcs.ops.infrastructure.value.CircuitOverviewDTO;
import com.ctrip.model.QueryCircuitOverviewDataRequestType;
import com.ctrip.model.QueryCircuitOverviewDataResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tour.auth.soa.session.SessionContext;

@Service
public class QueryCircuitOverviewDataExecutor implements Executor<QueryCircuitOverviewDataRequestType, QueryCircuitOverviewDataResponseType> {

    @Autowired
    private CircuitService circuitService;

    @Override
    public QueryCircuitOverviewDataResponseType execute(QueryCircuitOverviewDataRequestType request) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        CircuitOverviewDTO circuitOverviewDTO = circuitService.queryCircuitOverviewData(partyId, request.getQueryDate(), request.getCityId(), request.getProductLine());
        return convertDTO(circuitOverviewDTO);
    }

    private QueryCircuitOverviewDataResponseType convertDTO(CircuitOverviewDTO circuitOverviewDTO) {
        QueryCircuitOverviewDataResponseType queryCircuitOverviewDataResponseType = new QueryCircuitOverviewDataResponseType();
        queryCircuitOverviewDataResponseType.setOrderCnt(circuitOverviewDTO.getOrderCnt());
        queryCircuitOverviewDataResponseType.setOrderCompletionCnt(circuitOverviewDTO.getOrderCompletionCnt());
        queryCircuitOverviewDataResponseType.setNoVehicleCnt(circuitOverviewDTO.getNoVehicleCnt());
        queryCircuitOverviewDataResponseType.setVehicleAndPersonNotMatchCnt(circuitOverviewDTO.getVehicleAndPersonNotMatchCnt());
        queryCircuitOverviewDataResponseType.setNotStandardizedCNt(circuitOverviewDTO.getNotStandardizedCNt());
        queryCircuitOverviewDataResponseType.setOtherCnt(circuitOverviewDTO.getOtherCnt());
        queryCircuitOverviewDataResponseType.setNoVehicleRatio(circuitOverviewDTO.getNoVehicleRatio());
        queryCircuitOverviewDataResponseType.setVehicleAndPersonNotMatchRatio(circuitOverviewDTO.getVehicleAndPersonNotMatchRatio());
        queryCircuitOverviewDataResponseType.setNotStandardizedRatio(circuitOverviewDTO.getNotStandardizedRatio());
        queryCircuitOverviewDataResponseType.setNoVehicleIsCircuited(circuitOverviewDTO.getNoVehicleIsCircuited());
        queryCircuitOverviewDataResponseType.setVehicleAndPersonNotMatchIsCircuited(circuitOverviewDTO.getVehicleAndPersonNotMatchIsCircuited());
        queryCircuitOverviewDataResponseType.setNotStandardizedIsCircuited(circuitOverviewDTO.getNotStandardizedIsCircuited());
        queryCircuitOverviewDataResponseType.setNoVehicleisWarn(circuitOverviewDTO.getNoVehicleisWarn());
        queryCircuitOverviewDataResponseType.setVehicleAndPersonNotMatchisWarn(circuitOverviewDTO.getVehicleAndPersonNotMatchisWarn());
        queryCircuitOverviewDataResponseType.setNotStandardizedisWarn(circuitOverviewDTO.getNotStandardizedisWarn());
        return queryCircuitOverviewDataResponseType;
    }

    @Override
    public void validate(Validator<QueryCircuitOverviewDataRequestType> validator) {
        validator.ruleFor("queryDate").notEmpty();
        validator.ruleFor("cityId").notNull();
        validator.ruleFor("productLine").notEmpty();
    }
}
