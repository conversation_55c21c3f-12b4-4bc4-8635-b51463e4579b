package com.ctrip.dcs.ops.application.soa.executor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.go.soa.server.Validator;
import com.ctrip.dcs.ops.application.util.LocaleUtil;
import com.ctrip.dcs.ops.infrastructure.gateway.CityGateway;
import com.ctrip.dcs.ops.infrastructure.service.QueryCityService;
import com.ctrip.dcs.ops.service.domain.City;
import com.ctrip.model.QueryCityListRequestType;
import com.ctrip.model.QueryCityListResponseType;

import tour.auth.soa.session.SessionContext;

/**
 * 查询城市列表
 */
@Component
public class QueryCityListExecutor implements Executor<QueryCityListRequestType, QueryCityListResponseType> {

    @Autowired
    private CityGateway cityGateWay;

    @Autowired
    private QueryCityService queryCityService;

    @Override

    public QueryCityListResponseType execute(QueryCityListRequestType requestType) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        Set<String> resultMonth = queryCityService.queryCityListList(requestType.getType(), partyId);
        return setCityName(resultMonth);
    }

    private QueryCityListResponseType setCityName(Set<String> resultMonth) {
        QueryCityListResponseType responseType = new QueryCityListResponseType();
        if (CollectionUtils.isNotEmpty(resultMonth)) {
            List<Long> list = resultMonth.stream().map(Long::valueOf).toList();
            Map<Long, com.ctrip.dcs.geo.domain.value.City> cityMap = cityGateWay.getCityName(list, LocaleUtil.getLocale());
            List<City> cityList = new ArrayList<>();
            if (MapUtils.isNotEmpty(cityMap)) {
                cityMap.forEach((key, value) -> {
                    City city = new City();
                    city.setCityId(key);
                    city.setCityName(value.getTranslationName());
                    cityList.add(city);
                });
            }
            responseType.setResultList(cityList);
        }
        return responseType;
    }

    @Override
    public void validate(Validator<QueryCityListRequestType> validator) {
        validator.ruleFor("type").notNull();
        validator.ruleFor("type").notEmpty();
    }
}
