package com.ctrip.dcs.ops.application.soa.executor;

import com.ctrip.dcs.go.soa.server.Validator;
import com.ctrip.dcs.ops.application.util.LocaleUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.ops.application.soa.converter.DataConvertUtil;
import com.ctrip.dcs.ops.infrastructure.service.DayRentGuideInfoService;
import com.ctrip.model.QueryGuideInfoRequestType;
import com.ctrip.model.QueryGuideInfoResponseType;

import tour.auth.soa.session.SessionContext;

@Service
public class QueryDayRentGuideInfoExecutor implements Executor<QueryGuideInfoRequestType, QueryGuideInfoResponseType> {
    @Autowired
    private DayRentGuideInfoService dayRentGuideInfoService;

    @Override
    public QueryGuideInfoResponseType execute(QueryGuideInfoRequestType request) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        return DataConvertUtil.convert(dayRentGuideInfoService.queryData(request.getSiteId(), Long.valueOf(partyId), LocaleUtil.getLocale()));
    }

    @Override
    public void validate(Validator<QueryGuideInfoRequestType> validator) {
        validator.ruleFor("siteId").notNull();
    }
}
