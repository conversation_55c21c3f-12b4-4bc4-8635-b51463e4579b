package com.ctrip.dcs.ops.application.soa.executor;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.go.soa.server.Validator;
import com.ctrip.dcs.ops.application.util.LocaleUtil;
import com.ctrip.dcs.ops.infrastructure.service.CircuitService;
import com.ctrip.dcs.ops.infrastructure.value.DriverProblemPageDTO;
import com.ctrip.dcs.ops.infrastructure.value.DriverProblemResultDTO;
import com.ctrip.dcs.ops.infrastructure.value.PageDTO;
import com.ctrip.dcs.ops.service.domain.DriverProblemDTO;
import com.ctrip.dcs.ops.service.domain.PageResponse;
import com.ctrip.model.QueryDrvProblemListRequestType;
import com.ctrip.model.QueryDrvProblemListResponseType;
import com.google.common.collect.Lists;

import tour.auth.soa.session.SessionContext;

@Service
public class QueryDrvProblemListExecutor implements Executor<QueryDrvProblemListRequestType, QueryDrvProblemListResponseType> {

    @Autowired
    private CircuitService circuitService;

    @Override
    public QueryDrvProblemListResponseType execute(QueryDrvProblemListRequestType request) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        QueryDrvProblemListResponseType responseType = new QueryDrvProblemListResponseType();
        DriverProblemPageDTO driverProblemPageDTO = circuitService.queryDriverProblemList(partyId, request.getQueryDate(), request.getCityId(), request.getProductLine(), LocaleUtil.getLocale(),
            request.getPaginator().getPageNo(), request.getPaginator().getPageSize(), Objects.nonNull(request.getSort()) && StringUtils.isNotBlank(request.getSort().getField()) ? request.getSort().getField() : "",
            Objects.nonNull(request.getSort()) && StringUtils.isNotBlank(request.getSort().getSortOrder()) ? request.getSort().getSortOrder() : "");
        responseType.setPagination(convertPage(request, driverProblemPageDTO.getPageDTO()));
        responseType.setDriverProblemList(convertData(driverProblemPageDTO.getDriverProblemResultDTOS()));
        return responseType;
    }

    private List<DriverProblemDTO> convertData(List<DriverProblemResultDTO> driverProblemResultDTOS) {
        if (CollectionUtils.isEmpty(driverProblemResultDTOS)) {
            return Lists.newArrayList();
        }
        List<DriverProblemDTO> driverProblemDTOlist = Lists.newArrayList();
        for (DriverProblemResultDTO driverProblemResultDTO : driverProblemResultDTOS) {
            driverProblemDTOlist.add(convertFromDriverProblemResultDTO(driverProblemResultDTO));
        }
        return driverProblemDTOlist;
    }

    private DriverProblemDTO convertFromDriverProblemResultDTO(DriverProblemResultDTO driverProblemResultDTO) {
        DriverProblemDTO driverProblemDTO = new DriverProblemDTO();
        driverProblemDTO.setDriverId(driverProblemResultDTO.getDriverId());
        driverProblemDTO.setDriverName(driverProblemResultDTO.getDriverName());
        driverProblemDTO.setCityName(driverProblemResultDTO.getCityName());
        driverProblemDTO.setDefectCnt(driverProblemResultDTO.getDefectCnt());
        return driverProblemDTO;
    }

    @NotNull
    private static PageResponse convertPage(QueryDrvProblemListRequestType request, PageDTO pageDTO) {
        PageResponse pageResponse = new PageResponse();
        pageResponse.setPageNo(request.getPaginator().getPageNo());
        pageResponse.setPageSize(request.getPaginator().getPageSize());
        pageResponse.setTotalSize(pageDTO.getTotalSize());
        pageResponse.setTotalPages(pageDTO.getTotalPages());
        return pageResponse;
    }

    @Override
    public void validate(Validator<QueryDrvProblemListRequestType> validator) {
        validator.ruleFor("queryDate").notEmpty();
        validator.ruleFor("cityId").notNull();
        validator.ruleFor("productLine").notEmpty();
    }
}
