package com.ctrip.dcs.ops.application.soa.executor;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.go.soa.server.Validator;
import com.ctrip.dcs.ops.application.util.LocaleUtil;
import com.ctrip.dcs.ops.infrastructure.service.CircuitService;
import com.ctrip.dcs.ops.infrastructure.value.OrderProblemPageDTO;
import com.ctrip.dcs.ops.infrastructure.value.OrderProblemResultDTO;
import com.ctrip.dcs.ops.infrastructure.value.PageDTO;
import com.ctrip.dcs.ops.service.domain.OrderProblemDTO;
import com.ctrip.dcs.ops.service.domain.PageResponse;
import com.ctrip.model.QueryOrderProblemListRequestType;
import com.ctrip.model.QueryOrderProblemListResponseType;
import com.google.common.collect.Lists;

import tour.auth.soa.session.SessionContext;

@Service
public class QueryOrderProblemListExecutor implements Executor<QueryOrderProblemListRequestType, QueryOrderProblemListResponseType> {

    @Autowired
    private CircuitService circuitService;

    @Override
    public QueryOrderProblemListResponseType execute(QueryOrderProblemListRequestType request) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        QueryOrderProblemListResponseType responseType = new QueryOrderProblemListResponseType();
        OrderProblemPageDTO orderProblemPageDTO = circuitService.queryOrderProblemList(partyId, request.getQueryDate(), request.getCityId(), request.getProductLine(), request.getDefectType(), LocaleUtil.getLocale(),
            request.getPaginator().getPageNo(), request.getPaginator().getPageSize(), Objects.nonNull(request.getSort()) && StringUtils.isNotBlank(request.getSort().getField()) ? request.getSort().getField() : "",
            Objects.nonNull(request.getSort()) && StringUtils.isNotBlank(request.getSort().getSortOrder()) ? request.getSort().getSortOrder() : "");
        responseType.setPagination(convertPage(request, orderProblemPageDTO.getPageDTO()));
        responseType.setOrderProblemList(convertData(orderProblemPageDTO.getOrderProblemResultDTOS()));
        return responseType;
    }

    @NotNull
    private static PageResponse convertPage(QueryOrderProblemListRequestType request, PageDTO pageDTO) {
        PageResponse pageResponse = new PageResponse();
        pageResponse.setPageNo(request.getPaginator().getPageNo());
        pageResponse.setPageSize(request.getPaginator().getPageSize());
        pageResponse.setTotalSize(pageDTO.getTotalSize());
        pageResponse.setTotalPages(pageDTO.getTotalPages());
        return pageResponse;
    }

    private List<OrderProblemDTO> convertData(List<OrderProblemResultDTO> orderProblemResultDTOS) {
        if (CollectionUtils.isEmpty(orderProblemResultDTOS)) {
            return Lists.newArrayList();
        }
        List<OrderProblemDTO> orderProblemDTOlist = Lists.newArrayList();
        for (OrderProblemResultDTO orderProblemResultDTO : orderProblemResultDTOS) {
            orderProblemDTOlist.add(convertFromOrderProblemResultDTO(orderProblemResultDTO));
        }
        return orderProblemDTOlist;

    }

    private OrderProblemDTO convertFromOrderProblemResultDTO(OrderProblemResultDTO orderProblemResultDTO) {
        OrderProblemDTO orderProblemDTO = new OrderProblemDTO();
        orderProblemDTO.setOrderId(orderProblemResultDTO.getOrderId());
        orderProblemDTO.setUseDate(orderProblemResultDTO.getUseDate());
        orderProblemDTO.setDriverId(orderProblemResultDTO.getDriverId());
        orderProblemDTO.setDriverName(orderProblemResultDTO.getDriverName());
        orderProblemDTO.setCityName(orderProblemResultDTO.getCityName());
        orderProblemDTO.setDefectType(orderProblemResultDTO.getDefectType());
        orderProblemDTO.setCityId(orderProblemResultDTO.getCityId());
        return orderProblemDTO;
    }

    @Override
    public void validate(Validator<QueryOrderProblemListRequestType> validator) {
        validator.ruleFor("queryDate").notEmpty();
        validator.ruleFor("cityId").notNull();
        validator.ruleFor("productLine").notEmpty();
    }
}
