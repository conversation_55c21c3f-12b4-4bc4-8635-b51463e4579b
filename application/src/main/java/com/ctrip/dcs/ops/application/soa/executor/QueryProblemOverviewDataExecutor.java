package com.ctrip.dcs.ops.application.soa.executor;

import java.util.ArrayList;
import java.util.List;

import com.ctrip.dcs.go.soa.server.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.ops.application.util.LocaleUtil;
import com.ctrip.dcs.ops.infrastructure.service.CircuitService;
import com.ctrip.dcs.ops.infrastructure.value.ProblemOverviewDTO;
import com.ctrip.dcs.ops.service.domain.Problem;
import com.ctrip.model.QueryProblemOverviewDataRequestType;
import com.ctrip.model.QueryProblemOverviewDataResponseType;
import com.google.common.collect.Lists;

import tour.auth.soa.session.SessionContext;

@Service
public class QueryProblemOverviewDataExecutor implements Executor<QueryProblemOverviewDataRequestType, QueryProblemOverviewDataResponseType> {

    @Autowired
    private CircuitService circuitService;

    @Override
    public QueryProblemOverviewDataResponseType execute(QueryProblemOverviewDataRequestType request) {
        QueryProblemOverviewDataResponseType responseType = new QueryProblemOverviewDataResponseType();
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        List<ProblemOverviewDTO> problemOverviewDTOS = circuitService.queryProblemOverviewData(partyId, request.getQueryDate(), request.getCityId(), request.getProductLine(), LocaleUtil.getLocale());
        responseType.setProblemList(convertList(problemOverviewDTOS));
        return responseType;
    }

    private List<Problem> convertList(List<ProblemOverviewDTO> problemOverviewDTOS) {
        if (CollectionUtils.isEmpty(problemOverviewDTOS)) {
            return new ArrayList<>();
        }
        List<Problem> problemlist = Lists.newArrayList();
        for (ProblemOverviewDTO problemOverviewDTO : problemOverviewDTOS) {
            problemlist.add(convertFromProblemOverviewDTO(problemOverviewDTO));
        }
        return problemlist;
    }

    private Problem convertFromProblemOverviewDTO(ProblemOverviewDTO problemOverviewDTO) {
        Problem problem = new Problem();
        problem.setProblemName(problemOverviewDTO.getProblemName());
        problem.setProblemType(problemOverviewDTO.getProblemType());
        problem.setCnt(problemOverviewDTO.getCnt());
        problem.setRatio(problemOverviewDTO.getRatio());
        return problem;
    }

    @Override
    public void validate(Validator<QueryProblemOverviewDataRequestType> validator) {
        validator.ruleFor("queryDate").notEmpty();
        validator.ruleFor("cityId").notNull();
        validator.ruleFor("productLine").notEmpty();
    }
}
