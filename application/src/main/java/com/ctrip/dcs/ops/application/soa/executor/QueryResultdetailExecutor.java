package com.ctrip.dcs.ops.application.soa.executor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.luna.Log;
import com.ctrip.dcs.ops.application.soa.converter.DataConvertUtil;
import com.ctrip.dcs.ops.infrastructure.config.OpsSourceConfig;
import com.ctrip.dcs.ops.infrastructure.constant.OpsExamPlatformEnum;
import com.ctrip.dcs.ops.infrastructure.constant.OpsExamStatusEnum;
import com.ctrip.dcs.ops.infrastructure.service.QueryOpsService;
import com.ctrip.dcs.ops.infrastructure.value.OpsExamResultDTO;
import com.ctrip.dcs.ops.infrastructure.value.SummaryDTO;
import com.ctrip.dcs.ops.service.domain.Summary;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.model.QueryResultdetailRequestType;
import com.ctrip.model.QueryResultdetailResponseType;

import lombok.SneakyThrows;
import tour.auth.soa.session.SessionContext;

@Component
public class QueryResultdetailExecutor implements Executor<QueryResultdetailRequestType, QueryResultdetailResponseType> {
    private final static Log log = Log.getInstance(QueryResultdetailExecutor.class);

    @Autowired
    private QueryOpsService queryOpsService;
    @Autowired
    private OpsSourceConfig opsSourceConfig;

    @SneakyThrows
    @Override
    public QueryResultdetailResponseType execute(QueryResultdetailRequestType requestType) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        OpsExamPlatformEnum type = OpsExamPlatformEnum.getEnum(requestType.getType());
        log.info("partyId", partyId);
        if (paramCheck(partyId, requestType)) {
            return new QueryResultdetailResponseType();
        }
        return getQueryResultdetailResponseType(requestType, type, partyId);
    }

    private boolean paramCheck(String partyId, QueryResultdetailRequestType requestType) {
        String period = requestType.getPeriod();
        Long cityId = requestType.getCityId();
        String type = requestType.getType();
        String productLine = requestType.getProductLine();
        return StringUtils.isEmpty(partyId) || StringUtils.isEmpty(period) || StringUtils.isEmpty(type) || Objects.isNull(cityId) || StringUtils.isEmpty(productLine)
            || !StringUtils.equals(ParentCategoryEnum.JNT.getCode(), productLine);
    }

    private QueryResultdetailResponseType getQueryResultdetailResponseType(QueryResultdetailRequestType requestType, OpsExamPlatformEnum type, String partyId) {
        QueryResultdetailResponseType queryResultdetailResponseType = new QueryResultdetailResponseType();
        Long cityId = requestType.getCityId();
        String period = requestType.getPeriod();
        // 判断是否在汰换表中
        if (queryOpsService.checkInUnExam(partyId, period, cityId)) {
            return buildResult(type);
        }
        Pair<SummaryDTO, List<OpsExamResultDTO>> summaryListPair = queryOpsService.getSummaryListPair(type, partyId, period, cityId);
        queryResultdetailResponseType.setSummary(DataConvertUtil.convert(summaryListPair.getLeft()));
        queryResultdetailResponseType.setOpsExamResultData(summaryListPair.getRight() == null ? new ArrayList<>() : summaryListPair.getRight().stream().map(DataConvertUtil::convert).toList());
        return queryResultdetailResponseType;
    }

    private QueryResultdetailResponseType buildResult(OpsExamPlatformEnum type) {
        QueryResultdetailResponseType queryResultdetailResponseType = new QueryResultdetailResponseType();
        Summary summary = new Summary();
        summary.setDownloadUrl(opsSourceConfig.getSourceInfo(type.getType()).getDownloadUrl());
        summary.setExamStatus(OpsExamStatusEnum.UNEXAM.getCode());
        queryResultdetailResponseType.setOpsExamResultData(new ArrayList<>());
        queryResultdetailResponseType.setSummary(summary);
        return queryResultdetailResponseType;
    }
}
