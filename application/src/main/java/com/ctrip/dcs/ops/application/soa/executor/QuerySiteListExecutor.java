package com.ctrip.dcs.ops.application.soa.executor;

import com.ctrip.dcs.go.soa.server.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.ops.application.soa.converter.DataConvertUtil;
import com.ctrip.dcs.ops.application.util.LocaleUtil;
import com.ctrip.dcs.ops.infrastructure.service.QuerySiteService;
import com.ctrip.model.QuerySiteListRequestType;
import com.ctrip.model.QuerySiteListResponseType;

import tour.auth.soa.session.SessionContext;

@Service
public class QuerySiteListExecutor implements Executor<QuerySiteListRequestType, QuerySiteListResponseType> {

    @Autowired
    private QuerySiteService querySiteService;

    @Override
    public QuerySiteListResponseType execute(QuerySiteListRequestType request) {
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        QuerySiteListResponseType response = new QuerySiteListResponseType();
        response.setSiteInfos(DataConvertUtil.convertSite(querySiteService.querySiteList(request.getProductLine(), partyId, LocaleUtil.getLocale())));
        return response;
    }

    @Override
    public void validate(Validator<QuerySiteListRequestType> validator) {
        validator.ruleFor("productLine").notNull();
        validator.ruleFor("productLine").notEmpty();
    }
}
