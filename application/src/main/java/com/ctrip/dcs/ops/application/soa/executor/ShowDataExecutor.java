package com.ctrip.dcs.ops.application.soa.executor;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.ops.infrastructure.constant.OpsExamPlatformEnum;
import com.ctrip.dcs.ops.infrastructure.service.CheckCycleService;
import com.ctrip.model.ShowDataRequestType;
import com.ctrip.model.ShowDataResponseType;

import lombok.SneakyThrows;
import tour.auth.soa.session.SessionContext;

@Service
public class ShowDataExecutor implements Executor<ShowDataRequestType, ShowDataResponseType> {

    @Autowired
    private CheckCycleService checkCycleService;

    @Autowired
    @Qualifier("showDatahreadPool")
    private ExecutorService showDatahreadPool;

    @Override
    public ShowDataResponseType execute(ShowDataRequestType requestType) {
        List<String> extracted = checkData();
        ShowDataResponseType showDataResponseType = new ShowDataResponseType();
        showDataResponseType.setData(extracted);
        return showDataResponseType;
    }

    @SneakyThrows
    private List<String> checkData() {
        List<String> list = new ArrayList<>();
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        CompletableFuture<Set<String>> setCompletableFuture = CompletableFuture.supplyAsync(() -> checkCycleService.queryPeriodList(OpsExamPlatformEnum.PLATFORM.getType(), partyId),showDatahreadPool);
        CompletableFuture<Set<String>> setCompletableFuture1 = CompletableFuture.supplyAsync(() -> checkCycleService.queryPeriodList(OpsExamPlatformEnum.TRIPCAR.getType(), partyId), showDatahreadPool);
        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(setCompletableFuture, setCompletableFuture1);
        voidCompletableFuture.get();

        Set<String> strings = setCompletableFuture.get();
        if (CollectionUtils.isNotEmpty(strings)) {
            list.add(OpsExamPlatformEnum.PLATFORM.getType());
        }

        Set<String> strings1 = setCompletableFuture1.get();
        if (CollectionUtils.isNotEmpty(strings1)) {
            list.add(OpsExamPlatformEnum.TRIPCAR.getType());
        }
        return list;
    }

}
