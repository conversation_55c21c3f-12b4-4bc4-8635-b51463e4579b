package com.ctrip.dcs.ops.application.soa.executor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.soa.server.Executor;
import com.ctrip.dcs.go.soa.server.Validator;
import com.ctrip.dcs.ops.application.soa.converter.DataConvertUtil;
import com.ctrip.dcs.ops.infrastructure.service.TeamRankService;
import com.ctrip.dcs.ops.infrastructure.value.PageDTO;
import com.ctrip.dcs.ops.infrastructure.value.TeamInfoPageDTO;
import com.ctrip.dcs.ops.service.domain.PageRequest;
import com.ctrip.dcs.ops.service.domain.PageResponse;
import com.ctrip.model.TeamRankListRequestType;
import com.ctrip.model.TeamRankListResponseType;

import tour.auth.soa.session.SessionContext;

@Service
public class TeamRankListExecutor implements Executor<TeamRankListRequestType, TeamRankListResponseType> {

    @Autowired
    private TeamRankService teamRankService;

    @Override
    public TeamRankListResponseType execute(TeamRankListRequestType teamRankListRequestType) {
        TeamRankListResponseType teamRankListResponseType = new TeamRankListResponseType();
        PageRequest paginator = teamRankListRequestType.getPaginator();
        String partyId = SessionContext.getInstance().getUserInfo().getPartyId(); // 供应商id
        TeamInfoPageDTO teamInfoPageDTO = teamRankService.queryTeamRankList(partyId, teamRankListRequestType.getSiteId(), paginator.getPageNo(), paginator.getPageSize());
        teamRankListResponseType.setDataList(DataConvertUtil.convertTeam(teamInfoPageDTO.getTeamInfoDTOS()));
        PageDTO pageDTO = teamInfoPageDTO.getPageDTO();
        PageResponse pageResponse = new PageResponse();
        pageResponse.setPageNo(paginator.getPageNo());
        pageResponse.setPageSize(paginator.getPageSize());
        pageResponse.setTotalSize(pageDTO.getTotalSize());
        pageResponse.setTotalPages(pageDTO.getTotalPages());
        teamRankListResponseType.setPagination(pageResponse);
        return teamRankListResponseType;
    }

    @Override
    public void validate(Validator<TeamRankListRequestType> validator) {
        validator.ruleFor("siteId").notNull();
        validator.ruleFor("productLine").notNull();
        validator.ruleFor("productLine").notEmpty();
    }
}
