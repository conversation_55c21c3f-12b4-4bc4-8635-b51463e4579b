package com.ctrip.dcs.ops.application.soa.filter;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;

import com.ctrip.dcs.go.soa.contract.HasRestResponseResult;
import com.ctrip.dcs.go.soa.contract.RestResponseResult;
import com.ctriposs.baiji.rpc.common.HasResponseStatus;
import com.ctriposs.baiji.rpc.common.types.AckCodeType;
import com.ctriposs.baiji.rpc.common.types.ErrorDataType;
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType;
import com.ctriposs.baiji.rpc.common.util.ServiceUtils;
import com.ctriposs.baiji.rpc.server.HostConfig;
import com.ctriposs.baiji.rpc.server.HttpRequestWrapper;
import com.ctriposs.baiji.rpc.server.HttpResponseWrapper;
import com.dianping.cat.status.ProductVersionManager;

import lombok.extern.slf4j.Slf4j;
import tour.auth.soa.filter.AuthTourConfigFilter;
import tour.auth.soa.model.authtypeprocessor.AuthTypeProcessor;
import tour.auth.soa.session.SSOClient;
import tour.auth.soa.util.PathCache;

@Slf4j
public class OpsAuthTourConfigFilter extends AuthTourConfigFilter {

    public static void registerFilter(HostConfig hostConfig) {
        OpsAuthTourConfigFilter authTourConfigFilter = new OpsAuthTourConfigFilter();
        hostConfig.addRequestFilter(authTourConfigFilter);
        hostConfig.addPostResponseFilter(authTourConfigFilter);
        ServiceUtils.registerMobileWriteBackExtensionKey(AuthTypeProcessor.REDIRECT_URL);
        PathCache.init();
        ProductVersionManager.getInstance().register("AuthTourConfigFilter.Version", SSOClient.getModuleVersion());
    }

    @Override
    public void postBeforeProcessLoginException(HttpRequestWrapper request, HttpResponseWrapper response) {
        if (!(response.responseObject() instanceof HasResponseStatus) || !(response.responseObject() instanceof HasRestResponseResult)) {
            return;
        }

        HasResponseStatus responseStatus = (HasResponseStatus)response.responseObject();
        if (responseStatus == null) {
            return;
        }

        ResponseStatusType responseStatusType = responseStatus.getResponseStatus();
        if (responseStatusType == null || responseStatusType.ack != AckCodeType.Failure || CollectionUtils.isEmpty(responseStatusType.errors)) {
            return;
        }

        for (ErrorDataType error : responseStatusType.errors) {
            log.info("Login author failed {}, {}", error.errorCode, error.getMessage());
        }

        HasRestResponseResult restates = (HasRestResponseResult)response.responseObject();
        RestResponseResult restResponseResult = new RestResponseResult();
        restResponseResult.setRcode(String.valueOf(HttpServletResponse.SC_FORBIDDEN));
        restResponseResult.setRmsg(responseStatusType.errors.get(0).getMessage());
        restates.setResstatus(restResponseResult);
    }

}
