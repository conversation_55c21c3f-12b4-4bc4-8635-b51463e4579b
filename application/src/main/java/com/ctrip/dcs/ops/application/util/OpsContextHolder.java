package com.ctrip.dcs.ops.application.util;

import com.ctrip.igt.HasIGTRestRequestHeader;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;

/**
 * 使用时，需要自己把握clear
 */
public final class OpsContextHolder {
    private static final ThreadLocal<HasIGTRestRequestHeader> requestCache = new ThreadLocal();
    private static final String GET_LOCAL_FAILED = "-1";

    public static HasIGTRestRequestHeader getRequest() {
        return requestCache.get();
    }

    public static void clear() {
        requestCache.remove();
    }

    public static void setRequestCache(HasIGTRestRequestHeader req) {
        requestCache.set(req);
    }

    public static String getLocale() {
        String locale = null;
        HasIGTRestRequestHeader request = getRequest();
        if (request != null && request.getReqhead() != null) {
            locale = request.getReqhead().getLanguage();
            if (locale == null) {
                locale = request.getReqhead().getLocale();
            }
        }
        Cat.logEvent("OpsContextHolder", "locale", locale == null ? GET_LOCAL_FAILED : Event.SUCCESS, locale);
        return locale;
    }
}
