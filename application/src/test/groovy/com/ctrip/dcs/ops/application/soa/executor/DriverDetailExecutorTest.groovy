package com.ctrip.dcs.ops.application.soa.executor


import com.ctrip.dcs.ops.infrastructure.service.DriverService
import com.ctrip.dcs.ops.infrastructure.value.DriverDetailQueryReq
import com.ctrip.dcs.ops.infrastructure.value.DriverDetailRespDTO
import com.ctrip.dcs.ops.infrastructure.value.DriverDetailRespDTO.DriverScoreDetail
import com.ctrip.model.DriverDetailRequestType
import spock.lang.Specification

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/30 17:48
 * @Description: TODO
 */
class DriverDetailExecutorTest extends Specification {
    def testObj = new DriverDetailExecutor()
    def driverService = Mock(DriverService)

    def setup() {
     testObj.driverService = driverService
    }

    def "Execute"() {
      given: "设定相关方法入参"
      and: "Mock相关接口返回"
      driverService.queryDriverDetail(_) >> new DriverDetailRespDTO(driverId:1L, sopPassRate:"66.67", driverScoreDetails: [new DriverScoreDetail(currScore:20,lastScore:30,itemScoreTypeEnum:"DRIVER_POINT"),new DriverScoreDetail(currScore:-10,lastScore:-30,itemScoreTypeEnum:"DRIVER_SOP_POINT")])

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getPartyId() >> "22"
        spy.buildParam(_,_) >> new DriverDetailQueryReq(driverId: 1L, startDate: "2024-12-29", endDate: "2024-12-29", cityId:2L)

      when:
      def result = spy.execute(req)

      then: "验证返回结果里属性值是否符合预期"
      result.getDriverSummary().getSopPassRate() == sopPassRate
      result.getResult().stream().findFirst().get().getDayToDayRatio() == driverPointRatio
      where: "表格方式验证多种分支调用场景"
      req || sopPassRate | driverPointRatio
      new DriverDetailRequestType(driverId: 1L, startDate: "2024-12-29", endDate: "2024-12-29", cityId:2L) || "66.67" | -33.33

    }
}
