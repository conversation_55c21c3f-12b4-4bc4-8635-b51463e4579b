<project>
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ctrip.dcs.ops</groupId>
        <artifactId>dcs-ops-platform</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>infrastructure</artifactId>

    <dependencies>
        <!--框架核心-->
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <!--DAL组件-->
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>dal</artifactId>
        </dependency>
        <!--HTTP组件-->
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>http</artifactId>
        </dependency>
        <!--REDIS组件-->
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>redis</artifactId>
        </dependency>
        <!--QMQ组件-->
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>qmq</artifactId>
        </dependency>
        <!--ES组件-->
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>es</artifactId>
        </dependency>
        <!--SOA客户端-->
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>soa-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.sysdev</groupId>
            <artifactId>daas-client-soa</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.geo</groupId>
            <artifactId>geo-platform-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.platform.members.geolocation</groupId>
            <artifactId>geolocationservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.platform.basesystem.vendorservice.v1</groupId>
            <artifactId>vendorservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.ibu.platform</groupId>
            <artifactId>ibu-shark-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.tour.tripservice</groupId>
            <artifactId>crm-backedservice-contract</artifactId>
        </dependency>
        <!-- EasyExcel for Excel generation -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>jakarta.activation</artifactId>
                    <groupId>com.sun.activation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>