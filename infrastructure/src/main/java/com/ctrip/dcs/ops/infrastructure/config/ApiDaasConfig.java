package com.ctrip.dcs.ops.infrastructure.config;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.ops.infrastructure.config.dto.ApiInfoDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class ApiDaasConfig {
    private static JsonConfig.ParameterizedClass parameterString = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue = JsonConfig.ParameterizedClass.of(ApiInfoDTO.class);
    private static JsonConfig.ParameterizedClass parameter = JsonConfig.ParameterizedClass.of(Map.class, parameterString, parametervalue);// Map<String, Set<String>>
    private static JsonConfig<Map<String, ApiInfoDTO>> complexTestJsonConfig = JsonConfig.get("api.daas.info.json", parameter);

    public ApiInfoDTO getApiInfo(String key) {
        return complexTestJsonConfig.current().get(key);
    }
}
