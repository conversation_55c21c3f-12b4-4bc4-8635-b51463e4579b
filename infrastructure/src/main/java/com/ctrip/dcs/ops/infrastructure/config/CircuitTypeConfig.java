package com.ctrip.dcs.ops.infrastructure.config;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.go.util.JsonUtil;
import com.google.gson.reflect.TypeToken;

import lombok.Data;
import qunar.tc.qconfig.client.spring.QMapConfig;

@Component
@Data
public class CircuitTypeConfig {

    @QMapConfig(value = "circuit.type.properties", key = "problemTypeMap")
    private String problemTypeMap;

    @QMapConfig(value = "circuit.type.properties", key = "orderProblemTypeMap")
    private String orderProblemTypeMap;

    @QMapConfig(value = "circuit.type.properties", key = "reverseProblemTypeMap")
    private String reverseProblemTypeMap;

    @QMapConfig(value = "circuit.type.properties", key = "serviceTypeMap")
    private String serviceTypeMap;

    @QMapConfig(value = "circuit.type.properties", key = "limitSize", defaultValue = "100")
    private Integer limitSize;

    public Map<String, String> getProblemTypeMap() {
        return JsonUtil.fromString(problemTypeMap, new TypeToken<Map<String, String>>() {}.getType());
    }

    public Map<String, String> getOrderProblemTypeMap() {
        return JsonUtil.fromString(orderProblemTypeMap, new TypeToken<Map<String, String>>() {}.getType());
    }

    public Map<String, String> getReverseProblemTypeMap() {
        return JsonUtil.fromString(reverseProblemTypeMap, new TypeToken<Map<String, String>>() {}.getType());

    }

    public Map<String, String> getServiceTypeMap() {
        return JsonUtil.fromString(serviceTypeMap, new TypeToken<Map<String, String>>() {}.getType());
    }

}