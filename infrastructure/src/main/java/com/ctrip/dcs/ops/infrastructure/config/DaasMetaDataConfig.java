package com.ctrip.dcs.ops.infrastructure.config;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ctrip.sysdev.daas.client.DaasClient;
import org.springframework.stereotype.Component;

import com.ctrip.daas.controller.ApiServiceClient;

import qunar.tc.qconfig.client.spring.QMapConfig;

@Component
public class DaasMetaDataConfig {
    @QMapConfig("daas_metadata.properties")
    private static Map<String, String> config = new HashMap<>();

    @QMapConfig("daas_metadata.properties")
    private void onChange(Map<String, String> map) {
        config = map;
    }

    public String getApiUrl() {
        return config.getOrDefault("apiUrl", "");
    }

    public String getFileUploadEndpoint() {
        return config.getOrDefault("fileUploadEndpoint", "");
    }

    public String getChannel() {
        return config.getOrDefault("channel", "");
    }

    public ApiServiceClient getApiServiceClientV1() {
        return ApiServiceClient.getInstance(getApiUrl());
    }

    public DaasClient getOneApiServiceClientV2(){
        return DaasClient.getInstance();
    }

    public Integer getAppId() {
        return Integer.valueOf(config.getOrDefault("appid", "100029576"));
    }

    public Boolean isLimitLastMonth() {
        return Boolean.valueOf(config.getOrDefault("limitLastMonth", "true"));
    }

    public Boolean demoteNotHiveD() {
        return Boolean.valueOf(config.getOrDefault("demoteNotHiveD", "false"));
    }

    public Integer getDaysToDemote() {
        return Integer.valueOf(config.getOrDefault("daysToDemote", "1"));
    }

    public Integer scaleInt() {
        return Integer.valueOf(config.getOrDefault("scaleInt", "1"));
    }

    public List<String> getLevelList() {
        return List.of(config.getOrDefault("levelList", "").split(","));
    }

    public String getFallbackToken() {
        return config.getOrDefault("fallbackToken", "");
    }
}
