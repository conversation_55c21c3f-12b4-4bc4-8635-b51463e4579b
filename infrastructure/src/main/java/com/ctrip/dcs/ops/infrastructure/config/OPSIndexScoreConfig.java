package com.ctrip.dcs.ops.infrastructure.config;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.ops.infrastructure.config.dto.OPSIndexScoreInfoDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class OPSIndexScoreConfig {
    private static JsonConfig.ParameterizedClass parameterString = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue = JsonConfig.ParameterizedClass.of(OPSIndexScoreInfoDTO.class);
    private static JsonConfig.ParameterizedClass parameter = JsonConfig.ParameterizedClass.of(Map.class, parameterString, parametervalue);// Map<String, Set<String>>
    private static JsonConfig<Map<String, OPSIndexScoreInfoDTO>> complexTestJsonConfig = JsonConfig.get("ops.index.config.json", parameter);

    public OPSIndexScoreInfoDTO getOPSIndexScoreInfo(String key) {
        return complexTestJsonConfig.current().get(key);
    }

    public List<String> getFieldList() {
        return complexTestJsonConfig.current().keySet().stream().toList();
    }

    public String getDBFieldMap(String key) {
        Map<String, String> map = new HashMap<>();
        complexTestJsonConfig.current().forEach((key1, value) -> map.put(value.getIndexKey(), key1));
        return map.get(key);
    }
}
