package com.ctrip.dcs.ops.infrastructure.config;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.ops.infrastructure.value.TableDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class OPSIndexTableConfig {
    private static JsonConfig.ParameterizedClass parameterString = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue = JsonConfig.ParameterizedClass.of(List.class, TableDTO.class);
    private static JsonConfig.ParameterizedClass parameter = JsonConfig.ParameterizedClass.of(Map.class, parameterString, parametervalue);// Map<String, Set<String>>
    private static JsonConfig<Map<String, List<TableDTO>>> complexTestJsonConfig = JsonConfig.get("ops.index.table.json", parameter);

    public List<TableDTO> getTableIndexName(String key) {
        return complexTestJsonConfig.current().get(key);
    }

}
