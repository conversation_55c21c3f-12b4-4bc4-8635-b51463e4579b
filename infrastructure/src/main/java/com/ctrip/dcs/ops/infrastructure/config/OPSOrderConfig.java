package com.ctrip.dcs.ops.infrastructure.config;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.ops.infrastructure.config.dto.OPSOrderDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class OPSOrderConfig {
    private static JsonConfig.ParameterizedClass parameterString = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue = JsonConfig.ParameterizedClass.of(OPSOrderDTO.class);
    private static JsonConfig.ParameterizedClass parameter = JsonConfig.ParameterizedClass.of(Map.class, parameterString, parametervalue);// Map<String, Set<String>>
    private static JsonConfig<Map<String, OPSOrderDTO>> complexTestJsonConfig = JsonConfig.get("ops.order.config.json", parameter);

    public OPSOrderDTO getOPSOrderInfo(String key) {
        return complexTestJsonConfig.current().get(key);
    }
}
