package com.ctrip.dcs.ops.infrastructure.config;

import java.util.List;

import org.springframework.stereotype.Component;

import lombok.Data;
import qunar.tc.qconfig.client.spring.QMapConfig;

@Component
@Data
public class OpsCommonConfig {
    @QMapConfig(value = "ops.rank.gray.properties", key = "totalScore", defaultValue = "5.0")
    private Double totalScore;
    @QMapConfig(value = "ops.rank.gray.properties", key = "countryDisable", defaultValue = "false")
    private Boolean countryDisable;
    @QMapConfig(value = "ops.rank.gray.properties", key = "provinceDisable", defaultValue = "false")
    private Boolean provinceDisable;
    @QMapConfig(value = "ops.rank.gray.properties", key = "cityWhiteList")
    private List<Long> cityWhiteList;
    @QMapConfig(value = "ops.rank.gray.properties", key = "cityWhiteListDisable", defaultValue = "false")
    private Boolean cityWhiteListDisable;
    @QMapConfig(value = "commonConfig.properties", key = "currentDuration")
    private String currentDuration;
    @QMapConfig(value = "commonConfig.properties", key = "daysToDemote", defaultValue = "0")
    private Integer daysToDemote;
    @QMapConfig(value = "commonConfig.properties", key = "expireSeconds", defaultValue = "129600")
    private Integer expireSeconds;
    @QMapConfig(value = "commonConfig.properties", key = "optimizationCnt", defaultValue = "3")
    private Integer optimizationCnt;
}
