package com.ctrip.dcs.ops.infrastructure.config;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.ops.infrastructure.config.dto.OpsExamFieldDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class OpsExamFieldConfig {
    private static JsonConfig.ParameterizedClass parameterString = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue = JsonConfig.ParameterizedClass.of(List.class, OpsExamFieldDTO.class);
    private static JsonConfig.ParameterizedClass parameter = JsonConfig.ParameterizedClass.of(Map.class, parameterString, parametervalue);// Map<String, List<String>>
    private static JsonConfig<Map<String, List<OpsExamFieldDTO>>> complexTestJsonConfig = JsonConfig.get("ops.exam.field.json", parameter);

    public List<OpsExamFieldDTO> getFieldList(String key) {
        return complexTestJsonConfig.current().get(key);
    }

}
