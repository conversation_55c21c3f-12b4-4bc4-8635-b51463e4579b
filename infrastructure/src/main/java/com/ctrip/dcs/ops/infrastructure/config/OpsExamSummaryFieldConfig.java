package com.ctrip.dcs.ops.infrastructure.config;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.ops.infrastructure.config.dto.OpsExamSummaryFieldDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class OpsExamSummaryFieldConfig {
    private static JsonConfig.ParameterizedClass parameterString = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue = JsonConfig.ParameterizedClass.of(OpsExamSummaryFieldDTO.class);
    private static JsonConfig.ParameterizedClass parameter = JsonConfig.ParameterizedClass.of(Map.class, parameterString, parametervalue);// Map<String, List<String>>
    private static JsonConfig<Map<String, OpsExamSummaryFieldDTO>> complexTestJsonConfig = JsonConfig.get("ops.exam.summary.field.json", parameter);

    public OpsExamSummaryFieldDTO getFieldList(String key) {
        return complexTestJsonConfig.current().get(key);
    }
}
