package com.ctrip.dcs.ops.infrastructure.config;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.ops.infrastructure.config.dto.OpsSourceDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class OpsSourceConfig {
    private static JsonConfig.ParameterizedClass parameterString = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue = JsonConfig.ParameterizedClass.of(OpsSourceDTO.class);
    private static JsonConfig.ParameterizedClass parameter = JsonConfig.ParameterizedClass.of(Map.class, parameterString, parametervalue);// Map<String, Set<String>>
    private static JsonConfig<Map<String, OpsSourceDTO>> complexTestJsonConfig = JsonConfig.get("ops.source.json", parameter);

    public OpsSourceDTO getSourceInfo(String key) {
        return complexTestJsonConfig.current().get(key);
    }
}
