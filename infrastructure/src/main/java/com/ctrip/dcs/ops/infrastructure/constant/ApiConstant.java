package com.ctrip.dcs.ops.infrastructure.constant;

public class ApiConstant {
    /**
     * 考核周期平台固化表
     */
    public static final String CHECK_CYCLE_LIST_PLATFORM_MI = "checkCycleListPlatformMi";
    /**
     * 考核周期平台临时表
     */
    public static final String CHECK_CYCLE_LIST_PLATFORM_DF = "checkCycleListPlatformDf";
    /**
     * 考核周期携程固化
     */
    public static final String CHECK_CYCLE_LIST_TRIPCAR_MI = "checkCycleListTripCarMi";
    /**
     * 考核周期携程临时表
     */
    public static final String CHECK_CYCLE_LIST_TRIPCAR_DF = "checkCycleListTripCarDf";
    /**
     * 查询城市列表平台固化
     */
    public static final String QUERY_CITY_LIST_PLATFORM_MI = "queryCityListPlatformMi";
    /**
     * 查询城市列表平台临时
     */
    public static final String QUERY_CITY_LIST_PLATFORM_DF = "queryCityListPlatformDf";
    /**
     * 查询城市列表携程固化表
     */
    public static final String QUERY_CITY_LIST_TRIPCAR_MI = "queryCityListTripCarMi";
    /**
     * 查询城市列表携程临时
     */
    public static final String QUERY_CITY_LIST_TRIPCAR_DF = "queryCityListTripCarDf";
    /**
     * 查询结果详细信息平台
     */
    public static final String QUERY_RESULT_DETAIL_PLATFORM_DF = "queryResultDetailPlatformDf";
    /**
     * 查询结果详情平台固化表
     */
    public static final String QUERY_RESULT_DETAIL_PLATFORM_MI = "queryResultDetailPlatformMi";

    /**
     * 查询结果详细信息携程
     */
    public static final String QUERY_RESULT_DETAIL_TRIPCAR_DF = "queryResultDetailTripCarDf";
    /**
     * 查询结果详情携程固化表
     */
    public static final String QUERY_RESULT_DETAIL_TRIPCAR_MI = "queryResultDetailTripCarMi";

    /**
     * 支票不可用
     */
    public static final String CHECK_IS_IN_UNEXAM = "checkIsInUnexam";

    /**
     * 查询ops站点列表
     */
    public static final String QUERY_OPS_SORT_TOTAL = "queryOPSSortTotal";

    /**
     * 查询ops排序权重
     */
    public static final String QUERY_OPS_SORT_WEIGHT = "queryopsSortWeight";
    /**
     * 查询最大日期
     */
    public static final String QUERY_MAX_DATE = "queryMaxDate";

    /**
     * 查询最大排名操作总数
     */
    public static final String QUERY_MAX_RANK_OPS_TOTAL = "queryMaxRankOPSTotal";
    /**
     * 查询ops站点列表
     */
    public static final String QUERY_OPS_SITE_LIST = "queryOPSSiteList";

    /**
     * 查询操作详细信息
     */
    public static final String QUERY_OPS_DETAIL = "queryOpsDetail";

    /**
     * 查询ops总分
     */
    public static final String QUERY_OPS_TOTAL_SCORE = "queryOPSTotalScore";

    /**
     * 查询操作详细信息最大排名
     */
    public static final String QUERY_OPS_DETAIL_MAX_RANK = "queryOPSDetailMaxRank";

    /**
     * 查询ops详细索引数据
     */
    public static final String QUERY_OPS_DETAIL_INDEX_DATA = "queryOPSDetailIndexData";
    /**
     * 按索引查询ops排序因子
     */
    public static final String QUERY_OPS_SORT_FACTOR_BY_INDEX = "queryOPSSortFactorByIndex";

    public static final String QUERY_TOTAL_DATA_BY_PAGE = "queryTotalDataByPage";
    public static final String QUERY_CNT_TOTAL = "queryCntTotal";

    public static final String QUERY_DRIVER_SCORE_BY_DRIVERID = "queryDriverScorebyDriverId";
    public static final String QUERY_DRIVER_SCORE = "queryDriverScore";
    public static final String QUERY_DRIVER_SUPPLIER_INFO = "queryDriverSupplierInfo";
    public static final String QUERY_MIN_DATE = "queryMinDate";

    public static final String QUERY_CIRCUIT_MIN_DATE = "queryCircuitMinDate";


}
