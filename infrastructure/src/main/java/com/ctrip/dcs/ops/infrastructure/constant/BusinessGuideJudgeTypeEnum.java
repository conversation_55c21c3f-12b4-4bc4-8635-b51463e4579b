package com.ctrip.dcs.ops.infrastructure.constant;

public enum BusinessGuideJudgeTypeEnum {
    /**
     * 中位数
     */
    MEDIAN("median"),
    /**
     * 选择区间
     */
    SECTION("section"),
    /**
     * 第一类表格
     */
    TABLE_FIRST("table_first"),
    /**
     * 第二类表格
     */
    TABLE_SECOND("table_second"),;

    private String type;

    public String getType() {
        return type;
    }

    BusinessGuideJudgeTypeEnum(String type) {
        this.type = type;
    }

    public static BusinessGuideJudgeTypeEnum getBusinessGuideJudgeTypeEnum(String type) {
        for (BusinessGuideJudgeTypeEnum businessGuideJudgeTypeEnum : BusinessGuideJudgeTypeEnum.values()) {
            if (businessGuideJudgeTypeEnum.getType().equals(type)) {
                return businessGuideJudgeTypeEnum;
            }
        }
        return null;
    }
}
