package com.ctrip.dcs.ops.infrastructure.constant;

public enum BusinessGuideTypeEnum {
    /**
     * 静态文本
     */
    STATICTEXT("staticText"),
    /**
     * 动态文本
     */
    DYNAMICTEXT("dynamicText"),
    /**
     * url
     */
    URL("url"),
    /**
     * 桌子
     */
    TABLE("table"),;

    private String type;

    public String getType() {
        return type;
    }

    BusinessGuideTypeEnum(String type) {
        this.type = type;
    }

    public static BusinessGuideTypeEnum getBusinessGuideTypeEnum(String type) {
        for (BusinessGuideTypeEnum businessGuideTypeEnum : BusinessGuideTypeEnum.values()) {
            if (businessGuideTypeEnum.getType().equals(type)) {
                return businessGuideTypeEnum;
            }
        }
        return null;
    }
}
