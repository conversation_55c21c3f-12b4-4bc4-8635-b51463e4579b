package com.ctrip.dcs.ops.infrastructure.constant;

public enum DataTypeSnum {
    PRERANK("preRank"), MEDIAN("median"),YESTDAY("yestday");

    private String type;

    DataTypeSnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static DataTypeSnum getDataTypeSnum(String type) {
        for (DataTypeSnum dataTypeSnum : DataTypeSnum.values()) {
            if (dataTypeSnum.getType().equals(type)) {
                return dataTypeSnum;
            }
        }
        return null;
    }
}
