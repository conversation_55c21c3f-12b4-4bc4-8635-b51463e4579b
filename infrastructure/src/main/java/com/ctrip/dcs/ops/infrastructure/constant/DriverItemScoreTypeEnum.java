package com.ctrip.dcs.ops.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

 /**
  * @Author: <EMAIL>
  * @Date: 2024/12/23 16:35
  * @Description: 司机指标维度类型枚举
  */
 @Getter
 @AllArgsConstructor
 public enum DriverItemScoreTypeEnum {

  //-------------司机服务分 维度 ---------===========
  /**
   * 司机分
   */
  DRIVER_POINT("", "servicePoint", "service_point"),
  /**
   * 司机SOP分 Field： service_good_point
   */
  DRIVER_SOP_POINT("servicePoint", "driverSopPoint", "service_good_point"),
  /**
   * 附加服务分-举牌接机完单 :pickup_card_point
   */
  DRIVER_PICKUP_CARD_POINT("servicePoint","pickupCardPoint", "pickup_card_point"),
  /**
   * 附件服务分-儿童座椅完单
   */
  DRIVER_CHILD_SEAT_POINT("servicePoint","childSeatPoint", "child_seat_point"),
  /**
   * 好评分
   */
  DRIVER_GOOD_POINT("servicePoint","goodPoint", "good_point"),
  /**
   * 一级违规分
   */
  DRIVER_COMPLAINT_L1_POINT("servicePoint", "complaintL1Point", "complaint_l1_point"),
  /**
   * 二级违规分
   */
  DRIVER_COMPLAINT_L2_POINT("servicePoint", "complaintL2Point", "complaint_l2_point"),
  /**
   * 三级违规分
   */
  DRIVER_COMPLAINT_L3_POINT("servicePoint", "complaintL3Point", "complaint_l3_point"),
  /**
   * 四级违规分
   */
  DRIVER_COMPLAINT_L4_POINT("servicePoint", "complaintL4Point", "complaint_l4_point"),

  //-------------司机活跃分 维度 ---------===========

  /**
   * 司机活跃分
   */
  ACTIVEPOINT("", "activePoint", "active_point_online"),

 /**
  * 参与1次抢单
  */
 JOIN_GRABE_POINT("activePoint", "joinGrabePoint", "join_grabe_point"),
 /**
  * 特殊时间段加分
  */
 SPECIAL_PERIOD_POINT("activePoint", "specialPeriodPoint", "special_period_point"),
 /**
  * 单日活跃分
  */
 ACTIVE_POINT_CURMONTH("activePoint", "activePointCurmonth", "active_point_curmonth"),
 /**
  * 活跃天数分
  */
 ACTIVE_DAYS_POINT("activePoint", "activeDaysPoint", "active_days_point"),

 ;

 private String parentKey;
 /**
  * itemKey
  */
 private String key;
 private String dbField;

}
