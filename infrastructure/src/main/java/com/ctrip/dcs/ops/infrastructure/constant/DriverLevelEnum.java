package com.ctrip.dcs.ops.infrastructure.constant;

public enum DriverLevelEnum {

    COMMON(100,"common"),
    BRONZ<PERSON>(101, "bronze"),
    SILVER(102, "silver"),
    GOLD(103, "gold"),
    ;


    private Integer level;
    private String name;

    DriverLevelEnum(Integer level, String name) {
        this.level = level;
        this.name = name;
    }

    public Integer getLevel() {
        return level;
    }

    public String getName() {
        return name;
    }

    // 获取枚举
    public static DriverLevelEnum getEnum(Integer level) {
        for (DriverLevelEnum e : DriverLevelEnum.values()) {
            if (e.getLevel().equals(level)) {
                return e;
            }
        }
        return null;
    }

}
