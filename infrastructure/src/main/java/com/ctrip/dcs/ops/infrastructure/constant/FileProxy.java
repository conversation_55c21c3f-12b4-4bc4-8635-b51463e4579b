package com.ctrip.dcs.ops.infrastructure.constant;

import com.ctrip.dcs.ops.infrastructure.util.Md5Utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public class FileProxy {

    public static Map<String, String> getHeaders(FileType.Type fileType, byte[] fileBytes) {
        return getHeaders(fileType, fileBytes, 0, 0);
    }

    public static Map<String, String> getHeaders(FileType.Type fileType, byte[] fileBytes, int start, int offset) {
        Map<String, String> headers = new ConcurrentHashMap<>(5);
        headers.put("Content-Type", FileType.getContentType(fileType));

        headers.put("Content-Length", fileBytes.length + ""); // If you use the URLConnection's method, please set the 'Content-Length' parameter yourself.

        headers.put("Crc", Md5Utils.compute(fileBytes));
        if (start != 0 && offset != 0) {
            long fileSize = fileBytes.length;
            headers.put("Content-Range", "bytes=" + start + "-" + (start + offset) + "/" + fileSize);
            headers.put("Accept-Ranges", "bytes");
            headers.put("Content-Length", offset + "");
        }
        return headers;
    }
}
