package com.ctrip.dcs.ops.infrastructure.constant;

import java.util.HashMap;
import java.util.Map;

public class FileType {
    private static Map<Type, String> FileTypeMap = new HashMap<>(16);
    static {
        FileTypeMap.put(Type.excel_xls, "application/vnd.ms-excel");
        FileTypeMap.put(Type.excel_xlsx, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    public static String getContentType(Type type) {
        return FileTypeMap.get(type);
    }

    public enum Type {
        excel_xls("xls"), excel_xlsx("xlsx"),;

        private String code;

        Type(String code) {
            this.code = code;
        }

        public static Type getType(String fileType) {
            for (Type type : Type.values()) {
                if (type.code.equals(fileType)) {
                    return type;
                }
            }
            throw new UnsupportedOperationException("Un supported file type: " + fileType);
        }
    }
}
