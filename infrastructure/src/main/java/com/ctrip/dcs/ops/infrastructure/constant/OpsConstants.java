package com.ctrip.dcs.ops.infrastructure.constant;

public class OpsConstants {
    public static final String PREFIX = "ops.key.rank.%s";
    public static final String CIRCUIT_PREFIX = "ops.key.rank.%s";
    public static final String SUGGEST_ONE = "suggest1";
    public static final String SUGGEST_SIX = "suggest6";
    public static final String SUGGEST_TWELVE = "suggest12";
    public static final String TOP = "top";
    public static final String MIDDLE = "middle";
    public static final String DOWN = "down";
    public static final String PERIOD1 = "period1";
    public static final String PERIOD = "period";
    public static final String OTHER = "other";

    public static String generateShark(String key) {
        return String.format(PREFIX, key);
    }

    public static String  generateCircuitShark(String key) {
        return String.format(CIRCUIT_PREFIX, key);

    }

    public static final String YES = "\u662f";

    public static final String NO = "\u5426";

}