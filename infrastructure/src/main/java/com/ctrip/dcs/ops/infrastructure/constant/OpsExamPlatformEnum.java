package com.ctrip.dcs.ops.infrastructure.constant;

/**
 * ops平台类型
 */
public enum OpsExamPlatformEnum {

    /**
     * 平台
     */
    PLATFORM("platform"),
    /**
     * 携程自营
     */
    TRIPCAR("tripcar"),

    DRIVERSCORE("driverScore"),

    CIRCUIT("circuit"),
    ;

    private String type;

    OpsExamPlatformEnum(String type) {
        this.type = type;
    }

    public static OpsExamPlatformEnum getEnum(String type) {
        for (OpsExamPlatformEnum opsExamPlatformEnum : OpsExamPlatformEnum.values()) {
            if (opsExamPlatformEnum.getType().equals(type)) {
                return opsExamPlatformEnum;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }
}
