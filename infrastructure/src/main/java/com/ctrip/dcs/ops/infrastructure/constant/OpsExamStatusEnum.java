package com.ctrip.dcs.ops.infrastructure.constant;

/**
 * 考核状态枚举
 *
 * <AUTHOR>
 * @date 2024/10/10
 */
public enum OpsExamStatusEnum {

    /**
     * 正常
     */
    NORMAL(0),
    /**
     * 考核结束
     */
    EXAMED(1),
    /**
     * 汰换下线
     */
    UNEXAM(2);

    private Integer code;

    OpsExamStatusEnum(Integer code) {
        this.code = code;
    }

    public static OpsExamStatusEnum getEnum(Integer code) {
        for (OpsExamStatusEnum statusEnum : OpsExamStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }
}
