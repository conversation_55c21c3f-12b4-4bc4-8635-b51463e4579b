package com.ctrip.dcs.ops.infrastructure.constant;

public enum OpsSiteEnum {
    COUNTRY("country"), PROVINCE("province"), CITY("city"),;

    private String value;

    OpsSiteEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static OpsSiteEnum getEnum(String value) {
        for (OpsSiteEnum opsSiteEnum : OpsSiteEnum.values()) {
            if (opsSiteEnum.getValue().equals(value)) {
                return opsSiteEnum;
            }
        }
        return null;
    }
}
