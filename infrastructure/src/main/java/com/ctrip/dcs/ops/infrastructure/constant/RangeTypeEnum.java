package com.ctrip.dcs.ops.infrastructure.constant;

public enum RangeTypeEnum {
    SECTION("section"), VALUEMATCH("valueMatch"),FIXEDSCORERANGE("fixedScoreRange");

    private String type;

    RangeTypeEnum(String type) {
        this.type = type;
    }

    public static RangeTypeEnum getRangeTypeEnum(String type) {
        for (RangeTypeEnum rangeTypeEnum : RangeTypeEnum.values()) {
            if (rangeTypeEnum.getType().equals(type)) {
                return rangeTypeEnum;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }
}
