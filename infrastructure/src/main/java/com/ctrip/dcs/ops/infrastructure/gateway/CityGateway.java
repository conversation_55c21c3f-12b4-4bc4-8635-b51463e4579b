package com.ctrip.dcs.ops.infrastructure.gateway;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;

@Component
public class CityGateway {
    @Autowired
    private CityRepository cityRepository;

    public Map<Long, City> getCityName(List<Long> cityIdList, String locale) {
        return cityRepository.findMany(cityIdList, locale);
    }
}
