package com.ctrip.dcs.ops.infrastructure.gateway;

import com.ctrip.dcs.go.soa.client.SoaClient;
import com.ctrip.tour.tripservice.crm.backedservice.contract.CrmBackedServiceClient;
import com.ctrip.tour.tripservice.crm.backedservice.contract.GetPublishAdvisorListRequestType;
import com.ctrip.tour.tripservice.crm.backedservice.contract.GetPublishAdvisorListResponseType;

@SoaClient(value = CrmBackedServiceClient.class, format = "json")
public interface CrmBackedServiceProxy {
    GetPublishAdvisorListResponseType getPublishAdvisorList(GetPublishAdvisorListRequestType request);
}
