package com.ctrip.dcs.ops.infrastructure.gateway;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.dcs.luna.Log;
import com.dianping.cat.Cat;
import com.dianping.cat.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

public abstract class DaasBaseGateway {
    private static final Log log = Log.getInstance(DaasBaseGateway.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public ArrayList<Map<String, Object>> getMaps(String apiName, Map<String, Object> param, String result) throws JsonProcessingException {
        if (StringUtils.isNotEmpty(result)) {
            ArrayList<Map<String, Object>> maps = objectMapper.readValue(result, new TypeReference<>() {});
            log.info("result", "apiName " + apiName + " param: " + JsonUtil.toString(param) + "res:" + JsonUtil.toString(maps));
            if (CollectionUtils.isEmpty(maps)) {
                Cat.logEvent("queryDataByDaas", "zeroResult");
                Cat.logEvent("dcs:queryDataByDaas" + apiName, "zeroResult");
                return new ArrayList<>();
            }
            Cat.logEvent("queryDataByDaas", "existResult");
            Cat.logEvent("dcs:queryDataByDaas" + apiName, "existResult");
            return maps;
        }
        return new ArrayList<>();
    }
}
