package com.ctrip.dcs.ops.infrastructure.gateway;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.ctrip.daas.controller.ApiRequestType;
import com.ctrip.daas.controller.ApiResponseType;
import com.ctrip.daas.controller.ApiServiceClient;
import com.ctrip.daas.controller.Head;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.dcs.luna.Log;
import com.ctrip.dcs.ops.infrastructure.config.ApiDaasConfig;
import com.ctrip.dcs.ops.infrastructure.config.DaasMetaDataConfig;
import com.ctrip.dcs.ops.infrastructure.config.dto.ApiInfoDTO;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;

@Service
public class DaasGatewayV1 extends DaasBaseGateway {
    private static final Log log = Log.getInstance(DaasGatewayV1.class);
    @Autowired
    private DaasMetaDataConfig daasMetaDataConfig;
    @Autowired
    private ApiDaasConfig apiDaasConfig;

    public List<Map<String, Object>> queryDataByDaas(String apiName, HashMap<String, Object> param) {
        ApiServiceClient client = daasMetaDataConfig.getApiServiceClientV1();
        Transaction transaction = Cat.newTransaction("queryDataByDaas", apiName);
        try {
            log.info("queryDataByDaas", "apiName:" + apiName + " param:" + JsonUtil.toString(param));
            ApiRequestType req = new ApiRequestType();
            Head head = new Head();
            head.setApiName(apiName);

            // 获取token，如果获取不到则使用兜底token
            String token = null;
            ApiInfoDTO apiInfo = apiDaasConfig.getApiInfo(apiName);
            if (apiInfo != null && apiInfo.getToken() != null && !apiInfo.getToken().trim().isEmpty()) {
                token = apiInfo.getToken();
            } else {
                token = daasMetaDataConfig.getFallbackToken();
                log.warn("queryDataByDaas", "使用兜底token，apiName:" + apiName + " fallbackToken:" + token);
            }

            head.setToken(token);
            head.setAppId(daasMetaDataConfig.getAppId());
            req.setParams(JSON.toJSONString(param));
            req.setHead(head);
            ApiResponseType res = client.invoke(req);
            log.info("res", "apiName:" + apiName + " res:" + JsonUtil.toString(res));
            if (res != null) {
                String result = res.getResult();
                return getMaps(apiName, param, result);
            }
            Cat.logEvent("queryDataByDaas", "zeroResult");
            Cat.logEvent("dcs:queryDataByDaas" + apiName, "zeroResult");
            return new ArrayList<>();
        } catch (Exception e) {
            log.warn("queryDataByDaas", e);
            log.warn("queryDataByDaasparam", "param:" + JsonUtil.toString(param));
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return new ArrayList<>();
    }

}
