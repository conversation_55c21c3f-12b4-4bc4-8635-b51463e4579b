package com.ctrip.dcs.ops.infrastructure.gateway;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.ctrip.daas.controller.DaasApiRequestType;
import com.ctrip.daas.controller.DaasApiResponseType;
import com.ctrip.daas.controller.Head;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.dcs.luna.Log;
import com.ctrip.dcs.ops.infrastructure.config.ApiDaasConfig;
import com.ctrip.dcs.ops.infrastructure.config.DaasMetaDataConfig;
import com.ctrip.dcs.ops.infrastructure.config.dto.ApiInfoDTO;
import com.ctrip.sysdev.daas.client.DaasClient;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;

@Service
public class DaasGatewayV2 extends DaasBaseGateway {
    private static final Log log = Log.getInstance(DaasGatewayV2.class);
    @Autowired
    private DaasMetaDataConfig daasMetaDataConfig;
    @Autowired
    private ApiDaasConfig apiDaasConfig;

    /**
     * 用daas查询数据
     *
     * @param apiName api名称
     * @param param   param
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    public List<Map<String, Object>> queryDataByDaas(String apiName, Map<String, Object> param) {
        DaasClient client = daasMetaDataConfig.getOneApiServiceClientV2();
        Transaction transaction = Cat.newTransaction("queryDataByDaas", apiName);
        try {
            log.info("queryDataByDaas", "apiName:" + apiName + " param:" + JsonUtil.toString(param));
            DaasApiRequestType requestType = new DaasApiRequestType();

            // 获取token，如果获取不到则使用兜底token
            String token = null;
            ApiInfoDTO apiInfo = apiDaasConfig.getApiInfo(apiName);
            if (apiInfo != null && apiInfo.getToken() != null && !apiInfo.getToken().trim().isEmpty()) {
                token = apiInfo.getToken();
            } else {
                token = daasMetaDataConfig.getFallbackToken();
                log.warn("queryDataByDaas", "使用兜底token，apiName:" + apiName + " fallbackToken:" + token);
            }

            requestType.setHead(new Head(token, daasMetaDataConfig.getAppId(), apiName));
            requestType.setParams(JSON.toJSONString(param));
            DaasApiResponseType res = client.invoke2(requestType);
            log.info("res", "apiName:" + apiName + " res:" + JsonUtil.toString(res));
            if (res != null) {
                String result = res.getData();
                return getMaps(apiName, param, result);
            }
            Cat.logEvent("queryDataByDaas", "zeroResult");
            Cat.logEvent("dcs:queryDataByDaas" + apiName, "zeroResult");
            return new ArrayList<>();
        } catch (Exception e) {
            log.warn("queryDataByDaas", e);
            log.warn("queryDataByDaasparam", "param:" + JsonUtil.toString(param));
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return new ArrayList<>();
    }
}
