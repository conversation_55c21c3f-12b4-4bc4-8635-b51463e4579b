package com.ctrip.dcs.ops.infrastructure.gateway;

import com.ctrip.basebiz.implus.vendor.contract.GetVenAgentStatusRequestType;
import com.ctrip.basebiz.implus.vendor.contract.GetVenAgentStatusResponseType;
import com.ctrip.basebiz.implus.vendor.contract.IMPlusVendorServiceClient;
import com.ctrip.dcs.go.soa.client.SoaClient;

@SoaClient(value = IMPlusVendorServiceClient.class, format = "json")
public interface IMPlusVendorServiceProxy {
    GetVenAgentStatusResponseType getVenAgentStatus(GetVenAgentStatusRequestType request);
}
