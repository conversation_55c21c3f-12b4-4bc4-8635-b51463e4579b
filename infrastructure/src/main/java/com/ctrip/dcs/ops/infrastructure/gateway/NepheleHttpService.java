package com.ctrip.dcs.ops.infrastructure.gateway;

import java.util.Base64;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.dcs.ops.infrastructure.config.DaasMetaDataConfig;
import com.ctrip.dcs.ops.infrastructure.constant.FileProxy;
import com.ctrip.dcs.ops.infrastructure.constant.FileType;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.Response;

import lombok.Data;
import lombok.SneakyThrows;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;

/**
 * <AUTHOR>
 */
@Component
public class NepheleHttpService implements NepheleGateway {

    Log log = Log.getInstance(NepheleHttpService.class);

    @Autowired
    DaasMetaDataConfig config;

    QunarAsyncClient qunarAsyncClient = new QunarAsyncClient();

    @SneakyThrows
    @Override
    public String upload(byte[] bytes, String filename) {
        if (ArrayUtils.isEmpty(bytes)) {
            return "";
        }
        String uploadUrl = String.format("%s?channel=%s&public=1&internet_host=1&oversea=1&filename=%s", config.getFileUploadEndpoint(), config.getChannel(), Base64.getUrlEncoder().encodeToString(filename.getBytes()));
        Map<String, String> headers = FileProxy.getHeaders(FileType.Type.getType(filename.substring(filename.lastIndexOf(".") + 1)), bytes);
        QHttpOption option = new QHttpOption();
        option.setPostBodyData(bytes);
        headers.forEach(option::addHeader);
        ListenableFuture<Response> result = qunarAsyncClient.post(uploadUrl, option);
        Response response = result.get();
        String responseBody = response.getResponseBody("UTF-8");
        log.info("upload_response", "url :{}, status :{} ,header:{} responce :{}", uploadUrl, response.getStatusCode(), JsonUtil.toString(response.getHeaders()), responseBody);
        FileUpLoadResponse downLoadResponse = JsonUtil.fromString(responseBody, FileUpLoadResponse.class);
        return downLoadResponse.getUrl();
    }

    @Data
    private class FileUpLoadResponse {
        String url;
    }

}
