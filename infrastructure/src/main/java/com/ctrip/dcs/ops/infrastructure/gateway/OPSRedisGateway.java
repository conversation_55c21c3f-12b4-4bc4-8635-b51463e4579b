package com.ctrip.dcs.ops.infrastructure.gateway;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.go.redis.RedisCluster;
import com.ctrip.dcs.go.redis.RedisOperation;

import lombok.extern.slf4j.Slf4j;

@Component
@RedisCluster("dcs_tms")
@Slf4j
public class OPSRedisGateway extends RedisOperation {


    public String get(String key) {
        return super.get(key);
    }

    public Boolean set(String key, String value,int expireSeconds) {
        return setex(key, expireSeconds, value);
    }
}
