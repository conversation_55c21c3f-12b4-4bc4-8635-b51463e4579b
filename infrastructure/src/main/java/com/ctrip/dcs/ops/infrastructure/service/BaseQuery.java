package com.ctrip.dcs.ops.infrastructure.service;

import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2;
import com.ctrip.dcs.ops.infrastructure.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*;

public abstract class BaseQuery {

    @Autowired
    public DaasGatewayV2 daasGatewayV2;
    public LocalDate[] convertQuertDate(String startDate, String endDate) {
        LocalDate localStartDate = DateUtil.convertStringToDate(startDate, DateUtil.YYYY_MM_DD);
        LocalDate localEndDate = DateUtil.convertStringToDate(endDate, DateUtil.YYYY_MM_DD);
        LocalDate localMinDate = DateUtil.convertStringToDate(queryMinDate(), DateUtil.YYYY_MM_DD);
        if (localMinDate.isBefore(localEndDate)) {
            long daysBetween = ChronoUnit.DAYS.between(localMinDate, localEndDate);
            LocalDate newLocalStartDate = localStartDate.minusDays(daysBetween);
            return new LocalDate[] {newLocalStartDate, localMinDate};
        }
        return new LocalDate[] {localStartDate, localEndDate};
    }

    private String queryMinDate() {
        List<Map<String, Object>> queryMinDate = daasGatewayV2.queryDataByDaas(QUERY_MIN_DATE, new HashMap<>());
        return (String)queryMinDate.stream().findFirst().get().get("minDate");
    }

    public String queryCircuitMinDate(String period) {
        List<Map<String, Object>> queryMinDate = daasGatewayV2.queryDataByDaas(QUERY_CIRCUIT_MIN_DATE, new HashMap<>());
        String minDate = (String)queryMinDate.stream().findFirst().get().get("minDate");

        LocalDate periodDateLocal = DateUtil.convertStringToDate(period, DateUtil.YYYY_MM_DD);
        LocalDate queryMinDateLocal = DateUtil.convertStringToDate(minDate, DateUtil.YYYY_MM_DD);

        // 如果传入的日期在查询日期之后，使用查询出来的日期；否则使用传入的日期
        if (periodDateLocal.isAfter(queryMinDateLocal)) {
            return minDate;
        }
        return period;
    }
}
