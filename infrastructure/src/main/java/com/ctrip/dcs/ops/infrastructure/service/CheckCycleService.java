package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.ops.infrastructure.constant.OpsExamPlatformEnum;
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV1;
import com.ctrip.dcs.ops.infrastructure.util.DateUtil;

@Service
public class CheckCycleService {

    @Autowired
    private DaasGatewayV1 daasGatewayV1;

    public Set<String> queryPeriodList(String type, String partyId) {
        OpsExamPlatformEnum platformEnum = OpsExamPlatformEnum.getEnum(type);
        Set<String> resultMonth = Set.of();
        if (platformEnum != null) {
            switch (platformEnum) {
                case PLATFORM:
                    resultMonth = queryPeriodByPlatform(partyId);
                    break;
                case TRIPCAR:
                    resultMonth = queryPeriodByTripCar(partyId);
                    break;
                default:
                    break;
            }
        }
        return resultMonth;
    }

    private Set<String> queryPeriodByPlatform(String partyId) {
        return queryPeriod(partyId, CHECK_CYCLE_LIST_PLATFORM_MI, CHECK_CYCLE_LIST_PLATFORM_DF);
    }

    private Set<String> queryPeriodByTripCar(String partyId) {
        return queryPeriod(partyId, CHECK_CYCLE_LIST_TRIPCAR_MI, CHECK_CYCLE_LIST_TRIPCAR_DF);
    }

    private Set<String> queryPeriod(String partyId, String apiNameMi, String apiNameDf) {
        // 从固化表中查询出该供应商所有的考核周期
        String lastMonth = DateUtil.getLastMonth();
        String currentMonth = DateUtil.getCurrentMonth();

        HashMap<String, Object> params = new HashMap<>();
        params.put("corpIdUse", Integer.valueOf(partyId));
        Set<String> resultMonth = new HashSet<>();
        List<Map<String, Object>> resultMiList = daasGatewayV1.queryDataByDaas(apiNameMi, params);
        List<Map<String, Object>> resultDfList = daasGatewayV1.queryDataByDaas(apiNameDf, params);
        if (CollectionUtils.isNotEmpty(resultMiList)) {
            Set<String> assessMonth = resultMiList.stream().map(item -> (String)item.get("assess_month")).collect(Collectors.toSet());
            resultMonth.addAll(assessMonth);
        }

        if (CollectionUtils.isNotEmpty(resultDfList)) {
            Set<String> assessMonth = resultDfList.stream().map(item -> (String)item.get("assess_month")).collect(Collectors.toSet());
            if (assessMonth.contains(lastMonth)) {
                resultMonth.add(lastMonth);
            }
            if (assessMonth.contains(currentMonth)) {
                resultMonth.add(currentMonth);
            }
        }
        return resultMonth;
    }

}
