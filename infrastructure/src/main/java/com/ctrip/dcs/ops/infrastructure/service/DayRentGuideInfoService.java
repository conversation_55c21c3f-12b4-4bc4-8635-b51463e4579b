
package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*;
import static com.ctrip.dcs.ops.infrastructure.constant.OpsConstants.*;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.ctrip.dcs.ops.infrastructure.util.MapUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.luna.JsonUtil;
import com.ctrip.dcs.luna.Log;
import com.ctrip.dcs.ops.infrastructure.config.OPSIndexScoreConfig;
import com.ctrip.dcs.ops.infrastructure.config.OpsCommonConfig;
import com.ctrip.dcs.ops.infrastructure.config.dto.OPSIndexScoreInfoDTO;
import com.ctrip.dcs.ops.infrastructure.constant.OpsConstants;
import com.ctrip.dcs.ops.infrastructure.gateway.CrmBackedServiceProxy;
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV1;
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2;
import com.ctrip.dcs.ops.infrastructure.util.DateUtil;
import com.ctrip.dcs.ops.infrastructure.util.MathUtil;
import com.ctrip.dcs.ops.infrastructure.util.SharkUtil;
import com.ctrip.dcs.ops.infrastructure.value.DayRentGuideInfoDTO;
import com.ctrip.dcs.ops.infrastructure.value.RankInfoDTO;
import com.ctrip.tour.tripservice.crm.backedservice.contract.GetPublishAdvisorListRequestType;
import com.ctrip.tour.tripservice.crm.backedservice.contract.GetPublishAdvisorListResponseType;
import com.ctrip.tour.tripservice.crm.backedservice.contract.dto.AdvisorDTO;
import com.ctrip.tour.tripservice.crm.backedservice.contract.dto.BasicDTO;
import com.google.common.collect.Lists;

import lombok.SneakyThrows;

@Service
public class DayRentGuideInfoService extends OPSBaseService {
    private static final Log log = Log.getInstance(DaasGatewayV2.class);

    @Autowired
    private DaasGatewayV1 daasGatewayV1;
    @Autowired
    private OpsCommonConfig opsCommonConfig;
    @Autowired
    private OPSIndexScoreConfig opsIndexScoreConfig;
    @Autowired
    private CrmBackedServiceProxy crmBackedServiceProxy;

    @Autowired
    @Qualifier("guideInfoThreadPool")
    private ExecutorService guideInfoThreadPool;

    @SneakyThrows
    public DayRentGuideInfoDTO queryData(Long siteId, Long partyId, String locale) {
        DayRentGuideInfoDTO dayRentGuideInfoDTO = new DayRentGuideInfoDTO();
        String today = getQueryDate().getLeft();
        if (StringUtils.isBlank(today)) {
            return dayRentGuideInfoDTO;
        }

        LocalDate date = DateUtil.convertStringToDate(today, DateUtil.YYYY_MM_DD);
        String before = DateUtil.getBeforeDate(date, 1);
        List<String> queryDateList = Lists.newArrayList(today, before);

        dayRentGuideInfoDTO.setTotalScore(MathUtil.doubleAccuracy(opsCommonConfig.getTotalScore()));

        HashMap<String, Object> params = new HashMap<>();
        params.put("districtId", siteId);
        params.put("vendorId", partyId);
        params.put("hiveDs", queryDateList);

        List<Map<String, Object>> opsTotalData = daasGatewayV1.queryDataByDaas(QUERY_OPS_SORT_TOTAL, params);
        // 查询出当前供应商的两天的总分结果
        if (CollectionUtils.isEmpty(opsTotalData)) {
            return dayRentGuideInfoDTO;
        }

        // 按照日期进行分组
        Map<String, Map<String, Object>> opsTotalDataByHiveDate = opsTotalData.stream().collect(Collectors.toMap(item -> (String)item.get("hive_d"), a -> a, (k1, k2) -> k2));
        // 获取查询日的数据
        Map<String, Object> queryDateData = opsTotalDataByHiveDate.get(today);
        Map<String, Object> beforeDateData = opsTotalDataByHiveDate.get(before);

        Double opsTotalScore = Double.valueOf(String.valueOf(queryDateData.get("ops_total_score")));
        Integer opsRank = (Integer)queryDateData.get("ops_rank");
        dayRentGuideInfoDTO.setTeamName((String)queryDateData.get("advisor_name"));
        dayRentGuideInfoDTO.setScore(MathUtil.doubleAccuracy(opsTotalScore));
        dayRentGuideInfoDTO.setRank(opsRank);
        Pair<List<Integer>, Integer> listIntegerPair = processRank(opsRank, siteId, today);
        List<Integer> ranks = listIntegerPair.getLeft();
        dayRentGuideInfoDTO.setTotalRank(listIntegerPair.getRight());

        if (MapUtils.isNotEmpty(beforeDateData)) {
            Double beforeOpsTotalScore = Double.valueOf(String.valueOf(beforeDateData.get("ops_total_score")));
            Integer beforeOpsRank = (Integer)beforeDateData.get("ops_rank");
            dayRentGuideInfoDTO.setScoreCompareToYestday(MathUtil.doubleAccuracy(opsTotalScore - beforeOpsTotalScore));
            dayRentGuideInfoDTO.setRankCompareToYestday(beforeOpsRank - opsRank);
        }

        List<Map<String, Object>> totalData = daasGatewayV2.queryDataByDaas(QUERY_OPS_TOTAL_SCORE, buildParam(siteId, today, ranks, opsRank));
        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> setTeamListInfo(partyId, locale, totalData, ranks, opsRank, opsTotalScore, dayRentGuideInfoDTO, String.valueOf(queryDateData.get("uid"))), guideInfoThreadPool);
        CompletableFuture<Void> voidCompletableFuture1 = CompletableFuture.runAsync(() -> setOptimization(siteId, partyId, totalData, today, opsRank, dayRentGuideInfoDTO), guideInfoThreadPool);
        CompletableFuture<Void> voidCompletableFuture3 = CompletableFuture.runAsync(() -> setWorkStatus(dayRentGuideInfoDTO, queryDateData), guideInfoThreadPool);
        CompletableFuture<Void> voidCompletableFuture2 = CompletableFuture.allOf(voidCompletableFuture, voidCompletableFuture1, voidCompletableFuture3);
        voidCompletableFuture2.get();

        return dayRentGuideInfoDTO;
    }

    private static HashMap<String, Object> buildParam(Long siteId, String today, List<Integer> ranks, Integer opsRank) {
        HashMap<String, Object> queryBuild = new HashMap<>();
        queryBuild.put("hiveD", today);
        queryBuild.put("ranks", queryRankList(ranks, opsRank));
        queryBuild.put("districtId", siteId);
        return queryBuild;
    }

    private void setWorkStatus(DayRentGuideInfoDTO dayRentGuideInfoDTO, Map<String, Object> queryDateData) {
        dayRentGuideInfoDTO.setIsInWork(setWorkStatus((String)queryDateData.get("uid")));
    }

    private void setTeamListInfo(Long partyId, String locale, List<Map<String, Object>> totalData, List<Integer> ranks, Integer opsRank, Double opsTotalScore, DayRentGuideInfoDTO dayRentGuideInfoDTO, String selfUid) {
        Map<String, String> uidImageMap = queryImageUrl(totalData);

        Map<Integer, Map<String, Object>> totalDataMap = Optional.ofNullable(totalData).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(item -> (Integer)item.get("ops_rank"), a -> a, (k1, k2) -> k2));
        List<RankInfoDTO> rankInfo = new ArrayList<>();

        ranks.forEach(rank -> {
            Map<String, Object> map = totalDataMap.get(rank);
            if (MapUtils.isNotEmpty(map)) {
                RankInfoDTO rankInfoDTO = new RankInfoDTO();
                rankInfoDTO.setTeamName(MapUtil.getKeyAsString(map, "advisor_name"));
                rankInfoDTO.setRank(MapUtil.getKeyAsInteger(map, "ops_rank"));
                rankInfoDTO.setScore(MapUtil.getKeyAsDouble(map, "ops_total_score"));
                Long vendorId = MapUtil.getKeyAsLong(map,"vendor_id");
                String uid = MapUtil.getKeyAsString(map, "uid");
                String logoUrl = uidImageMap.get(uid);

                if (Objects.equals(rank, opsRank) && vendorId.equals(partyId)) {
                    rankInfoDTO.setIsSelfTeam(true);
                    if (opsRank <= 6) {
                        rankInfoDTO.setTop("TOP6");
                    } else if (opsRank <= 12) {
                        rankInfoDTO.setTop("TOP12");
                    }
                    rankInfoDTO.setRankingComparison(processComparison(totalDataMap, opsRank, opsTotalScore, locale));
                }
                rankInfoDTO.setLogoImgUrl(Objects.nonNull(logoUrl) ? logoUrl : "");
                rankInfo.add(rankInfoDTO);
            }
        });
        dayRentGuideInfoDTO.setRankInfo(rankInfo);
        String logoUrl = uidImageMap.get(selfUid);
        dayRentGuideInfoDTO.setLogoImgUrl(Objects.nonNull(logoUrl) ? logoUrl : "");
    }

    private void setOptimization(Long siteId, Long partyId, List<Map<String, Object>> totalData, String today, Integer opsRank, DayRentGuideInfoDTO dayRentGuideInfoDTO) {
        Map<Integer, Map<String, Object>> totalDataMap = Optional.ofNullable(totalData).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(item ->MapUtil.getKeyAsInteger(item,"ops_rank"), a -> a, (k1, k2) -> k2));
        HashMap<String, Object> params1 = new HashMap<>();
        params1.put("hiveD", today);
        List<Map<String, Object>> weightIndex = daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_WEIGHT, params1);
        Map<String, Double> weightScoreMap = weightIndex.stream().collect(Collectors.toMap(item -> MapUtil.getKeyAsString(item,"index_id_l3"), item -> MapUtil.getKeyAsDouble(item,"weight"), (k1, k2) -> k2));

        List<Long> vendorIdS = new ArrayList<>();
        vendorIdS.add(partyId);
        Map<String, Object> compareMap = totalDataMap.get(opsRank == 1 ? opsRank + 1 : opsRank - 1);
        if (MapUtils.isEmpty(compareMap)) {
            return;
        }
        Long otherVendorId = MapUtil.getKeyAsLong(compareMap,"vendor_id");
        vendorIdS.add(otherVendorId);

        Map<Long, List<Map<String, Object>>> vendorId = Optional.ofNullable(daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL, buildParam(siteId, today, vendorIdS))).orElse(Lists.newArrayList()).stream()
            .collect(Collectors.groupingBy(item -> MapUtil.getKeyAsLong(item,"vendor_id")));
        List<Map<String, Object>> selfMapList = vendorId.get(partyId);
        List<Map<String, Object>> otherMapList = vendorId.get(otherVendorId);
        Map<String, Double> selfMap =
            Optional.ofNullable(selfMapList).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(item -> MapUtil.getKeyAsString(item,"index_id_l3"), item -> MapUtil.getKeyAsDouble(item,"index_score_l3"), (k1, k2) -> k2));
        Map<String, Double> otherMap =
            Optional.ofNullable(otherMapList).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(item -> MapUtil.getKeyAsString(item,"index_id_l3"), item -> MapUtil.getKeyAsDouble(item,"index_score_l3"), (k1, k2) -> k2));

        Map<String, Double> scoreResultMap = new HashMap<>();
        opsIndexScoreConfig.getFieldList().forEach(field -> {
            Double selfScore = selfMap.get(field);
            Double otherScore = otherMap.get(field);
            Double weight = weightScoreMap.get(field);
            if (Objects.nonNull(selfScore) && Objects.nonNull(otherScore) && Objects.nonNull(weight)) {
                double v = weight * (otherScore - selfScore);
                scoreResultMap.put(field, v);
            }
        });

        Map<String, Double> removeMap = new HashMap<>();
        removeMap.put("sale_30d", scoreResultMap.get("sale_30d"));
        removeMap.put("atv_90d", scoreResultMap.get("atv_90d"));
        removeMap.put("order_30d", scoreResultMap.get("order_30d"));
        scoreResultMap.remove("sale_30d");
        scoreResultMap.remove("atv_90d");
        scoreResultMap.remove("order_30d");

        // 根据值进行排序
        Map<String, Double> sortedMap = scoreResultMap.entrySet().stream().filter(entry -> entry.getValue() > 0).sorted(Map.Entry.<String, Double>comparingByValue().reversed()) // 按值排序
            .limit(opsCommonConfig.getOptimizationCnt()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, // 处理键冲突
                LinkedHashMap::new // 保持插入顺序
            ));
        log.info("sortedMap", JsonUtil.serialize(sortedMap));

        // 根据值进行排序
        Map<String, Double> removeSortedMap = removeMap.entrySet().stream().filter(entry -> entry.getValue() > 0).sorted(Map.Entry.<String, Double>comparingByValue().reversed()) // 按值排序
            .limit(1).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, // 处理键冲突
                LinkedHashMap::new // 保持插入顺序
            ));
        log.info("removeSortedMap", JsonUtil.serialize(removeSortedMap));

        Double slalesScore;
        if (MapUtils.isNotEmpty(removeSortedMap)) {
            slalesScore = removeSortedMap.values().stream().toList().stream().findFirst().get();
        } else {
            slalesScore = 0.0;
        }
        List<String> list = new ArrayList<>();
        if (MapUtils.isNotEmpty(sortedMap)) {
            list.addAll(sortedMap.keySet().stream().toList());
            if (list.size() < opsCommonConfig.getOptimizationCnt() && MapUtils.isNotEmpty(removeSortedMap)) {
                list.addAll(removeSortedMap.keySet().stream().toList());
            } else {
                AtomicReference<Boolean> flag = new AtomicReference<>(false);
                sortedMap.forEach((key, value) -> {
                    if (slalesScore > value && BooleanUtils.isFalse(flag.get())) {
                        if (MapUtils.isNotEmpty(removeSortedMap)) {
                            list.addAll(removeSortedMap.keySet().stream().toList());
                            flag.set(true);
                        }
                    }
                });
            }
        } else {
            if (MapUtils.isNotEmpty(removeSortedMap)) {
                list.addAll(removeSortedMap.keySet().stream().toList());
            }
        }
        dayRentGuideInfoDTO.setOptimization(convert(list));
    }

    private static HashMap<String, Object> buildParam(Long siteId, String today, List<Long> vendorIdS) {
        HashMap<String, Object> queryDetailParam = new HashMap<>();
        queryDetailParam.put("siteId", siteId);
        queryDetailParam.put("vendorId", vendorIdS);
        queryDetailParam.put("hiveD", today);
        return queryDetailParam;
    }

    private Map<String, String> queryImageUrl(List<Map<String, Object>> queriedDataByDaas1) {
        List<String> uidList = Optional.ofNullable(queriedDataByDaas1).orElse(Lists.newArrayList()).stream().map(item -> String.valueOf(item.get("uid"))).toList();
        GetPublishAdvisorListRequestType requestType = new GetPublishAdvisorListRequestType();
        requestType.setUids(uidList);
        Map<String, String> result = new HashMap<>();
        GetPublishAdvisorListResponseType publishAdvisorList = crmBackedServiceProxy.getPublishAdvisorList(requestType);
        List<AdvisorDTO> items = publishAdvisorList.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            items.forEach(item -> {
                BasicDTO basic = item.getBasic();
                String uid = basic.getUid();
                String coverImgUrl = basic.getLogoImgUrl();
                result.put(uid, coverImgUrl);
            });
        }
        return result;
    }

    private static List<Integer> queryRankList(List<Integer> ranks, Integer opsRank) {
        List<Integer> allRank = new ArrayList<>(ranks);
        if (opsRank <= 6) {
            allRank.add(1);
        } else if (opsRank <= 12) {
            allRank.add(6);
        } else {
            allRank.add(12);
        }
        return allRank.stream().distinct().toList();
    }

    private List<String> convert(List<String> list) {
        return list.stream().map(item -> {
            OPSIndexScoreInfoDTO opsIndexScoreInfo = opsIndexScoreConfig.getOPSIndexScoreInfo(item);
            return opsIndexScoreInfo.getIndexKey();
        }).toList();
    }

    private String processComparison(Map<Integer, Map<String, Object>> rankMap, Integer opsRank, Double opsTotalScore, String locale) {
        if (opsRank == 1) {
            return StringUtils.EMPTY;
        } else if (opsRank <= 6) {
            Map<String, Object> scoreLastRank = rankMap.get(opsRank - 1);
            Map<String, Object> scoreFirst = rankMap.get(1);
            return getScore(opsTotalScore, locale, scoreLastRank, scoreFirst, SUGGEST_ONE);
        } else if (opsRank <= 12) {
            Map<String, Object> scoreLastRank = rankMap.get(opsRank - 1);
            Map<String, Object> scoreSix = rankMap.get(6);
            return getScore(opsTotalScore, locale, scoreLastRank, scoreSix, SUGGEST_SIX);
        } else {
            Map<String, Object> scoreLastRank = rankMap.get(opsRank - 1);
            Map<String, Object> scoreTwelve = rankMap.get(12);
            return getScore(opsTotalScore, locale, scoreLastRank, scoreTwelve, SUGGEST_TWELVE);
        }
    }
    private String getScore(Double opsTotalScore, String locale, Map<String, Object> scoreLastRank, Map<String, Object> scoreFirst, String suggestOne) {
        Double lastRank = MapUtil.getKeyAsDouble(scoreLastRank, "ops_total_score");
        Double first = MapUtil.getKeyAsDouble(scoreFirst, "ops_total_score");
        return String.format(SharkUtil.get(OpsConstants.generateShark(suggestOne), locale, SUGGEST_ONE), MathUtil.doubleAccuracy(lastRank - opsTotalScore), MathUtil.doubleAccuracy(first - opsTotalScore));
    }

    private Pair<List<Integer>, Integer> processRank(Integer opsRank, Long siteId, String queryDate) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("districtId", siteId);
        params.put("hiveD", queryDate);
        List<Map<String, Object>> queriedDataByDaas = daasGatewayV2.queryDataByDaas(QUERY_MAX_RANK_OPS_TOTAL, params);
        Integer maxRank = (Integer)queriedDataByDaas.stream().findFirst().get().get("result");
        if (opsRank == 1) {
            return Pair.of(Lists.newArrayList(opsRank, opsRank + 1, opsRank + 2), maxRank);
        }
        if (Objects.equals(maxRank, opsRank)) {
            return Pair.of(Lists.newArrayList(opsRank - 2, opsRank - 1, opsRank), maxRank);
        }
        return Pair.of(Lists.newArrayList(opsRank - 1, opsRank, opsRank + 1), maxRank);
    }
}
