package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*;
import static com.ctrip.dcs.ops.infrastructure.constant.OpsConstants.OTHER;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.ctrip.dcs.ops.infrastructure.util.MapUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.dcs.ops.infrastructure.config.OPSIndexScoreConfig;
import com.ctrip.dcs.ops.infrastructure.config.OPSIndexTableConfig;
import com.ctrip.dcs.ops.infrastructure.config.OpsCommonConfig;
import com.ctrip.dcs.ops.infrastructure.config.dto.*;
import com.ctrip.dcs.ops.infrastructure.constant.BusinessGuideJudgeTypeEnum;
import com.ctrip.dcs.ops.infrastructure.constant.BusinessGuideTypeEnum;
import com.ctrip.dcs.ops.infrastructure.constant.OpsConstants;
import com.ctrip.dcs.ops.infrastructure.constant.RangeTypeEnum;
import com.ctrip.dcs.ops.infrastructure.gateway.OPSRedisGateway;
import com.ctrip.dcs.ops.infrastructure.service.indexhandler.IndexHandler;
import com.ctrip.dcs.ops.infrastructure.util.DateUtil;
import com.ctrip.dcs.ops.infrastructure.util.MathUtil;
import com.ctrip.dcs.ops.infrastructure.util.SharkUtil;
import com.ctrip.dcs.ops.infrastructure.value.*;

import lombok.SneakyThrows;

@Service
public class DayRentIndexService extends OPSBaseService {

    @Autowired
    private OPSIndexScoreConfig opsIndexScoreConfig;
    @Autowired
    private OPSRedisGateway opsRedisGateway;
    @Autowired
    private OPSIndexTableConfig opsIndexTableConfig;
    @Autowired
    private OpsCommonConfig opsCommonConfig;
    @Autowired
    @Qualifier("scoreIndexThreadPool")
    private ExecutorService scoreIndexThreadPool;
    @Autowired
    private List<IndexHandler> indexHandlerList;

    private final static String key = "ops_%s_%s_%s_%s";

    @SneakyThrows
    public IndexScoreInfoDTO queryData(Long partyId, Long siteId, String indexName, String locale) {
        IndexScoreInfoDTO indexScoreInfoDTO = new IndexScoreInfoDTO();

        String queryDate = getQueryDate().getLeft();
        if (StringUtils.isBlank(queryDate)) {
            return indexScoreInfoDTO;
        }

        // 查询详情表信息
        List<Map<String, Object>> opsDetailData = daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, buildDetailIndexParam(partyId, siteId, indexName, queryDate, null));
        if (CollectionUtils.isEmpty(opsDetailData)) {
            return indexScoreInfoDTO;
        }
        Map<String, Object> detailData = opsDetailData.stream().findFirst().get();
        Double index_pre_abs_l3 = MapUtil.getKeyAsDouble(detailData, "index_pre_abs_l3");
        Integer rank = MapUtil.getKeyAsInteger(detailData, "ops_rank");
        OPSIndexScoreInfoDTO opsIndexScoreInfo = opsIndexScoreConfig.getOPSIndexScoreInfo(opsIndexScoreConfig.getDBFieldMap(indexName));

        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
            setFactor(partyId, siteId, locale, detailData, queryDate, indexScoreInfoDTO);
        }, scoreIndexThreadPool);
        CompletableFuture<Void> voidCompletableFuture1 = CompletableFuture.runAsync(() -> {
            setSummary(indexName, locale, queryDate, indexScoreInfoDTO);
        }, scoreIndexThreadPool);
        CompletableFuture<Void> voidCompletableFuture2 = CompletableFuture.runAsync(() -> {
            setIndexRule(siteId, indexName, locale, opsIndexScoreInfo, queryDate, indexScoreInfoDTO);
        }, scoreIndexThreadPool);
        CompletableFuture<Void> voidCompletableFuture3 = CompletableFuture.runAsync(() -> {
            setBussinessGuide(partyId, siteId, indexName, locale, indexScoreInfoDTO, opsIndexScoreInfo, queryDate, rank, index_pre_abs_l3);
        }, scoreIndexThreadPool);

        CompletableFuture<Void> voidCompletableFuture4 = CompletableFuture.allOf(voidCompletableFuture, voidCompletableFuture1, voidCompletableFuture2, voidCompletableFuture3);
        voidCompletableFuture4.get();

        indexScoreInfoDTO.setScore(MathUtil.doubleAccuracy(MapUtil.getKeyAsDouble(detailData,"index_score_l3")));
        indexScoreInfoDTO.setRank(rank);
        indexScoreInfoDTO.setAbsoluteValue(MathUtil.doubleAccuracy(index_pre_abs_l3));
        return indexScoreInfoDTO;
    }

    private void setIndexRule(Long siteId, String indexName, String locale, OPSIndexScoreInfoDTO opsIndexScoreInfo, String queryDate, IndexScoreInfoDTO indexScoreInfoDTO) {
        RangeTypeInfoDTO rangeTypeInfo = opsIndexScoreInfo.getRangeTypeInfo();
        if (Objects.nonNull(rangeTypeInfo)) {
            List<IndexRuleDTO> indexRuleDTOS = new ArrayList<>();
            String rangeType = rangeTypeInfo.getRangeType();
            List<RangeDTO> rangeList = rangeTypeInfo.getRange();
            RangeTypeEnum rangeTypeEnum = RangeTypeEnum.getRangeTypeEnum(rangeType);
            switch (rangeTypeEnum) {
                case SECTION:
                    processSection(siteId, indexName, rangeList, queryDate, indexRuleDTOS, locale);
                    break;
                case VALUEMATCH:
                    processValueMatch(rangeList, indexRuleDTOS, locale);
                    break;
                case FIXEDSCORERANGE:
                    processFixedScoreRange(rangeList, indexRuleDTOS, locale);
                default:
                    break;
            }
            indexScoreInfoDTO.setIndexRuleList(indexRuleDTOS);
        }
    }

    private void processFixedScoreRange(List<RangeDTO> rangeList, List<IndexRuleDTO> indexRuleDTOS, String locale) {
        rangeList.forEach(range -> {
            StringBuffer stringBuffer = new StringBuffer();
            IndexRuleDTO indexRuleDTO = new IndexRuleDTO();
            indexRuleDTO.setLevel(SharkUtil.get(OpsConstants.generateShark(range.getLevel()), locale, range.getLevel()));
            processScoreRange(range, Double.valueOf(range.getScoreLeft()), Double.valueOf(range.getScoreRight()), stringBuffer);
            indexRuleDTO.setScoreRange(stringBuffer.toString());
            indexRuleDTOS.add(indexRuleDTO);
        });
    }

    private void setBussinessGuide(Long partyId, Long siteId, String indexName, String locale, IndexScoreInfoDTO indexScoreInfoDTO, OPSIndexScoreInfoDTO opsIndexScoreInfo, String queryDate, Integer rank,
        Double index_pre_abs_l3) {
        indexScoreInfoDTO.setType(opsIndexScoreInfo.getIndexType());
        StringBuffer sb = new StringBuffer();
        List<BussinessGuideDTO> businessGuide = opsIndexScoreInfo.getBusinessGuide();
        List<URLInfoDTO> urlInfoDTOS = new ArrayList<>();
        TableInfoDTO tableInfoDTO = new TableInfoDTO();

        if (CollectionUtils.isNotEmpty(businessGuide)) {
            businessGuide.forEach(item -> {
                String businessGuideType = item.getBusinessGuideType();
                BusinessGuideTypeEnum businessGuideTypeEnum = BusinessGuideTypeEnum.getBusinessGuideTypeEnum(businessGuideType);
                BusinessGuideJudgeTypeEnum businessGuideJudgeTypeEnum = BusinessGuideJudgeTypeEnum.getBusinessGuideJudgeTypeEnum(item.getBusinessGuideJudgeType());
                switch (businessGuideTypeEnum) {
                    case STATICTEXT:
                        processStaticText(locale, item, sb);
                        break;
                    case DYNAMICTEXT:
                        processDynamicText(partyId, siteId, indexName, locale, queryDate, rank, index_pre_abs_l3, item, businessGuideJudgeTypeEnum, sb);
                        break;
                    case URL:
                        processUrl(locale, item, urlInfoDTOS);
                        break;
                    case TABLE:
                        processTable(indexName, tableInfoDTO, businessGuideJudgeTypeEnum);
                        break;
                    default:
                        break;
                }
            });
        }
        BusinessGuideDTO businessGuideDTO = new BusinessGuideDTO();
        businessGuideDTO.setTextValue(sb.toString());
        businessGuideDTO.setUrlDto(urlInfoDTOS);
        businessGuideDTO.setType(opsIndexScoreInfo.getBusinessGuideComponentType());
        businessGuideDTO.setTableInfoDTO(tableInfoDTO);
        indexScoreInfoDTO.setBusinessGuide(businessGuideDTO);
    }

    private static void processStaticText(String locale, BussinessGuideDTO item, StringBuffer sb) {
        sb.append(SharkUtil.get(OpsConstants.generateShark(item.getKey()), locale, item.getKey()));
    }

    private void processTable(String indexName, TableInfoDTO tableInfoDTO, BusinessGuideJudgeTypeEnum businessGuideJudgeType) {
        tableInfoDTO.setTableDTOS(opsIndexTableConfig.getTableIndexName(opsIndexScoreConfig.getDBFieldMap(indexName)));
        tableInfoDTO.setTableType(businessGuideJudgeType.getType());

    }

    private static void processUrl(String locale, BussinessGuideDTO item, List<URLInfoDTO> urlInfoDTOS) {
        URLInfoDTO urlInfoDTO = new URLInfoDTO();
        urlInfoDTO.setName(item.getName());
        urlInfoDTO.setUrl(item.getUrl());
        urlInfoDTO.setUrlName(SharkUtil.get(OpsConstants.generateShark(item.getKey()), locale, item.getUrlName()));
        urlInfoDTOS.add(urlInfoDTO);
    }

    private void processDynamicText(Long partyId, Long siteId, String indexName, String locale, String queryDate, Integer rank, Double index_pre_abs_l3, BussinessGuideDTO item,
        BusinessGuideJudgeTypeEnum businessGuideJudgeType, StringBuffer sb) {
        switch (businessGuideJudgeType) {
            case MEDIAN:
                processMidan(siteId, indexName, locale, queryDate, rank, item, sb);
                break;
            case SECTION:
                processSection(partyId, siteId, indexName, locale, queryDate, rank, index_pre_abs_l3, item, sb);
                break;
            default:
                break;
        }
    }

    private void processSection(Long partyId, Long siteId, String indexName, String locale, String queryDate, Integer rank, Double index_pre_abs_l3, BussinessGuideDTO item, StringBuffer sb) {
        List<BusinessGuideRangeDTO> businessGuideRange = item.getBusinessGuideRange();
        AtomicReference<String> scorevalue = new AtomicReference<>();
        AtomicReference<String> sharkKey = new AtomicReference<>();
        businessGuideRange.forEach(rangeDTO -> {
            Integer leftRank = rangeDTO.getLeftRank();
            Integer rightRank = rangeDTO.getRightRank();
            boolean b = (Objects.nonNull(leftRank) && rank >= leftRank) || Objects.isNull(leftRank);
            boolean b1 = (Objects.nonNull(rightRank) && rank <= rightRank) || Objects.isNull(rightRank);
            if (b && b1) {
                scorevalue.set(rangeDTO.getScorevalue());
                sharkKey.set(rangeDTO.getKey());
            }
        });
        if (StringUtils.equals(scorevalue.get(), "lastRank")) {
            int i = rank + 1;
            generateSuggest(partyId, siteId, indexName, locale, queryDate, i, sb, sharkKey, rank, index_pre_abs_l3, scorevalue.get());
        } else if (StringUtils.equals(scorevalue.get(), "preRank")) {
            int i = rank - 1;
            generateSuggest(partyId, siteId, indexName, locale, queryDate, i, sb, sharkKey, rank, index_pre_abs_l3, scorevalue.get());
        } else {
            Integer i = Integer.valueOf(scorevalue.get());
            List<Map<String, Object>> maps = daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, buildDetailIndexParam(null, siteId, indexName, queryDate, List.of(i)));
            if (CollectionUtils.isNotEmpty(maps)) {
                Map<String, Object> stringObjectMap3 = maps.stream().findFirst().get();
                Double pre_index_pre_abs_l3 = MapUtil.getKeyAsDouble(stringObjectMap3, "index_pre_abs_l3");
                sb.append(
                    String.format(SharkUtil.get(OpsConstants.generateShark(sharkKey.get()), locale, sharkKey.get()), i, getDeltaValue(partyId, indexName, rank, i, queryDate, pre_index_pre_abs_l3 - index_pre_abs_l3)));
            }
        }
    }

    private void processMidan(Long siteId, String indexName, String locale, String queryDate, Integer rank, BussinessGuideDTO item, StringBuffer sb) {
        Map<String, BusinessGuideRangeDTO> scoreValueMap = item.getBusinessGuideRange().stream().collect(Collectors.toMap(BusinessGuideRangeDTO::getScorevalue, a -> a, (k1, k2) -> k2));
        List<Map<String, Object>> queriedDataByDaas2 = daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_MAX_RANK, buildMaxRankParam(siteId, indexName, queryDate));
        String str = null;
        if (CollectionUtils.isNotEmpty(queriedDataByDaas2)) {
            Map<String, Object> stringObjectMap2 = queriedDataByDaas2.stream().findFirst().get();
            Integer maxRank = MapUtil.getKeyAsInteger(stringObjectMap2, "opsRank");
            str = maxRank / 2 > rank ? "up" : "down";
        }
        sb.append(SharkUtil.get(OpsConstants.generateShark(scoreValueMap.get(str).getKey()), locale, scoreValueMap.get(str).getKey()));
    }

    private void setSummary(String indexName, String locale, String queryDate, IndexScoreInfoDTO indexScoreInfoDTO) {
        // 查询权重数据
        Map<String, Map<String, Object>> weightTableMap =
            daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_WEIGHT, buildParam(queryDate)).stream().collect(Collectors.toMap(item ->MapUtil.getKeyAsString(item,"index_id_l3"), a -> a, (k1, k2) -> k2));
        Map<String, Object> weightIdMap = weightTableMap.get(opsIndexScoreConfig.getDBFieldMap(indexName));
        String duration = MapUtil.getKeyAsString(weightIdMap, "duration");
        indexScoreInfoDTO.setSummary(process(queryDate,duration, locale));
    }

    private void setFactor(Long partyId, Long siteId, String locale, Map<String, Object> detailData, String queryDate, IndexScoreInfoDTO indexScoreInfoDTO) {
        // 查询因子表信息
        List<Map<String, Object>> factorData = daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_FACTOR_BY_INDEX, buildParam(partyId, siteId, queryDate, (String)detailData.get("index_id_l3")));
        if (CollectionUtils.isEmpty(factorData)) {
            return;
        }
        // 因子表数据
        List<FactorDetailDTO> list = factorData.stream().map(item -> {
            String factorId = MapUtil.getKeyAsString(item, "factor_id");
            if (StringUtils.isBlank(factorId)) {
                return null;
            }
            final String[] factorValue = {Objects.requireNonNull(MathUtil.serDoubleAccuracy(MapUtil.getKeyAsDouble(item,"factor_value"), 2)).toString()};
            indexHandlerList.forEach(indexHandler -> {
                Boolean support = indexHandler.isSupport(item, detailData, factorValue[0]);
                if (support) {
                    factorValue[0] = indexHandler.process(item, detailData, factorValue[0]);
                }
            });
            if (Objects.nonNull(factorValue[0])) {
                FactorDetailDTO factorDetailDTO = new FactorDetailDTO();
                factorDetailDTO.setKey(SharkUtil.get(OpsConstants.generateShark(factorId), locale, factorId));
                factorDetailDTO.setSharkKey(factorId);
                factorDetailDTO.setValue(factorValue[0]);
                return factorDetailDTO;
            }
            return null;
        }).filter(Objects::nonNull).toList();
        Optional<FactorDetailDTO> isNew = list.stream().filter(item -> StringUtils.equals(item.getSharkKey(), "is_new")).findFirst();
        if (isNew.isPresent()) {
            FactorDetailDTO factorDetailDTO = isNew.get();
            String value = factorDetailDTO.getValue();
            if (StringUtils.equals(value, OpsConstants.NO)) {
                list = list.stream().filter(item -> !StringUtils.equals(item.getSharkKey(), "new_days")).toList();
            }
        }
        indexScoreInfoDTO.setFactorDetails(list);
    }

    private void generateSuggest(Long partyId, Long siteId, String indexName, String locale, String queryDate, int i, StringBuffer sb, AtomicReference<String> sharkKey, Integer rank, Double index_pre_abs_l3, String s) {
        List<Map<String, Object>> maps = daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, buildDetailIndexParam(null, siteId, indexName, queryDate, List.of(i)));
        if (CollectionUtils.isNotEmpty(maps)) {
            Map<String, Object> stringObjectMap3 = maps.stream().findFirst().get();
            Double pre_index_pre_abs_l3 = MapUtil.getKeyAsDouble(stringObjectMap3,"index_pre_abs_l3");
            double v;
            if (StringUtils.equals(s, "lastRank")) {
                v = index_pre_abs_l3 - pre_index_pre_abs_l3;
            } else {
                v = pre_index_pre_abs_l3 - index_pre_abs_l3;
            }
            sb.append(String.format(SharkUtil.get(OpsConstants.generateShark(sharkKey.get()), locale, sharkKey.get()), getDeltaValue(partyId, indexName, rank, i, queryDate, v)));
        }
    }

    private HashMap<String, Object> buildMaxRankParam(Long siteId, String indexName, String queryDate) {
        HashMap<String, Object> queryMacRankDetail = new HashMap<>();
        queryMacRankDetail.put("hiveD", queryDate);
        queryMacRankDetail.put("siteId", siteId);
        queryMacRankDetail.put("indexName", opsIndexScoreConfig.getDBFieldMap(indexName));
        return queryMacRankDetail;
    }

    private HashMap<String, Object> buildDetailIndexParam(Long partyId, Long siteId, String indexName, String queryDate, List<Integer> opsRanks) {
        HashMap<String, Object> queryDetailIndexParam = new HashMap<>();
        queryDetailIndexParam.put("hiveD", queryDate);
        queryDetailIndexParam.put("indexName", opsIndexScoreConfig.getDBFieldMap(indexName));
        queryDetailIndexParam.put("siteId", siteId);
        if (Objects.nonNull(partyId)) {
            queryDetailIndexParam.put("vendorId", partyId);
        }
        if (CollectionUtils.isNotEmpty(opsRanks)) {
            queryDetailIndexParam.put("opsRanks", opsRanks);
        }
        return queryDetailIndexParam;
    }

    private static HashMap<String, Object> buildParam(Long partyId, Long siteId, String queryDate, String index_id_l3) {
        HashMap<String, Object> params1 = new HashMap<>();
        params1.put("hiveD", queryDate);
        params1.put("siteId", siteId);
        params1.put("vendorId", partyId);
        params1.put("indexIdL3", index_id_l3);
        return params1;
    }

    private static HashMap<String, Object> buildParam(String queryDate) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("hiveD", queryDate);
        return params;
    }

    private long getDeltaValue(Long partyId, String indexName, Integer rank, int i, String queryDate, double v) {
        List<Integer> rankList = new ArrayList<>();
        rankList.add(rank);
        rankList.add(i);
        Collections.sort(rankList);
        String redisKey = String.format(key, partyId, indexName, queryDate, JsonUtil.toString(rankList));
        String redisValue = opsRedisGateway.get(redisKey);
        double delta;
        if (StringUtils.isNotBlank(redisValue)) {
            Integer i1 = Integer.valueOf(redisValue);
            delta = v * (100 + i1) / 100;
        } else {
            int i1 = RandomUtils.nextInt(10, 21);
            opsRedisGateway.set(redisKey, String.valueOf(i1), opsCommonConfig.getExpireSeconds());
            BigDecimal divide = new BigDecimal("100").add(new BigDecimal(i1)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(v));
            delta = divide.doubleValue();
        }
        return Objects.requireNonNull(MathUtil.serDoubleAccuracy(delta, 0)).longValue();
    }

    private static void processValueMatch(List<RangeDTO> range, List<IndexRuleDTO> indexRuleDTOS, String locale) {
        range.forEach(item -> {
            IndexRuleDTO indexRuleDTO = new IndexRuleDTO();
            if (StringUtils.equals(item.getScoreLeft(), item.getScoreRight())) {
                indexRuleDTO.setIndexRank(item.getScoreLeft());
            } else {
                indexRuleDTO.setIndexRank(SharkUtil.get(OpsConstants.generateShark(OTHER), locale, OTHER));
            }
            indexRuleDTO.setLevel(SharkUtil.get(OpsConstants.generateShark(item.getLevel()), locale, item.getLevel()));
            indexRuleDTOS.add(indexRuleDTO);
        });
    }

    private void processSection(Long siteId, String indexName, List<RangeDTO> range, String queryDate, List<IndexRuleDTO> indexRuleDTOS, String locale) {
        List<Integer> list = range.stream().map(item -> {
            String scoreLeft = item.getScoreLeft();
            String scoreRight = item.getScoreRight();
            if (StringUtils.equals(scoreLeft, "rightRank")) {
                return item.getRightRank();
            }
            if (StringUtils.equals(scoreRight, "leftRank")) {
                return item.getLeftRank();
            }
            return null;
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Pair<Map<Integer, Double>, Integer> mapIntegerPair = getaDouble(siteId, indexName, queryDate, list);
        Map<Integer, Double> rankScoreMap = mapIntegerPair.getLeft();
        Integer maxRank = mapIntegerPair.getRight();

        range.forEach(item -> {
            IndexRuleDTO indexRuleDTO = new IndexRuleDTO();
            indexRuleDTO.setIndexRank(getIndexRank(locale, item));

            String scoreLeft = item.getScoreLeft();
            String scoreRight = item.getScoreRight();
            Double leftScore;
            Double rightScore;
            if (StringUtils.equals(scoreLeft, "rightRank")) {
                Integer rightRank = item.getRightRank();
                leftScore = rankScoreMap.get(rightRank);
            } else {
                leftScore = Double.valueOf(scoreLeft);
            }
            if (StringUtils.equals(scoreRight, "leftRank")) {
                Integer leftRank = item.getLeftRank();
                rightScore = rankScoreMap.get(leftRank);
            } else {
                rightScore = Double.valueOf(scoreRight);
            }
            StringBuffer stringBuffer = new StringBuffer();
            if (Objects.isNull(rightScore)) {
                stringBuffer.append("/");
            } else if (Objects.isNull(leftScore)) {
                leftScore = rankScoreMap.get(maxRank);
                processScoreRange(item, leftScore, rightScore, stringBuffer);
            } else {
                processScoreRange(item, leftScore, rightScore, stringBuffer);
            }
            indexRuleDTO.setScoreRange(stringBuffer.toString());
            indexRuleDTO.setLevel(SharkUtil.get(OpsConstants.generateShark(item.getLevel()), locale, item.getLevel()));
            indexRuleDTOS.add(indexRuleDTO);
        });
    }

    private void processScoreRange(RangeDTO item, Double leftScore, Double rightScore, StringBuffer stringBuffer) {
        stringBuffer.append(item.getLeftInterval() ? "[" : "(");
        stringBuffer.append(leftScore);
        stringBuffer.append(",");
        stringBuffer.append(rightScore);
        stringBuffer.append(item.getRightInterval() ? "]" : ")");
    }

    private static String getIndexRank(String locale, RangeDTO item) {
        String indexRank;
        if (Objects.isNull(item.getLeftRank()) && Objects.nonNull(item.getRightRank())) {
            indexRank = String.format(SharkUtil.get(OpsConstants.generateShark(OpsConstants.TOP), locale, OpsConstants.TOP), item.getRankright());
        } else if (Objects.nonNull(item.getLeftRank()) && Objects.isNull(item.getRightRank())) {
            indexRank = String.format(SharkUtil.get(OpsConstants.generateShark(OpsConstants.DOWN), locale, OpsConstants.DOWN), item.getRankleft());
        } else {
            indexRank = String.format(SharkUtil.get(OpsConstants.generateShark(OpsConstants.MIDDLE), locale, OpsConstants.MIDDLE), item.getRankleft(), item.getRankright());
        }
        return indexRank;
    }

    private Pair<Map<Integer, Double>, Integer> getaDouble(Long siteId, String indexName, String queryDate, List<Integer> leftRanks) {
        List<Map<String, Object>> queriedDataByDaas2 = daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_MAX_RANK, buildMaxRankParam(siteId, indexName, queryDate));
        Integer maxRank;
        if (CollectionUtils.isNotEmpty(queriedDataByDaas2)) {
            Map<String, Object> stringObjectMap2 = queriedDataByDaas2.stream().findFirst().get();
            maxRank = MapUtil.getKeyAsInteger(stringObjectMap2, "opsRank");
            boolean b = leftRanks.stream().anyMatch(item -> item > maxRank);
            if (b) {
                leftRanks.add(maxRank);
            }
        } else {
            maxRank = 1;
        }
        List<Map<String, Object>> rankScoreMap = daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, buildDetailIndexParam(null, siteId, indexName, queryDate, leftRanks));
        Map<Integer, Double> resutlMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rankScoreMap)) {
            rankScoreMap.forEach(item -> {
                Integer rank = MapUtil.getKeyAsInteger(item, "ops_rank");
                Double score = MapUtil.getKeyAsDouble(item, "index_score_l3");
                resutlMap.put(rank, score);

            });
        }
        return Pair.of(resutlMap, maxRank);
    }

    private String process(String queryDate, String duration, String locale) {
        if (StringUtils.equals(duration, opsCommonConfig.getCurrentDuration())) {
            return String.format(SharkUtil.get(OpsConstants.generateShark(OpsConstants.PERIOD1), locale, OpsConstants.PERIOD1), DateUtil.convertDateToString(DateUtil.convertStringToDate(queryDate, DateUtil.YYYY_MM_DD), DateUtil.YYYYMMDD));
        }
        String today = DateUtil.convertDateToString(DateUtil.convertStringToDate(queryDate, DateUtil.YYYY_MM_DD), DateUtil.YYYYMMDD);
        String before = DateUtil.getBeforeDate(DateUtil.convertStringToDate(queryDate, DateUtil.YYYY_MM_DD), Integer.parseInt(duration), DateUtil.YYYYMMDD);
        return String.format(SharkUtil.get(OpsConstants.generateShark(OpsConstants.PERIOD), locale, OpsConstants.PERIOD), before, today);
    }
}
