package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.ctrip.dcs.ops.infrastructure.util.MapUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.ops.infrastructure.config.OPSIndexScoreConfig;
import com.ctrip.dcs.ops.infrastructure.config.OPSOrderConfig;
import com.ctrip.dcs.ops.infrastructure.config.dto.OPSIndexScoreInfoDTO;
import com.ctrip.dcs.ops.infrastructure.config.dto.OPSOrderDTO;
import com.ctrip.dcs.ops.infrastructure.config.dto.RangeDTO;
import com.ctrip.dcs.ops.infrastructure.config.dto.RangeTypeInfoDTO;
import com.ctrip.dcs.ops.infrastructure.constant.DataTypeSnum;
import com.ctrip.dcs.ops.infrastructure.constant.RangeTypeEnum;
import com.ctrip.dcs.ops.infrastructure.util.DateUtil;
import com.ctrip.dcs.ops.infrastructure.util.MathUtil;
import com.ctrip.dcs.ops.infrastructure.value.IndexScore;
import com.ctrip.dcs.ops.infrastructure.value.OpsScoreDetailDTO;
import com.ctrip.dcs.ops.infrastructure.value.ScoreDetailDTO;
import com.ctrip.dcs.ops.infrastructure.value.TeamDTO;
import com.google.common.collect.Lists;

@Service
public class DayrentScoreService extends OPSBaseService {

    @Autowired
    private OPSIndexScoreConfig opsIndexScoreConfig;

    @Autowired
    private OPSOrderConfig opsOrderConfig;
    @Autowired
    @Qualifier("scoreCalThreadPool")
    private ExecutorService scoreCalThreadPool;

    public OpsScoreDetailDTO queryData(String partyId, Long siteId) {
        OpsScoreDetailDTO opsScoreDetailDTO = new OpsScoreDetailDTO();
        Pair<String, String> updateDate = getQueryDate();
        String queryDate = updateDate.getLeft();
        if (StringUtils.isBlank(queryDate)) {
            return opsScoreDetailDTO;
        }
        opsScoreDetailDTO.setUpdateTime(updateDate.getRight());

        HashMap<String, Object> params = new HashMap<>();
        params.put("vendorId", Collections.singletonList(partyId));
        params.put("hiveD", queryDate);
        params.put("siteId", siteId);

        List<Map<String, Object>> totalData = daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL, params);
        if (CollectionUtils.isEmpty(totalData)) {
            return opsScoreDetailDTO;
        }
        Map<String, IndexScore> map = new HashMap<>();
        Map<String, Map<String, Object>> indexMap = totalData.stream().collect(Collectors.toMap(item -> MapUtil.getKeyAsString(item,"index_id_l3"), a -> a, (k1, k2) -> k2));
        opsIndexScoreConfig.getFieldList().forEach(item -> {
            Map<String, Object> stringObjectMap = indexMap.get(item);
            Integer rank = MapUtil.getKeyAsInteger(stringObjectMap, "ops_rank");
            Double score = MapUtil.getKeyAsDouble(stringObjectMap, "index_score_l3");
            IndexScore indexScore = new IndexScore();
            indexScore.setName(item);
            indexScore.setScore(MathUtil.doubleAccuracy(score));
            indexScore.setRank(rank);
            map.put(item, indexScore);
        });

        HashMap<String, Object> params1 = new HashMap<>();
        params1.put("hiveD", queryDate);
        List<Map<String, Object>> weightIndex = daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_WEIGHT, params1);
        Map<String, Map<String, Object>> weightMap = weightIndex.stream().collect(Collectors.toMap(item -> MapUtil.getKeyAsString(item,"index_id_l3"), a -> a, (k1, k2) -> k2));

        List<CompletableFuture<TeamDTO>> futures = Arrays.stream(DataTypeSnum.values())
            .map(dataType -> map.entrySet().stream().map(entry -> CompletableFuture.supplyAsync(() -> query(entry.getKey(), entry.getValue(), partyId, siteId, queryDate, dataType), scoreCalThreadPool)).toList())
            .flatMap(Collection::stream).toList();

        // 等待所有任务完成并收集结果
        List<TeamDTO> results = Optional.ofNullable(futures).orElse(Lists.newArrayList()).stream().map(CompletableFuture::join).toList();
        Map<String, List<TeamDTO>> indexNameMap = Optional.ofNullable(results).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).collect(Collectors.groupingBy(TeamDTO::getIndexName));

        opsScoreDetailDTO.setScoreDetailDTOS(opsIndexScoreConfig.getFieldList().stream().map(index -> {
            Map<String, Object> indexItemMap = indexMap.get(index);
            if (MapUtils.isEmpty(indexItemMap)) {
                return null;
            }
            List<TeamDTO> teamDTOS = indexNameMap.get(index);
            if (CollectionUtils.isEmpty(teamDTOS)) {
                return null;
            }
            Map<String, Object> weight = weightMap.get(index);
            OPSIndexScoreInfoDTO opsIndexScoreInfo = opsIndexScoreConfig.getOPSIndexScoreInfo(index);
            ScoreDetailDTO scoreDetailDTO = new ScoreDetailDTO();

            scoreDetailDTO.setItemName(opsIndexScoreInfo.getIndexKey());
            Map<DataTypeSnum, Double> scoreMap = teamDTOS.stream().collect(Collectors.toMap(TeamDTO::getType, TeamDTO::getScore, (k1, k2) -> k2));

            Integer rank = MapUtil.getKeyAsInteger(indexItemMap, "ops_rank");
            scoreDetailDTO.setRank(rank);
            Double todayScore = MapUtil.getKeyAsDouble(indexItemMap, "index_score_l3");
            scoreDetailDTO.setScore(todayScore);
            Double v = scoreMap.get(DataTypeSnum.YESTDAY);
            if (Objects.nonNull(v) && Objects.nonNull(todayScore)) {
                scoreDetailDTO.setScoreCompareToYestday(MathUtil.doubleAccuracy(todayScore - v));
            }
            scoreDetailDTO.setPreTeamScore(MathUtil.doubleAccuracy(scoreMap.get(DataTypeSnum.PRERANK)));
            scoreDetailDTO.setMedianTeamScore(MathUtil.doubleAccuracy(scoreMap.get(DataTypeSnum.MEDIAN)));
            OPSOrderDTO sourceInfo = opsOrderConfig.getOPSOrderInfo(index);
            scoreDetailDTO.setOrder(sourceInfo.getOrder());
            scoreDetailDTO.setPeriod(MapUtil.getKeyAsString(weight, "duration"));
            scoreDetailDTO.setParentKey(MapUtil.getKeyAsString(indexItemMap, "index_id_l1"));
            scoreDetailDTO.setWeight(sourceInfo.getWeightLevel());
            scoreDetailDTO.setLevel(process(opsIndexScoreInfo, rank, todayScore));
            return scoreDetailDTO;
        }).filter(Objects::nonNull).toList());
        return opsScoreDetailDTO;
    }

    private String process(OPSIndexScoreInfoDTO sourceInfo, Integer rank, Double todayScore) {
        RangeTypeInfoDTO rangeType = sourceInfo.getRangeTypeInfo();

        if (Objects.nonNull(rangeType)) {
            String type = rangeType.getRangeType();
            List<RangeDTO> range = rangeType.getRange();
            RangeTypeEnum rangeTypeEnum = RangeTypeEnum.getRangeTypeEnum(type);
            switch (rangeTypeEnum) {
                case SECTION:
                    AtomicReference<String> sectionLevel = new AtomicReference<>("");
                    AtomicReference<Boolean> sectionFlag = new AtomicReference<>(false);
                    range.forEach(item -> {
                        boolean leftFlag = (Objects.nonNull(item.getLeftRank()) && (rank > item.getLeftRank() || item.getLeftInterval() && Objects.equals(rank, item.getLeftRank()))) || Objects.isNull(item.getLeftRank());
                        boolean rightFlag =
                            (Objects.nonNull(item.getRightRank()) && (rank < item.getRightRank() || item.getRightInterval() && Objects.equals(rank, item.getRightRank()))) || Objects.isNull(item.getRightRank());
                        if (leftFlag && rightFlag && BooleanUtils.isFalse(sectionFlag.get())) {
                            sectionLevel.set(item.getLevel());
                            sectionFlag.set(true);
                        }
                    });
                    return sectionLevel.get();
                case FIXEDSCORERANGE:
                case VALUEMATCH:
                    AtomicReference<String> valueMatchLevel = new AtomicReference<>("");
                    AtomicReference<Boolean> valueFlag = new AtomicReference<>(false);
                    range.forEach(item -> {
                        boolean leftFlag = ((Objects.nonNull(item.getScoreLeft()) && todayScore > Double.parseDouble(item.getScoreLeft()))
                            || (item.getLeftInterval() && Objects.equals(todayScore, Double.parseDouble(item.getScoreLeft()))));
                        boolean rightFlag = ((Objects.nonNull(item.getScoreRight()) && todayScore < Double.parseDouble(item.getScoreRight()))
                            || (item.getRightInterval() && Objects.equals(todayScore, Double.parseDouble(item.getScoreRight()))));
                        if (leftFlag && rightFlag && BooleanUtils.isFalse(valueFlag.get())) {
                            valueMatchLevel.set(item.getLevel());
                            valueFlag.set(true);
                        }
                    });
                    return valueMatchLevel.get();
                default:
                    return StringUtils.EMPTY;
            }

        }
        return StringUtils.EMPTY;
    }

    protected TeamDTO query(String key, IndexScore value, String partyId, Long siteId, String queryDate, DataTypeSnum dataType) {
        switch (dataType) {
            case MEDIAN:
                return processMedian(key, siteId, queryDate, dataType);
            case PRERANK:
                return processPrerank(key, value, siteId, queryDate, dataType);
            case YESTDAY:
                return processYestday(key, partyId, siteId, queryDate, dataType);
            default:
                break;
        }
        return null;
    }

    @Nullable
    private TeamDTO processYestday(String key, String partyId, Long siteId, String queryDate, DataTypeSnum dataType) {
        LocalDate date = DateUtil.convertStringToDate(queryDate, DateUtil.YYYY_MM_DD);
        String beforeDate = DateUtil.getBeforeDate(date, 1);
        HashMap<String, Object> queryDetailIndexParam = new HashMap<>();
        queryDetailIndexParam.put("hiveD", beforeDate);
        queryDetailIndexParam.put("indexName", key);
        queryDetailIndexParam.put("siteId", siteId);
        queryDetailIndexParam.put("vendorId", Long.valueOf(partyId));
        List<Map<String, Object>> queriedDataByDaas1 = daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, queryDetailIndexParam);
        return getTeamInfoDTO(key, dataType, queriedDataByDaas1);
    }

    @Nullable
    private TeamDTO processPrerank(String key, IndexScore value, Long siteId, String queryDate, DataTypeSnum dataType) {
        if (value.getRank() == 1) {
            TeamDTO teamDTO = new TeamDTO();
            teamDTO.setName(key);
            teamDTO.setIndexName(key);
            teamDTO.setScore(value.getScore());
            teamDTO.setType(dataType);
            return teamDTO;
        } else {
            HashMap<String, Object> queryDetailIndexParam = new HashMap<>();
            queryDetailIndexParam.put("hiveD", queryDate);
            queryDetailIndexParam.put("indexName", key);
            queryDetailIndexParam.put("siteId", siteId);
            queryDetailIndexParam.put("opsRank", value.getRank() - 1);
            return getTeamInfoDTO(key, dataType, queryDetailIndexParam);
        }
    }

    private TeamDTO processMedian(String key, Long siteId, String queryDate, DataTypeSnum dataType) {
        HashMap<String, Object> queryMaxRank = new HashMap<>();
        queryMaxRank.put("hiveD", queryDate);
        queryMaxRank.put("indexName", key);
        queryMaxRank.put("siteId", siteId);
        List<Map<String, Object>> queriedDataByDaas = daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_MAX_RANK, queryMaxRank);
        if (CollectionUtils.isNotEmpty(queriedDataByDaas)) {
            Map<String, Object> stringObjectMap = queriedDataByDaas.stream().findFirst().get();
            Integer rank = (Integer)stringObjectMap.get("opsRank");
            int i = rank / 2;
            HashMap<String, Object> queryDetailIndexParam = new HashMap<>();
            queryDetailIndexParam.put("hiveD", queryDate);
            queryDetailIndexParam.put("indexName", key);
            queryDetailIndexParam.put("siteId", siteId);
            queryDetailIndexParam.put("opsRank", i);
            TeamDTO teamDTO = getTeamInfoDTO(key, dataType, queryDetailIndexParam);
            if (teamDTO != null)
                return teamDTO;
        }
        return null;
    }

    private TeamDTO getTeamInfoDTO(String key, DataTypeSnum dataType, List<Map<String, Object>> queriedDataByDaas1) {
        if (CollectionUtils.isNotEmpty(queriedDataByDaas1)) {
            Map<String, Object> stringObjectMap1 = queriedDataByDaas1.stream().findFirst().get();
            TeamDTO teamDTO = new TeamDTO();
            teamDTO.setName(key);
            teamDTO.setIndexName(key);
            teamDTO.setScore(Double.valueOf(String.valueOf(stringObjectMap1.get("index_score_l3"))));
            teamDTO.setType(dataType);
            return teamDTO;
        }
        return null;
    }

    private TeamDTO getTeamInfoDTO(String key, DataTypeSnum dataType, HashMap<String, Object> queryDetailIndexParam) {
        List<Map<String, Object>> queriedDataByDaas1 = daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, queryDetailIndexParam);
        TeamDTO teamDTO = getTeamInfoDTO(key, dataType, queriedDataByDaas1);
        if (teamDTO != null)
            return teamDTO;
        return null;
    }
}
