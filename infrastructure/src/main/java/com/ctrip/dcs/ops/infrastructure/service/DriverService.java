package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.QUERY_DRIVER_SCORE;
import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.QUERY_DRIVER_SCORE_BY_DRIVERID;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.ops.infrastructure.constant.DriverItemScoreTypeEnum;
import com.ctrip.dcs.ops.infrastructure.constant.DriverLevelEnum;
import com.ctrip.dcs.ops.infrastructure.constant.OpsConstants;
import com.ctrip.dcs.ops.infrastructure.util.GrowthRateCalculator;
import com.ctrip.dcs.ops.infrastructure.util.MapUtil;
import com.ctrip.dcs.ops.infrastructure.util.MathUtil;
import com.ctrip.dcs.ops.infrastructure.util.SharkUtil;
import com.ctrip.dcs.ops.infrastructure.value.*;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

@Service
public class DriverService extends BaseQuery {
    public static final String SPLIT = "[，,]+";

    private Map<String, String> map = ImmutableMap.of(
            "soppassrate", "sopPassRate",
            "trackpassrate", "trackPassRate",
            "driverlevel", "curmonth_grade_id",
            "driverservicescore", "service_point",
            "driverscore", "total_point",
            "driveractivescore", "active_point_online");

    private Map<String, String> sortMap = ImmutableMap.of("ascend", "asc", "descend", "desc");

    public static final List<String> sortFieldList = Lists.newArrayList("soppassrate", "trackpassrate");

    public DriverListDTO queryDriverList(DriverListParam driverListParam) {
        DriverListDTO driverListDTO = new DriverListDTO();
        List<DriverListResultDTO> resultDTOS = new ArrayList<>();
        PageDTO pageDTO = new PageDTO();
        String sortField = driverListParam.getSortField();
        LocalDate[] localDates = convertQuertDate(driverListParam.getStartDate(), driverListParam.getEndDate());
        HashMap<String, Object> param = buildParam(localDates[0], localDates[1], driverListParam.getSupplierId(), driverListParam.getCityId());

        if (StringUtils.isNotBlank(driverListParam.getDriverId())) {
            queryByDriverId(driverListParam, param, resultDTOS);
        }else if (sortFieldList.contains(sortField.toLowerCase())) {
            queryDataSortByRate(driverListParam, param, resultDTOS, pageDTO);
        }else {
            queryDataSortByScore(driverListParam, param, resultDTOS, pageDTO);
        }
        driverListDTO.setResultDTOS(resultDTOS);
        driverListDTO.setPageDTO(pageDTO);
        return driverListDTO;
    }

    private void queryDataSortByScore(DriverListParam driverListParam, HashMap<String, Object> param, List<DriverListResultDTO> resultDTOS, PageDTO pageDTO) {
        buildParam(param, map.getOrDefault(driverListParam.getSortField().toLowerCase(), "id"), sortMap.getOrDefault(driverListParam.getSortOrder(), "asc"), driverListParam);
        List<Map<String, Object>> queryDriverScore = daasGatewayV2.queryDataByDaas(QUERY_DRIVER_SCORE, param);
        prcessResult(queryDriverScore, resultDTOS,driverListParam.getLocale());
        List<String> drvIdList = queryDriverScore.stream().map(item -> String.valueOf(item.get("drv_id"))).toList();
        pageDTO.setTotalSize(getTotalNum("countDriverScore", param));
        Map<Long, Map<String, Object>> drvIdMap = getResultByDrvIdList(param, drvIdList, QUERY_DRIVER_SCORE_BY_DRIVERID);
        resultDTOS.forEach(item -> {
            Long id = item.getDriverId();
            Map<String, Object> driverScoreMap = drvIdMap.get(id);
            item.setSopPassRate(MathUtil.doubleAccuracy(MapUtil.getKeyAsDouble(driverScoreMap, "sopPassRate")) );
            item.setTrackPassRate(MathUtil.doubleAccuracy(MapUtil.getKeyAsDouble(driverScoreMap, "trackPassRate")));
        });
    }
    private void queryDataSortByRate(DriverListParam driverListParam, HashMap<String, Object> param, List<DriverListResultDTO> resultDTOS, PageDTO pageDTO) {
        buildParam(param, map.get(driverListParam.getSortField().toLowerCase()), sortMap.get(driverListParam.getSortOrder()), driverListParam);
        List<Map<String, Object>> driverScoreList = daasGatewayV2.queryDataByDaas(QUERY_DRIVER_SCORE_BY_DRIVERID, param);
        processResult(driverScoreList, resultDTOS);
        List<String> drvIdList = driverScoreList.stream().map(item -> String.valueOf(item.get("drv_id"))).toList();
        pageDTO.setTotalSize( getTotalNum("countDriverScorebyDriverId", param));
        Map<Long, Map<String, Object>> drvIdMap = getResultByDrvIdList(param, drvIdList, QUERY_DRIVER_SCORE);
        resultDTOS.forEach(driverListResultDTO -> {
            Long id = driverListResultDTO.getDriverId();
            Map<String, Object> driverScoreMap = drvIdMap.get(id);
            driverListResultDTO.setDriverName(MapUtil.getKeyAsString(driverScoreMap, "drv_name"));
            driverListResultDTO.setDriverScore(MapUtil.getKeyAsDouble(driverScoreMap, "total_point"));
            driverListResultDTO.setDriverServiceScore(MapUtil.getKeyAsDouble(driverScoreMap, "service_point"));
            driverListResultDTO.setDriverActiveScore(MapUtil.getKeyAsDouble(driverScoreMap, "active_point_online"));
            driverListResultDTO.setDriverLevel(convertLevel(MapUtil.getKeyAsInteger(driverScoreMap, "curmonth_grade_id"), driverListParam.getLocale()));
        });
    }

    private Map<Long, Map<String, Object>> getResultByDrvIdList(HashMap<String, Object> param, List<String> drvIdList, String queryDriverScoreByDriverid) {
        param.put("driverIdList", drvIdList);
        List<Map<String, Object>> queryDriverScorebyDriverId = daasGatewayV2.queryDataByDaas(queryDriverScoreByDriverid, param);
        return queryDriverScorebyDriverId.stream().collect(Collectors.toMap(item -> Long.valueOf(String.valueOf(item.get("drv_id"))), a -> a, (k1, k2) -> k2));
    }

    private Integer getTotalNum(String apiName, HashMap<String, Object> param) {
        List<Map<String, Object>> result = daasGatewayV2.queryDataByDaas(apiName, param);
        return Integer.valueOf(String.valueOf(result.stream().findFirst().get().get("totalNum")));
    }

    private void queryByDriverId(DriverListParam driverListParam, HashMap<String, Object> param, List<DriverListResultDTO> resultDTOS) {
        String[] split = driverListParam.getDriverId().split(SPLIT);
        param.put("driverIdList", Arrays.stream(split).toList());
        List<Map<String, Object>> driverScoreList = daasGatewayV2.queryDataByDaas(QUERY_DRIVER_SCORE_BY_DRIVERID, param);
        if (CollectionUtils.isEmpty(driverScoreList)) {
            return;
        }

        driverScoreList.forEach(scoreResultMap ->{
            DriverListResultDTO driverListResultDTO = new DriverListResultDTO();
            driverListResultDTO.setDriverId(Long.valueOf(String.valueOf(scoreResultMap.get("drv_id"))));
            driverListResultDTO.setSopPassRate(MathUtil.doubleAccuracy(MapUtil.getKeyAsDouble(scoreResultMap, "sopPassRate")));
            driverListResultDTO.setTrackPassRate(MathUtil.doubleAccuracy(MapUtil.getKeyAsDouble(scoreResultMap, "trackPassRate")));
            resultDTOS.add(driverListResultDTO);
        });
        List<Map<String, Object>> queryDriverScore = daasGatewayV2.queryDataByDaas(QUERY_DRIVER_SCORE, param);
        if (CollectionUtils.isEmpty(queryDriverScore)) {
            return;
        }
        Map<Long, Map<String, Object>> drvId = queryDriverScore.stream().collect(Collectors.toMap(item -> MapUtil.getKeyAsLong(item, "drv_id"), a -> a, (k1, k2) -> k2));
        resultDTOS.forEach(driverListResultDTO ->{
            Map<String, Object> driverScoreMap = drvId.get(driverListResultDTO.getDriverId());
            buildResultInfo(driverListResultDTO, driverScoreMap, driverListParam.getLocale());
        });
    }

    private static void buildResultInfo(DriverListResultDTO driverListResultDTO, Map<String, Object> driverScoreMap, String locale) {
        driverListResultDTO.setDriverId(MapUtil.getKeyAsLong(driverScoreMap, "drv_id"));
        driverListResultDTO.setDriverName(MapUtil.getKeyAsString(driverScoreMap, "drv_name"));
        driverListResultDTO.setDriverScore(MapUtil.getKeyAsDouble(driverScoreMap, "total_point"));
        driverListResultDTO.setDriverServiceScore(MapUtil.getKeyAsDouble(driverScoreMap, "service_point"));
        driverListResultDTO.setDriverActiveScore(MapUtil.getKeyAsDouble(driverScoreMap, "active_point_online"));
        driverListResultDTO.setDriverLevel(convertLevel(MapUtil.getKeyAsInteger(driverScoreMap,"curmonth_grade_id"),locale));
    }

    private void buildParam(HashMap<String, Object> param, String map, String sortMap, DriverListParam driverListParam) {
        param.put("orderField", map);
        param.put("sortOrder", sortMap);
        param.put("pageNum", (driverListParam.getPageNo() - 1) * driverListParam.getPageSize());
        param.put("pageSize", driverListParam.getPageSize());
    }

    @NotNull
    private static HashMap<String, Object> buildParam(LocalDate localDates, LocalDate localDates1, Long supplierId, Long cityId) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("startDate", localDates.toString());
        param.put("endDate", localDates1.toString());
        param.put("supplierId", String.valueOf(supplierId));
        param.put("cityId", String.valueOf(cityId));
        return param;
    }

    private static void prcessResult(List<Map<String, Object>> queryDriverScore, List<DriverListResultDTO> resultDTOS, String locale) {
        queryDriverScore.forEach(item -> {
            DriverListResultDTO driverListResultDTO = new DriverListResultDTO();
            buildResult(resultDTOS, item, driverListResultDTO,locale);
        });
    }

    private static void buildResult(List<DriverListResultDTO> resultDTOS, Map<String, Object> item, DriverListResultDTO driverListResultDTO, String locale) {
        buildResultInfo(driverListResultDTO, item, locale);
        resultDTOS.add(driverListResultDTO);
    }

    private static void processResult(List<Map<String, Object>> driverScoreList, List<DriverListResultDTO> resultDTOS) {
        driverScoreList.forEach(item -> {
            DriverListResultDTO driverListResultDTO = new DriverListResultDTO();
            driverListResultDTO.setDriverId(Long.valueOf(String.valueOf(item.get("drv_id"))));
            driverListResultDTO.setSopPassRate(MathUtil.doubleAccuracy(MapUtil.getKeyAsDouble(item, "sopPassRate")));
            driverListResultDTO.setTrackPassRate(MathUtil.doubleAccuracy(MapUtil.getKeyAsDouble(item, "trackPassRate")));
            resultDTOS.add(driverListResultDTO);
        });
    }

    public DriverDetailRespDTO queryDriverDetail(DriverDetailQueryReq detailQueryReq) {
        LocalDate[] newQueryDate = convertQuertDate(detailQueryReq.getStartDate(), detailQueryReq.getEndDate());
        LocalDate startDate = newQueryDate[0];
        LocalDate endDate = newQueryDate[1];

        // 查一天就把开始日期改成一天前，后面计算环比需要
        if (detailQueryReq.isOneDay()) {
            startDate = fetchLastDateFromEnd(startDate);
        }
        HashMap<String, Object> param = buildParam(startDate, endDate, detailQueryReq.getSupplierId(), detailQueryReq.getCityId());
        param.put("drvId", List.of(detailQueryReq.getDriverId()));

        DriverDetailRespDTO detailRespDTO = new DriverDetailRespDTO();
        detailRespDTO.setDriverId(detailQueryReq.getDriverId());
        List<Map<String, Object>> driverSummeryScores = daasGatewayV2.queryDataByDaas("queryDriverDetail", param);

        Map<String, Object> currDriverData = driverSummeryScores.stream().filter(d -> endDate.toString().equals(String.valueOf(d.get("hive_d")))).findFirst().orElse(Maps.newHashMap());
        Map<String, Object> preDriverData = driverSummeryScores.stream().filter(d -> fetchLastDateFromEnd(endDate).toString().equals(String.valueOf(d.get("hive_d")))).findFirst().orElse(Maps.newHashMap());
        for (DriverItemScoreTypeEnum scoreTypeEnum : DriverItemScoreTypeEnum.values()) {
            DriverDetailRespDTO.DriverScoreDetail scoreDetail = new DriverDetailRespDTO.DriverScoreDetail();

            scoreDetail.setCurrScore(MapUtil.getKeyAsDouble(currDriverData, scoreTypeEnum.getDbField()));
            scoreDetail.setLastScore(MapUtil.getKeyAsDouble(preDriverData, scoreTypeEnum.getDbField()));
            scoreDetail.setItemScoreTypeEnum(scoreTypeEnum);
            detailRespDTO.getDriverScoreDetails().add(scoreDetail);
        }

        if (detailQueryReq.isOneDay()) {
            driverSummeryScores = driverSummeryScores.stream().filter(d -> endDate.toString().equals(String.valueOf(d.get("hive_d")))).collect(Collectors.toList());
        }
        // 司机端SOP使用合格率分子
        Long standardInLineNum = driverSummeryScores.stream().mapToLong(d -> MapUtil.getKeyAsLong(d, "standard_in_line_num")).sum();
        // 分母
        Long standardInLineDen = driverSummeryScores.stream().mapToLong(d -> MapUtil.getKeyAsLong(d, "standard_in_line_den")).sum();

        // 司机端轨迹合格率分子
        Long trailStandardNum = driverSummeryScores.stream().mapToLong(d -> MapUtil.getKeyAsLong(d, "trail_standard_num")).sum();
        // 分母
        Long trailStandardDen = driverSummeryScores.stream().mapToLong(d -> MapUtil.getKeyAsLong(d, "trail_standard_den")).sum();
        detailRespDTO.setSopPassRate(GrowthRateCalculator.calPercentage(standardInLineNum, standardInLineDen, 2) + "");
        detailRespDTO.setTrackPassRate(GrowthRateCalculator.calPercentage(trailStandardNum, trailStandardDen, 2) + "");

        if (CollectionUtils.isEmpty(driverSummeryScores)) {
            detailRespDTO.setDriverLevel(StringUtils.EMPTY);
        } else {
            detailRespDTO.setDriverLevel(convertLevel(MapUtil.getKeyAsInteger(driverSummeryScores.getLast(), "curmonth_grade_id"), detailQueryReq.getLocale()));
        }
        return detailRespDTO;
    }

    private LocalDate fetchLastDateFromEnd(LocalDate endDate) {
        try {
            return endDate.minusDays(1);
        } catch (Exception e) {
            return endDate;
        }
    }

    private static String convertLevel(Integer level, String locale) {
        DriverLevelEnum anEnum = DriverLevelEnum.getEnum(level);
        if (Objects.isNull(anEnum)) {
            return "-";
        }
        return SharkUtil.get(OpsConstants.generateShark(anEnum.getName()), locale, anEnum.getName());
    }
}
