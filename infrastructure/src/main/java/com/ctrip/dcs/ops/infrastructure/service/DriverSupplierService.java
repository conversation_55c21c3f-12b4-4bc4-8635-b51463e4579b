package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.QUERY_DRIVER_SUPPLIER_INFO;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.ctrip.dcs.ops.infrastructure.util.DateUtil;
import com.ctrip.dcs.ops.infrastructure.util.GrowthRateCalculator;
import com.ctrip.dcs.ops.infrastructure.util.MapUtil;
import com.ctrip.dcs.ops.infrastructure.util.MathUtil;
import com.ctrip.dcs.ops.infrastructure.value.DriverResultDTO;
import com.ctrip.dcs.ops.infrastructure.value.OpsRatioDTO;
import com.google.common.collect.Lists;

@Service
public class DriverSupplierService extends BaseQuery {

    public List<DriverResultDTO> querySupplierInfo(String partyId, Long cityId, String startDate, String endDate) {
        List<DriverResultDTO> result = Lists.newArrayList();
        LocalDate[] newQueryDate = convertQuertDate(startDate, endDate);
        LocalDate queryStartDate = newQueryDate[0];
        LocalDate[] preQueryDate = calculatePreviousInterval(newQueryDate[0], newQueryDate[1]);
        queryStartDate = getQueryStartDate(startDate, endDate, queryStartDate, preQueryDate);

        List<Map<String, Object>> queryDriverSupplierInfo = daasGatewayV2.queryDataByDaas(QUERY_DRIVER_SUPPLIER_INFO, buildParam(partyId, cityId, queryStartDate, newQueryDate));
        if (CollectionUtils.isEmpty(queryDriverSupplierInfo)) {
            return result;
        }

        Map<LocalDate, Map<String, Object>> dataMap =
            queryDriverSupplierInfo.stream().collect(Collectors.toMap(item -> DateUtil.convertStringToDate((String)item.get("hive_d"), DateUtil.YYYY_MM_DD), a -> a, (k1, k2) -> k2));

        AtomicReference<Double> ordCntGold = new AtomicReference<>(0D);
        AtomicReference<Double> ord_cnt_silver = new AtomicReference<>(0D);
        AtomicReference<Double> ord_cnt_bronze = new AtomicReference<>(0D);
        AtomicReference<Long> standardInLineNum = new AtomicReference<>(0L);
        AtomicReference<Long> standardInLineDen = new AtomicReference<>(0L);
        AtomicReference<Long> trail_standard_num = new AtomicReference<>(0L);
        AtomicReference<Long> trail_standard_den = new AtomicReference<>(0L);

        List<OpsRatioDTO> ord_cnt_gold_historyList = new ArrayList<>();
        List<OpsRatioDTO> ord_cnt_silver_historyList = new ArrayList<>();
        List<OpsRatioDTO> ord_cnt_bronze_historyList = new ArrayList<>();
        List<OpsRatioDTO> sopPassRate_historyList = new ArrayList<>();
        List<OpsRatioDTO> trackPassRate_historyList = new ArrayList<>();

        AtomicReference<Double> oldordCntGold = new AtomicReference<>(0D);
        AtomicReference<Double> oldord_cnt_silver = new AtomicReference<>(0D);
        AtomicReference<Double> oldord_cnt_bronze = new AtomicReference<>(0D);
        AtomicReference<Long> oldstandardInLineNum = new AtomicReference<>(0L);
        AtomicReference<Long> oldstandardInLineDen = new AtomicReference<>(0L);
        AtomicReference<Long> oldstrail_standard_num = new AtomicReference<>(0L);
        AtomicReference<Long> oldtrail_standard_den = new AtomicReference<>(0L);

        LocalDate finalQueryStartDate = queryStartDate;
        dataMap.forEach((date, value) -> {
            boolean isBetweenDate = isIsBetweenDate(date, newQueryDate);
            if (isBetweenDate) {
                ordCntGold.updateAndGet(v -> v + MapUtil.getKeyAsDouble(value, "ord_cnt_gold"));
                ord_cnt_silver.updateAndGet(v -> v + MapUtil.getKeyAsDouble(value, "ord_cnt_silver"));
                ord_cnt_bronze.updateAndGet(v -> v + MapUtil.getKeyAsDouble(value, "ord_cnt_bronze"));
                standardInLineNum.updateAndGet(v -> v + MapUtil.getKeyAsLong(value, "standard_in_line_num"));
                standardInLineDen.updateAndGet(v -> v + MapUtil.getKeyAsLong(value, "standard_in_line_den"));
                trail_standard_num.updateAndGet(v -> v + MapUtil.getKeyAsLong(value, "trail_standard_num"));
                trail_standard_den.updateAndGet(v -> v + MapUtil.getKeyAsLong(value, "trail_standard_den"));
            }
            boolean flag;
            if (StringUtils.equals(startDate, endDate)) {
                flag = (date.isAfter(finalQueryStartDate)) && (date.isBefore(newQueryDate[1]) || date.isEqual(newQueryDate[1]));
            } else {
                flag = isIsBetweenDate(date, newQueryDate);
            }
            if (flag) {
                ord_cnt_gold_historyList.add(getOpsRatioDTO(date, value, dataMap, "ord_cnt_gold"));
                ord_cnt_silver_historyList.add(getOpsRatioDTO(date, value, dataMap, "ord_cnt_silver"));
                ord_cnt_bronze_historyList.add(getOpsRatioDTO(date, value, dataMap, "ord_cnt_bronze"));
                sopPassRate_historyList.add(getOpsRatioDTO(date, value, dataMap, "standard_in_line_num", "standard_in_line_den"));
                trackPassRate_historyList.add(getOpsRatioDTO(date, value, dataMap, "trail_standard_num", "trail_standard_den"));
            }
            boolean b = isIsBetweenDate(date, preQueryDate);
            if (b) {
                oldordCntGold.updateAndGet(v -> v + MapUtil.getKeyAsDouble(value, "ord_cnt_gold"));
                oldord_cnt_silver.updateAndGet(v -> v + MapUtil.getKeyAsDouble(value, "ord_cnt_silver"));
                oldord_cnt_bronze.updateAndGet(v -> v + MapUtil.getKeyAsDouble(value, "ord_cnt_bronze"));
                oldstandardInLineNum.updateAndGet(v -> v + MapUtil.getKeyAsLong(value, "standard_in_line_num"));
                oldstandardInLineDen.updateAndGet(v -> v + MapUtil.getKeyAsLong(value, "standard_in_line_den"));
                oldstrail_standard_num.updateAndGet(v -> v + MapUtil.getKeyAsLong(value, "trail_standard_num"));
                oldtrail_standard_den.updateAndGet(v -> v + MapUtil.getKeyAsLong(value, "trail_standard_den"));
            }
        });

        result.add(getDriverResultDTO("ordCntGold", MathUtil.doubleAccuracy(ordCntGold.get()), ordCntGold.get(), oldordCntGold.get(), ord_cnt_gold_historyList));
        result.add(getDriverResultDTO("ordCntSilver", MathUtil.doubleAccuracy(ord_cnt_silver.get()), ord_cnt_silver.get(), oldord_cnt_silver.get(), ord_cnt_silver_historyList));
        result.add(getDriverResultDTO("ordCntBronze", MathUtil.doubleAccuracy(ord_cnt_bronze.get()), ord_cnt_bronze.get(), oldord_cnt_bronze.get(), ord_cnt_bronze_historyList));
        result.add(getDriverResultDTO("sopPassRate", GrowthRateCalculator.calPercentage(standardInLineNum.get(), standardInLineDen.get(), 2),
            GrowthRateCalculator.calPercentage(standardInLineNum.get(), standardInLineDen.get(), 2), GrowthRateCalculator.calPercentage(oldstandardInLineNum.get(), oldstandardInLineDen.get(), 2),
            sopPassRate_historyList));
        result.add(getDriverResultDTO("trackPassRate", GrowthRateCalculator.calPercentage(trail_standard_num.get(), trail_standard_den.get(), 2),
            GrowthRateCalculator.calPercentage(trail_standard_num.get().doubleValue(), trail_standard_den.get().doubleValue()),
            GrowthRateCalculator.calPercentage(oldstrail_standard_num.get().doubleValue(), oldtrail_standard_den.get().doubleValue()), trackPassRate_historyList));
        return result;
    }

    private static boolean isIsBetweenDate(LocalDate date, LocalDate[] newQueryDate) {
        return (date.isAfter(newQueryDate[0]) || date.isEqual(newQueryDate[0])) && (date.isBefore(newQueryDate[1]) || date.isEqual(newQueryDate[1]));
    }

    @NotNull
    private static DriverResultDTO getDriverResultDTO(String ordCntGold, Double ordCntGold1, Double ordCntGold2, Double oldordCntGold, List<OpsRatioDTO> ord_cnt_gold_historyList) {
        DriverResultDTO driverResultDTO1 = new DriverResultDTO();
        driverResultDTO1.setItemkey(ordCntGold);
        driverResultDTO1.setScore(ordCntGold1);
        driverResultDTO1.setDayToDayRatio(GrowthRateCalculator.calculateGrowthRate(ordCntGold2, oldordCntGold, 2));
        driverResultDTO1.setHistoryList(ord_cnt_gold_historyList.stream().sorted(Comparator.comparing(OpsRatioDTO::getDate)).toList());
        return driverResultDTO1;
    }

    private OpsRatioDTO getOpsRatioDTO(LocalDate date, Map<String, Object> value, Map<LocalDate, Map<String, Object>> dataMap, String index, String index2) {
        OpsRatioDTO opsRatioDTO = new OpsRatioDTO();
        opsRatioDTO.setScore(GrowthRateCalculator.calPercentage(MapUtil.getKeyAsDouble(value, index), MapUtil.getKeyAsDouble(value, index2)));
        if (dataMap.containsKey(date.minusDays(1))) {
            opsRatioDTO.setDayToDayRatio(GrowthRateCalculator.calculateGrowthRate(GrowthRateCalculator.calPercentage(MapUtil.getKeyAsDouble(value, index), MapUtil.getKeyAsDouble(value, index2)),
                GrowthRateCalculator.calPercentage(MapUtil.getKeyAsDouble(dataMap.get(date.minusDays(1)), index), MapUtil.getKeyAsDouble(dataMap.get(date.minusDays(1)), index2)), 2));
        }
        opsRatioDTO.setDate(date.toString());
        return opsRatioDTO;
    }

    @NotNull
    private static HashMap<String, Object> buildParam(String partyId, Long cityId, LocalDate queryStartDate, LocalDate[] newQueryDate) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("startDate", queryStartDate.toString());
        params.put("endDate", newQueryDate[1]);
        params.put("corpId", String.valueOf(partyId));
        params.put("cityId", String.valueOf(cityId));
        return params;
    }

    private static LocalDate getQueryStartDate(String startDate, String endDate, LocalDate queryStartDate, LocalDate[] preQueryDate) {
        if (StringUtils.equals(startDate, endDate)) {
            LocalDate localDate = DateUtil.convertStringToDate(startDate, DateUtil.YYYY_MM_DD);
            queryStartDate = localDate.minusDays(7);
        }

        if (preQueryDate[0].isBefore(queryStartDate)) {
            queryStartDate = preQueryDate[0];
        }
        return queryStartDate;
    }

    @NotNull
    private static OpsRatioDTO getOpsRatioDTO(LocalDate date, Map<String, Object> value, Map<LocalDate, Map<String, Object>> dataMap, String index) {
        OpsRatioDTO opsRatioDTO = new OpsRatioDTO();
        opsRatioDTO.setScore(MathUtil.doubleAccuracy(MapUtil.getKeyAsDouble(value, index)));
        if (dataMap.containsKey(date.minusDays(1))) {
            opsRatioDTO.setDayToDayRatio(GrowthRateCalculator.calculateGrowthRate(MapUtil.getKeyAsDouble(value, index), MapUtil.getKeyAsDouble(dataMap.get(date.minusDays(1)), index), 2));
        }
        opsRatioDTO.setDate(date.toString());
        return opsRatioDTO;
    }

    public static LocalDate[] calculatePreviousInterval(LocalDate startDate, LocalDate endDate) {
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        LocalDate newEndDate = startDate.minusDays(1);
        LocalDate newStartDate = newEndDate.minusDays(daysBetween);
        return new LocalDate[] {newStartDate, newEndDate};
    }
}
