package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.QUERY_OPS_TOTAL_SCORE;

import java.time.LocalDate;
import java.util.*;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.ops.infrastructure.util.DateUtil;
import com.ctrip.dcs.ops.infrastructure.value.HistoryScoreInfoDTO;

@Service
public class HistoryDataService extends OPSBaseService {

    public List<HistoryScoreInfoDTO> queryHistoryDataList(Long siteId, Long partyId) {
        List<HistoryScoreInfoDTO> resultList = new ArrayList<>();

        String queryDate = getQueryDate().getLeft();
        if (StringUtils.isBlank(queryDate)) {
            return resultList;
        }
        LocalDate localDate = DateUtil.convertStringToDate(queryDate, DateUtil.YYYY_MM_DD);
        String beforeDate = DateUtil.getBeforeDate(localDate, 30);

        HashMap<String, Object> queryBuild = new HashMap<>();
        queryBuild.put("minHiveD", beforeDate);
        queryBuild.put("maxHiveD", queryDate);
        queryBuild.put("districtId", siteId);
        queryBuild.put("vendorId", partyId);
        List<Map<String, Object>> totalData = daasGatewayV2.queryDataByDaas(QUERY_OPS_TOTAL_SCORE, queryBuild);
        if (CollectionUtils.isNotEmpty(totalData)) {
            totalData.forEach(item -> {
                HistoryScoreInfoDTO historyScoreInfoDTO = new HistoryScoreInfoDTO();
                historyScoreInfoDTO.setScore((Double)item.get("ops_total_score"));
                historyScoreInfoDTO.setDate((String)item.get("hive_d"));
                resultList.add(historyScoreInfoDTO);
            });
        }
        resultList.sort(Comparator.comparing(HistoryScoreInfoDTO::getDate));
        return resultList;
    }
}
