package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.QUERY_MAX_DATE;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import com.ctrip.basebiz.implus.vendor.contract.GetVenAgentStatusRequestType;
import com.ctrip.basebiz.implus.vendor.contract.GetVenAgentStatusResponseType;
import com.ctrip.basebiz.implus.vendor.contract.VendorAgentStatus;
import com.ctrip.dcs.ops.infrastructure.config.OpsCommonConfig;
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2;
import com.ctrip.dcs.ops.infrastructure.gateway.IMPlusVendorServiceProxy;
import com.ctrip.dcs.ops.infrastructure.util.DateUtil;
import com.google.common.collect.Lists;

public abstract class OPSBaseService {
    @Autowired
    public DaasGatewayV2 daasGatewayV2;
    @Autowired
    public IMPlusVendorServiceProxy imPlusVendorServiceProxy;
    @Autowired
    public OpsCommonConfig opsCommonConfig;

    public Pair<String, String> getQueryDate() {
        List<Map<String, Object>> queriedDataByDaas = daasGatewayV2.queryDataByDaas(QUERY_MAX_DATE, new HashMap<>());
        if (queriedDataByDaas.isEmpty()) {
            return Pair.of(StringUtils.EMPTY, StringUtils.EMPTY);
        }
        String hiveD = (String)queriedDataByDaas.stream().findFirst().get().get("result");
        LocalDate localDate = DateUtil.convertStringToDate(hiveD, DateUtil.YYYY_MM_DD);
        String beforeDate = DateUtil.getBeforeDate(localDate, opsCommonConfig.getDaysToDemote());
        String updateTime = DateUtil.convertDateToString((Long)queriedDataByDaas.stream().findFirst().get().get("updateTime"), DateUtil.YYYY_MM_DD_HH_MM_SS);
        LocalDateTime localDateTime = DateUtil.convertStringToDateTime(updateTime, DateUtil.YYYY_MM_DD_HH_MM_SS);
        String beforeDateTime = DateUtil.getBeforeDateTime(localDateTime, opsCommonConfig.getDaysToDemote());
        return Pair.of(beforeDate, beforeDateTime);
    }

    public Boolean setWorkStatus(String uid) {
        GetVenAgentStatusRequestType request = new GetVenAgentStatusRequestType();
        request.setUidList(Lists.newArrayList(uid));
        GetVenAgentStatusResponseType responseType = imPlusVendorServiceProxy.getVenAgentStatus(request);
        Map<String, VendorAgentStatus> statusList = responseType.getStatusList();
        if (MapUtils.isNotEmpty(statusList)) {
            VendorAgentStatus vendorAgentStatus = statusList.get(uid);
            Integer isWorkTime = vendorAgentStatus.getIsWorkTime();
            return BooleanUtils.toBoolean(isWorkTime);
        }
        return false;
    }

}
