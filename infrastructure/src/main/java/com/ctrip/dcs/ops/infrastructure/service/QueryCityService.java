package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*;

import java.util.*;
import java.util.stream.Collectors;

import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2;
import com.ctrip.dcs.ops.infrastructure.util.MapUtil;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.ops.infrastructure.constant.OpsExamPlatformEnum;
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV1;
import com.ctrip.dcs.ops.infrastructure.util.DateUtil;

@Service
public class QueryCityService {
    @Autowired
    private DaasGatewayV1 daasGatewayV1;

    @Autowired
    private DaasGatewayV2 daasGatewayV2;


    public Set<String> queryCityListList(String type, String partyId) {
        OpsExamPlatformEnum platformEnum = OpsExamPlatformEnum.getEnum(type);
        Set<String> resultMonth = Set.of();
        if (platformEnum != null) {
            switch (platformEnum) {
                case PLATFORM:
                    resultMonth = queryCityListByPlatform(partyId);
                    break;
                case TRIPCAR:
                    resultMonth = queryCityListByTripCar(partyId);
                    break;
                case DRIVERSCORE:
                    resultMonth = queryCityListByDriverScore(partyId);
                    break;
                case CIRCUIT:
                    resultMonth = queryCityListByCircuit(partyId);
                default:
                    break;
            }
        }
        return resultMonth;
    }

    private Set<String> queryCityListByCircuit(String partyId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("corpIdUse", partyId);
        List<Map<String, Object>> cityList = daasGatewayV2.queryDataByDaas("queryCityByCircuit", params);
        if (CollectionUtils.isNotEmpty(cityList)) {
            return cityList.stream().map(item -> MapUtil.getKeyAsLong(item, "city_id")).sorted().toList().stream().map(String::valueOf).collect(Collectors.toSet());
        }
        return Sets.newHashSet();
    }

    private Set<String> queryCityListByDriverScore(String partyId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("supplierId", partyId);
        List<Map<String, Object>> cityList = daasGatewayV2.queryDataByDaas("queryDriverScoreCityBySupplier", params);
        if (CollectionUtils.isNotEmpty(cityList)) {
            return cityList.stream().map(item -> MapUtil.getKeyAsLong(item, "city_id")).sorted().toList().stream().map(String::valueOf).collect(Collectors.toSet());
        }
        return Sets.newHashSet();
    }
    private Set<String> queryCityListByPlatform(String partyId) {
        return queryCityList(partyId, QUERY_CITY_LIST_PLATFORM_MI, QUERY_CITY_LIST_PLATFORM_DF);
    }

    private Set<String> queryCityListByTripCar(String partyId) {
        return queryCityList(partyId, QUERY_CITY_LIST_TRIPCAR_MI, QUERY_CITY_LIST_TRIPCAR_DF);
    }

    private Set<String> queryCityList(String partyId, String apiNameMi, String apiNameDf) {
        // 从固化表中查询出该供应商所有的考核周期
        String lastMonth = DateUtil.getLastMonth();
        String currentMonth = DateUtil.getCurrentMonth();

        HashMap<String, Object> params = new HashMap<>();
        params.put("corpIdUse", Integer.valueOf(partyId));
        Set<String> resultMonth = new HashSet<>();
        List<Map<String, Object>> resultMiList = daasGatewayV1.queryDataByDaas(apiNameMi, params);
        List<Map<String, Object>> resultDfList = daasGatewayV1.queryDataByDaas(apiNameDf, params);
        if (CollectionUtils.isNotEmpty(resultMiList)) {
            Set<String> assessMonth = resultMiList.stream().map(item -> (String)item.get("use_city_id")).collect(Collectors.toSet());
            resultMonth.addAll(assessMonth);
        }

        if (CollectionUtils.isNotEmpty(resultDfList)) {
            resultDfList.forEach(item -> {
                String assessMonth = (String)item.get("assess_month");
                String useCityId = (String)item.get("use_city_id");
                if (assessMonth.equals(lastMonth) || assessMonth.equals(currentMonth)) {
                    resultMonth.add(useCityId);
                }
            });

        }
        return resultMonth;
    }
}
