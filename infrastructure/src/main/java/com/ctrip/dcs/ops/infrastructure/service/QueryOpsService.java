package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.luna.Log;
import com.ctrip.dcs.ops.infrastructure.config.DaasMetaDataConfig;
import com.ctrip.dcs.ops.infrastructure.config.OpsExamFieldConfig;
import com.ctrip.dcs.ops.infrastructure.config.OpsExamSummaryFieldConfig;
import com.ctrip.dcs.ops.infrastructure.config.OpsSourceConfig;
import com.ctrip.dcs.ops.infrastructure.config.dto.OpsExamFieldDTO;
import com.ctrip.dcs.ops.infrastructure.constant.OpsExamPlatformEnum;
import com.ctrip.dcs.ops.infrastructure.constant.OpsExamStatusEnum;
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV1;
import com.ctrip.dcs.ops.infrastructure.util.DateUtil;
import com.ctrip.dcs.ops.infrastructure.util.GrowthRateCalculator;
import com.ctrip.dcs.ops.infrastructure.util.MathUtil;
import com.ctrip.dcs.ops.infrastructure.value.OpsExamResultDTO;
import com.ctrip.dcs.ops.infrastructure.value.OpsRatioDTO;
import com.ctrip.dcs.ops.infrastructure.value.SummaryDTO;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

@Service
public class QueryOpsService {
    private final static Log log = Log.getInstance(QueryOpsService.class);
    private static final List<String> RATIOLIST = Arrays.asList("enquiry_result_rate_cn", "enquiry_result_rate_trip");
    @Autowired
    private DaasGatewayV1 daasGatewayV1;
    @Autowired
    private DaasMetaDataConfig daasMetaDataConfig;
    @Autowired
    private OpsExamFieldConfig opsExamFieldConfig;
    @Autowired
    private OpsExamSummaryFieldConfig opsExamSummaryFieldConfig;
    @Autowired
    private OpsSourceConfig opsSourceConfig;

    private static HashMap<String, Object> buildParam(String partyId, String period, Long cityId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("assessMonth", period);
        params.put("corpIdUse", Integer.valueOf(partyId));
        params.put("useCityId", String.valueOf(cityId));
        return params;
    }

    private static List<OpsExamResultDTO> getOpsExamResultData(Map<String, List<OpsExamResultDTO>> itemMap, AtomicReference<String> maxHiveDate) {
        // 获取最大日期，获取最大日期的list
        Map<String, List<OpsExamResultDTO>> dateMap = itemMap.values().stream().flatMap(Collection::stream).collect(Collectors.groupingBy(OpsExamResultDTO::getDate));
        List<OpsExamResultDTO> opsExamResultDataList = dateMap.get(maxHiveDate.get());
        opsExamResultDataList.forEach(item -> {
            List<OpsExamResultDTO> historyList = itemMap.get(item.getItemkey());
            List<OpsRatioDTO> list = historyList.stream().map(history -> {
                OpsRatioDTO opsRatio = new OpsRatioDTO();
                opsRatio.setScore(history.getScore());
                opsRatio.setDayToDayRatio(history.getDayToDayRatio());
                opsRatio.setDate(history.getDate());
                return opsRatio;
            }).toList();
            item.setHistoryList(list);
        });
        opsExamResultDataList.sort(Comparator.comparing(OpsExamResultDTO::getOrder));
        return opsExamResultDataList;
    }

    public boolean checkInUnExam(String partyId, String period, Long cityId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("effectiveMonth", period);
        params.put("corpIdUse", partyId);
        params.put("cityId", String.valueOf(cityId));

        // 查询出考核月份内所有的对于供应商的该城市的考核结果列表（每天一条数据）
        List<Map<String, Object>> unexams = daasGatewayV1.queryDataByDaas(CHECK_IS_IN_UNEXAM, params);
        if (CollectionUtils.isNotEmpty(unexams)) {
            Integer cnt = (Integer)unexams.getFirst().get("cnt");
            // 在汰换表中，直接返回
            return cnt > 0;
        }
        return false;
    }

    public Pair<SummaryDTO, List<OpsExamResultDTO>> getSummaryListPair(OpsExamPlatformEnum type, String partyId, String period, Long cityId) {
        if (type != null) {
            switch (type) {
                case PLATFORM:
                    return queryResultDetailByPlatform(partyId, period, cityId);
                case TRIPCAR:
                    return queryResultDetailByTripCar(partyId, period, cityId);
                default:
                    break;
            }
        }
        return new ImmutablePair<>(null, null);
    }

    private Pair<SummaryDTO, List<OpsExamResultDTO>> queryResultDetailByTripCar(String partyId, String period, Long cityId) {
        return queryData(partyId, period, cityId, OpsExamPlatformEnum.TRIPCAR, QUERY_RESULT_DETAIL_TRIPCAR_DF, QUERY_RESULT_DETAIL_TRIPCAR_MI);
    }

    private Pair<SummaryDTO, List<OpsExamResultDTO>> queryResultDetailByPlatform(String partyId, String period, Long cityId) {
        return queryData(partyId, period, cityId, OpsExamPlatformEnum.PLATFORM, QUERY_RESULT_DETAIL_PLATFORM_DF, QUERY_RESULT_DETAIL_PLATFORM_MI);
    }

    private Pair<SummaryDTO, List<OpsExamResultDTO>> queryData(String partyId, String period, Long cityId, OpsExamPlatformEnum platform, String queryDf, String queryMi) {
        // 先查询该月份是否在汰换表中
        // 然后判断该月份是否是历史月份
        // 如果是历史月份，则直接查询固化表
        // 如果是当前月份，则查询临时表
        SummaryDTO summary = new SummaryDTO();
        summary.setDownloadUrl(opsSourceConfig.getSourceInfo(platform.getType()).getDownloadUrl());
        if (DateUtil.getCurrentMonth().equals(period)) {
            log.info("isCurrentMonth", period);
            return queryDataCurrentMonth(platform, partyId, period, cityId, summary, queryDf);
        }
        return queryDataHistoryMonth(platform, partyId, period, cityId, summary, queryDf, queryMi);
    }

    private Pair<SummaryDTO, List<OpsExamResultDTO>> queryDataHistoryMonth(OpsExamPlatformEnum platform, String partyId, String period, Long cityId, SummaryDTO summary, String queryDf, String queryMi) {
        // 历史月份
        HashMap<String, Object> params = buildParam(partyId, period, cityId);
        List<Map<String, Object>> queriedDataByDaasList = daasGatewayV1.queryDataByDaas(queryMi, params);
        if (CollectionUtils.isNotEmpty(queriedDataByDaasList)) {
            return getSummaryListPair(platform, period, queriedDataByDaasList, summary);
        }
        // 默认限制只有上个月的会走兜底到非固化表查询
        boolean queryDataDf = (StringUtils.equals(DateUtil.getLastMonth(), period) && daasMetaDataConfig.isLimitLastMonth()) || (BooleanUtils.isFalse(daasMetaDataConfig.isLimitLastMonth()));
        if (queryDataDf) {
            // 判断直接从最大的hive_d中查询还是指定的hive_d
            params.put("fixeddate", "true");
            if (BooleanUtils.isTrue(daasMetaDataConfig.demoteNotHiveD())) {
                String beforeDate = DateUtil.getBeforeDate(LocalDate.now(), daasMetaDataConfig.getDaysToDemote());
                params.put("hiveD", beforeDate);
            }
            queriedDataByDaasList = daasGatewayV1.queryDataByDaas(queryDf, params);
            return getSummaryListPair(platform, period, queriedDataByDaasList, summary);
        }
        return new ImmutablePair<>(summary, new ArrayList<>());
    }

    private Pair<SummaryDTO, List<OpsExamResultDTO>> queryDataCurrentMonth(OpsExamPlatformEnum platform, String partyId, String period, Long cityId, SummaryDTO summary, String queryDataDf) {
        // 当前月份,需要计算环比
        HashMap<String, Object> params = buildParam(partyId, period, cityId);
        List<Map<String, Object>> resultDetailDfList = daasGatewayV1.queryDataByDaas(queryDataDf, params);
        List<OpsExamFieldDTO> fieldList = opsExamFieldConfig.getFieldList(platform.getType());
        List<OpsExamResultDTO> resultList = new ArrayList<>();
        AtomicReference<Double> serviceProviderOverallScore = new AtomicReference<>();
        AtomicReference<String> serviceProviderLevel = new AtomicReference<>();
        AtomicReference<String> maxHiveDate = new AtomicReference<>("1970-01-01");
        if (CollectionUtils.isEmpty(resultDetailDfList)) {
            return Pair.of(summary, resultList);
        }
        resultDetailDfList.forEach(item -> {
            String hiveD = (String)item.get("hive_d");
            setOverScoreAndLevel(platform, item, hiveD, maxHiveDate, serviceProviderOverallScore, serviceProviderLevel);
            setField(fieldList, resultList, item, hiveD);
        });
        Map<String, List<OpsExamResultDTO>> itemMap = calDayToDayRatio(resultList);

        List<OpsExamResultDTO> opsExamResultDataList = getOpsExamResultData(itemMap, maxHiveDate);

        Long datachangeLasttime = (Long)resultDetailDfList.getFirst().get(opsExamSummaryFieldConfig.getFieldList(platform.getType()).getDatachangeLasttime());
        long daysBetween = DateUtil.daysBetween(datachangeLasttime);
        Cat.logEvent("daysBetween", String.valueOf(daysBetween));
        Cat.logEvent("dcs:daysBetween" + queryDataDf, String.valueOf(daysBetween));
        summary.setExamScope(String.format("%s-%s", DateUtil.getFirstDayOfMonth(DateUtil.YYYYMMDD), DateUtil.getCurrentDay(DateUtil.YYYYMMDD)));
        summary.setUpdateTime(DateUtil.convertDateToString(datachangeLasttime, DateUtil.YYYY_MM_DD_HH_MM_SS));
        summary.setExamStatus(OpsExamStatusEnum.NORMAL.getCode());
        summary.setRemainingDays(DateUtil.getRemainingDays());
        summary.setOverallScore(MathUtil.serDoubleAccuracy(serviceProviderOverallScore.get(), daasMetaDataConfig.scaleInt()));
        summary.setScoreLevel(serviceProviderLevel.get());
        return Pair.of(summary, opsExamResultDataList);
    }

    private void setOverScoreAndLevel(OpsExamPlatformEnum platform, Map<String, Object> item, String hiveD, AtomicReference<String> maxHiveDate, AtomicReference<Double> serviceProviderOverallScore,
        AtomicReference<String> serviceProviderLevel) {
        if (DateUtil.isAfter(hiveD, maxHiveDate.get())) {
            maxHiveDate.set(hiveD);
            serviceProviderOverallScore.set((Double)item.get(opsExamSummaryFieldConfig.getFieldList(platform.getType()).getOverallScore()));
            serviceProviderLevel.set(replaceWord((String)item.get(opsExamSummaryFieldConfig.getFieldList(platform.getType()).getProviderLevel())));
        }
    }

    private Map<String, List<OpsExamResultDTO>> calDayToDayRatio(List<OpsExamResultDTO> resultList) {
        // 按照指标计算环比
        Map<String, List<OpsExamResultDTO>> itemMap = Optional.ofNullable(resultList).orElse(Lists.newArrayList()).stream().collect(Collectors.groupingBy(OpsExamResultDTO::getItemkey));
        Optional.ofNullable(itemMap).orElse(Maps.newHashMap()).forEach((key, value) -> {
            if (CollectionUtils.isEmpty(value) || value.size() == 1) {
                return;
            }
            value.sort(Comparator.comparing(OpsExamResultDTO::getDate));
            for (int index = 1; index < value.size(); index++) {
                OpsExamResultDTO pre = value.get(index - 1);
                OpsExamResultDTO cur = value.get(index);
                Double growthRate = GrowthRateCalculator.calculateGrowthRate(cur.getScore(), pre.getScore());
                cur.setDayToDayRatio(MathUtil.serDoubleAccuracy(growthRate, daasMetaDataConfig.scaleInt()));
            }
        });
        return itemMap;
    }

    private void setField(List<OpsExamFieldDTO> fieldList, List<OpsExamResultDTO> resultList, Map<String, Object> item, String hiveD) {
        Optional.ofNullable(fieldList).orElse(Lists.newArrayList()).forEach(field -> {
            Double score = 0.0;
            if (StringUtils.equals(field.getType(), "double")) {
                if (RATIOLIST.contains(field.getKey())) {
                    score = Objects.nonNull(item.get(field.getKey())) ? MathUtil.serDoubleAccuracy((Double)item.get(field.getKey()) * 100, daasMetaDataConfig.scaleInt()) : null;
                } else {
                    score = MathUtil.serDoubleAccuracy(Double.valueOf(String.valueOf(item.get(field.getKey()))), daasMetaDataConfig.scaleInt());
                }
            } else if (StringUtils.equals(field.getType(), "long")) {
                score = MathUtil.serDoubleAccuracy(Double.valueOf(String.valueOf(item.get(field.getKey()))), daasMetaDataConfig.scaleInt());
            }
            OpsExamResultDTO opsExamResultData = new OpsExamResultDTO();
            opsExamResultData.setItemName(field.getItemName());
            opsExamResultData.setItemkey(field.getResultKey());
            opsExamResultData.setParentKey(field.getParentKey());
            opsExamResultData.setDate(hiveD);
            opsExamResultData.setScore(score);
            opsExamResultData.setOrder(field.getOrder());
            resultList.add(opsExamResultData);
        });
        resultList.sort(Comparator.comparing(OpsExamResultDTO::getOrder));
    }

    private Pair<SummaryDTO, List<OpsExamResultDTO>> getSummaryListPair(OpsExamPlatformEnum platform, String period, List<Map<String, Object>> queriedDataByDaasList, SummaryDTO summary) {
        if (CollectionUtils.isEmpty(queriedDataByDaasList)) {
            return Pair.of(summary, new ArrayList<>());
        }
        List<OpsExamResultDTO> resultList = new ArrayList<>();
        Map<String, Object> stringObjectMap = queriedDataByDaasList.getFirst();
        String hiveD = (String)stringObjectMap.get("hive_d");
        Double serviceProviderOverallScore = (Double)stringObjectMap.get(opsExamSummaryFieldConfig.getFieldList(platform.getType()).getOverallScore());
        String serviceProviderLevel = replaceWord((String)stringObjectMap.get(opsExamSummaryFieldConfig.getFieldList(platform.getType()).getProviderLevel()));

        List<OpsExamFieldDTO> fieldList = opsExamFieldConfig.getFieldList(platform.getType());
        setField(fieldList, resultList, stringObjectMap, hiveD);

        Long datachangeLasttime = (Long)stringObjectMap.get(opsExamSummaryFieldConfig.getFieldList(platform.getType()).getDatachangeLasttime());
        summary.setExamScope(String.format("%s-%s", DateUtil.getFirstDayOfMonth(period, DateUtil.YYYYMMDD), DateUtil.getLastDayOfMonth(period, DateUtil.YYYYMMDD)));
        summary.setUpdateTime(DateUtil.convertDateToString(datachangeLasttime, DateUtil.YYYY_MM_DD_HH_MM_SS));
        summary.setExamStatus(OpsExamStatusEnum.EXAMED.getCode());
        summary.setRemainingDays(DateUtil.getRemainingDays());
        summary.setOverallScore(MathUtil.serDoubleAccuracy(serviceProviderOverallScore, daasMetaDataConfig.scaleInt()));
        summary.setScoreLevel(serviceProviderLevel);
        return Pair.of(summary, resultList);
    }

    public String replaceWord(String word) {
        return daasMetaDataConfig.getLevelList().contains(word) ? "-" : word;
    }
}
