package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.QUERY_OPS_SITE_LIST;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.basebiz.geolocation.service.GetLocationInfosRequestType;
import com.ctrip.basebiz.geolocation.service.GetLocationInfosResponseType;
import com.ctrip.basebiz.geolocation.service.LocationInfo;
import com.ctrip.basebiz.geolocation.service.LocationKey;
import com.ctrip.dcs.ops.infrastructure.config.OpsCommonConfig;
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV1;
import com.ctrip.dcs.ops.infrastructure.gateway.GeoLocationServiceProxy;
import com.ctrip.dcs.ops.infrastructure.value.CityDTO;
import com.ctrip.dcs.ops.infrastructure.value.ProvinceInfoDTO;
import com.ctrip.dcs.ops.infrastructure.value.SiteInfoDTO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

@Service
public class QuerySiteService extends OPSBaseService {
    @Autowired
    private DaasGatewayV1 daasGatewayV1;
    @Autowired
    private GeoLocationServiceProxy geoLocationServiceProxy;
    @Autowired
    private OpsCommonConfig opsCommonConfig;

    public List<SiteInfoDTO> querySiteList(String type, String partyId, String locale) {
        String queryDate = getQueryDate().getLeft();
        if (StringUtils.isBlank(queryDate)) {
            return new ArrayList<>();
        }
        String apiName = "";
        HashMap<String, Object> params = new HashMap<>();
        if (StringUtils.equals(type, ParentCategoryEnum.DAY.getCode())) {
            apiName = QUERY_OPS_SITE_LIST;
            params.put("vendorId", partyId);
            params.put("hiveD", queryDate);
        }
        Map<Long, Map<Long, Set<Long>>> countryMap = querySiteInfo(daasGatewayV1.queryDataByDaas(apiName, params));
        if (MapUtils.isEmpty(countryMap)) {
            return Lists.newArrayList();
        }
        Map<Long, String> nameMap = querySiteName(locale, countryMap);
        return setSiteName(countryMap, nameMap);
    }

    private List<SiteInfoDTO> setSiteName(Map<Long, Map<Long, Set<Long>>> countryMap, Map<Long, String> nameMap) {
        List<SiteInfoDTO> result = Lists.newArrayList();
        countryMap.forEach((countryId, provinceMap) -> {
            SiteInfoDTO siteInfoDTO = new SiteInfoDTO();
            siteInfoDTO.setCountryId(countryId);
            siteInfoDTO.setCountryName(nameMap.get(countryId));
            siteInfoDTO.setDisable(opsCommonConfig.getCountryDisable());
            List<ProvinceInfoDTO> provinceInfoDTOS = new ArrayList<>();
            Optional.ofNullable(provinceMap).orElse(Maps.newHashMap()).forEach((provinceId, cityList) -> {
                ProvinceInfoDTO provinceInfoDTO = new ProvinceInfoDTO();
                provinceInfoDTO.setProvinceId(provinceId);
                provinceInfoDTO.setProvinceName(nameMap.get(provinceId));
                provinceInfoDTO.setDisable(opsCommonConfig.getProvinceDisable());
                provinceInfoDTO.setCityDTOS(cityList.stream().map(cityId -> {
                    CityDTO cityDTO = new CityDTO();
                    cityDTO.setCityId(cityId);
                    cityDTO.setCityName(nameMap.get(cityId));
                    cityDTO.setDisable(!BooleanUtils
                        .isTrue(BooleanUtils.isFalse(opsCommonConfig.getCityWhiteListDisable()) || (CollectionUtils.isNotEmpty(opsCommonConfig.getCityWhiteList()) && opsCommonConfig.getCityWhiteList().contains(cityId))));
                    return cityDTO;
                }).toList());

                provinceInfoDTOS.add(provinceInfoDTO);
            });
            siteInfoDTO.setProvinceList(provinceInfoDTOS);
            result.add(siteInfoDTO);
        });
        return result;
    }

    private Map<Long, String> querySiteName(String locale, Map<Long, Map<Long, Set<Long>>> countryMap) {
        List<Long> allIdList = countryMap.entrySet().stream()
            .flatMap(
                outerEntry -> Stream.concat(Stream.of(outerEntry.getKey()), outerEntry.getValue().entrySet().stream().flatMap(innerEntry -> Stream.concat(Stream.of(innerEntry.getKey()), innerEntry.getValue().stream()))))
            .toList();
        Map<Long, String> nameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allIdList)) {
            List<List<Long>> partitionIdList = Lists.partition(allIdList, 50);
            partitionIdList.forEach(idList -> {
                GetLocationInfosRequestType requestType = new GetLocationInfosRequestType();
                requestType.setKeys(buildParam(idList));
                requestType.setLocale(locale);
                GetLocationInfosResponseType responseType = geoLocationServiceProxy.getLocationInfos(requestType);
                Map<Long, String> transNameMap = Optional.ofNullable(responseType.getLocationInfos()).orElse(Lists.newArrayList()).stream()
                    .collect(Collectors.toMap(LocationInfo::getGlobalid, locationInfo -> StringUtils.isBlank(locationInfo.getTranslation()) ? locationInfo.getName() : locationInfo.getTranslation(), (k1, k2) -> k2));
                nameMap.putAll(transNameMap);
            });
        }
        return nameMap;
    }

    private Map<Long, Map<Long, Set<Long>>> querySiteInfo(List<Map<String, Object>> queriedDataByDaas) {
        Map<Long, Map<Long, Set<Long>>> countryMap = new HashMap<>();
        Optional.ofNullable(queriedDataByDaas).orElse(Lists.newArrayList()).forEach(data -> {
            Integer countryIdStr = (Integer)data.get("country_id");
            Long countryId = Long.valueOf(countryIdStr);
            Map<Long, Set<Long>> provinceInfo = countryMap.get(countryId);
            if (provinceInfo == null) {
                provinceInfo = new HashMap<>();
                Integer provinceIdStr = (Integer)data.get("province_id");
                setProvinceInfo(data, provinceInfo, Long.valueOf(provinceIdStr));
                countryMap.put(countryId, provinceInfo);
            } else {
                Integer provinceIdStr = (Integer)data.get("province_id");
                Long provinceId = Long.valueOf(provinceIdStr);
                Set<Long> cityList = provinceInfo.get(provinceId);
                if (CollectionUtils.isNotEmpty(cityList)) {
                    String cityIdList = (String)data.get("city_id_list");
                    String[] split = StringUtils.split(cityIdList, "-");
                    if (ArrayUtils.isNotEmpty(split)) {
                        for (String cityId : split) {
                            cityList.add(Long.valueOf(cityId));
                            provinceInfo.put(provinceId, cityList);
                        }
                    }
                } else {
                    setProvinceInfo(data, provinceInfo, provinceId);
                }

            }
        });
        return countryMap;
    }

    private List<LocationKey> buildParam(List<Long> idList) {
        return Optional.ofNullable(idList).orElse(Lists.newArrayList()).stream().map(id -> {
            LocationKey locationKey = new LocationKey();
            locationKey.setGlobalid(id);
            locationKey.setGeocategoryid(6);
            locationKey.setType("gs_district");
            return locationKey;
        }).toList();
    }

    private void setProvinceInfo(Map<String, Object> data, Map<Long, Set<Long>> provinceInfo, Long provinceId) {
        String cityIdList = (String)data.get("city_id_list");
        String[] split = StringUtils.split(cityIdList, "-");
        if (ArrayUtils.isNotEmpty(split)) {
            Set<Long> cityIds = Sets.newHashSet();
            for (String cityId : split) {
                cityIds.add(Long.valueOf(cityId));
                provinceInfo.put(provinceId, cityIds);
            }
        }
    }
}
