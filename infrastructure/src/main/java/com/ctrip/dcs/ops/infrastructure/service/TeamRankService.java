package com.ctrip.dcs.ops.infrastructure.service;

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.QUERY_CNT_TOTAL;
import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.QUERY_TOTAL_DATA_BY_PAGE;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.ops.infrastructure.value.PageDTO;
import com.ctrip.dcs.ops.infrastructure.value.TeamInfoDTO;
import com.ctrip.dcs.ops.infrastructure.value.TeamInfoPageDTO;

@Service
public class TeamRankService extends OPSBaseService {
    public TeamInfoPageDTO  queryTeamRankList(String partyId, Long siteId, Integer pageNo, Integer size) {
        TeamInfoPageDTO teamInfoPageDTO = new TeamInfoPageDTO();
        List<TeamInfoDTO> teamInfoDTOS = new ArrayList<>();
        String queryDate = getQueryDate().getLeft();
        if (StringUtils.isBlank(queryDate)) {
            return teamInfoPageDTO;
        }

        HashMap<String, Object> params = new HashMap<>();
        params.put("hiveD", queryDate);
        params.put("districtId", siteId);
        params.put("offsetCnt", (pageNo - 1) * size);
        params.put("limitCnt", size);

        List<Map<String, Object>> maps = daasGatewayV2.queryDataByDaas(QUERY_TOTAL_DATA_BY_PAGE, params);
        if (CollectionUtils.isNotEmpty(maps)) {
            maps.forEach(item -> {
                Long vendorId = Long.valueOf(String.valueOf(item.get("vendor_id")));
                String advisorName = (String)item.get("advisor_name");
                Integer opsRank = (Integer)item.get("ops_rank");
                TeamInfoDTO teamInfoDTO = new TeamInfoDTO();
                teamInfoDTO.setTeamName(advisorName);
                teamInfoDTO.setRank(opsRank);
                if (vendorId.equals(Long.valueOf(partyId))) {
                    teamInfoDTO.setIsSelfTeam(true);
                    teamInfoDTO.setIsInWork(setWorkStatus((String)item.get("uid")));
                }
                teamInfoDTOS.add(teamInfoDTO);
            });
        }

        teamInfoPageDTO.setTeamInfoDTOS(teamInfoDTOS);
        PageDTO pageDTO = new PageDTO();
        HashMap<String, Object> params1 = new HashMap<>();
        params1.put("hiveD", queryDate);
        params1.put("districtId", siteId);
        List<Map<String, Object>> maps1 = daasGatewayV2.queryDataByDaas(QUERY_CNT_TOTAL, params1);
        if (CollectionUtils.isNotEmpty(maps1)) {
            Integer total = (Integer)maps1.stream().findFirst().get().get("cnt");
            pageDTO.setTotalSize(total);
            pageDTO.setTotalPages((total / size) + ((total % size) == 0 ? 0 : 1));
        }
        pageDTO.setPageNo(pageNo);
        pageDTO.setPageSize(size);

        teamInfoPageDTO.setPageDTO(pageDTO);
        return teamInfoPageDTO;
    }
}
