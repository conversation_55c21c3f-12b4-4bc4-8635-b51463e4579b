package com.ctrip.dcs.ops.infrastructure.service.indexhandler;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

import com.ctrip.dcs.ops.infrastructure.constant.OpsConstants;
import com.ctrip.dcs.ops.infrastructure.util.MathUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class IsNewcomerIndexHandler implements IndexHandler {

    @Override
    public Boolean isSupport(Map<String, Object> item, Map<String, Object> detailData,String factorValue) {
        return StringUtils.equals((String) detailData.get("index_id_l3"), "newcomer") && StringUtils.equals((String) item.get("factor_id"), "is_new");
    }

    @Override
    public String process(Map<String, Object> item, Map<String, Object> detailData,String factorValue) {
        factorValue = Objects.requireNonNull(MathUtil.serDoubleAccuracy(Double.valueOf(String.valueOf(item.get("factor_value"))), 0)).toString();
        if (new BigDecimal(factorValue).compareTo(BigDecimal.valueOf(0)) == 0) {
            factorValue = OpsConstants.NO;
        } else {
            factorValue = OpsConstants.YES;
        }
        return factorValue;
    }
}
