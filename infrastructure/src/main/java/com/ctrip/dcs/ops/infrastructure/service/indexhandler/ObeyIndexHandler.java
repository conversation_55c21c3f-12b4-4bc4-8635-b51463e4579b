package com.ctrip.dcs.ops.infrastructure.service.indexhandler;

import java.math.BigDecimal;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class ObeyIndexHandler implements IndexHandler {

    @Override
    public Boolean isSupport(Map<String, Object> item, Map<String, Object> detailData,String factorValue) {
        return StringUtils.equals((String) detailData.get("index_id_l3"), "obey") && new BigDecimal(factorValue).compareTo(BigDecimal.valueOf(0)) == 0;
    }

    @Override
    public String process(Map<String, Object> item, Map<String, Object> detailData,String factorValue) {
        return null;
    }
}
