package com.ctrip.dcs.ops.infrastructure.service.indexhandler;

import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.ops.infrastructure.util.MathUtil;

@Service
public class RemainDaysIndexHandler implements IndexHandler {
    @Override
    public Boolean isSupport(Map<String, Object> item, Map<String, Object> detailData, String factorValue) {
        return StringUtils.equals((String)detailData.get("index_id_l3"), "newcomer") && StringUtils.equals((String)item.get("factor_id"), "remain_days");
    }

    @Override
    public String process(Map<String, Object> item, Map<String, Object> detailData, String factorValue) {
        return String.valueOf(Objects.requireNonNull(MathUtil.serDoubleAccuracy(Double.valueOf(String.valueOf(item.get("factor_value"))), 0)).longValue());
    }
}
