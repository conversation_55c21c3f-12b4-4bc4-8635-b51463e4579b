package com.ctrip.dcs.ops.infrastructure.service.indexhandler;

import java.util.Map;
import java.util.Objects;

import com.ctrip.dcs.ops.infrastructure.util.MathUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class SaleIndexHandler implements IndexHandler {

    @Override
    public Boolean isSupport(Map<String, Object> item, Map<String, Object> detailData,String factorValue) {
        return StringUtils.equals((String) detailData.get("index_id_l3"), "sale_30d")
                && (StringUtils.equals((String) item.get("factor_id"), "not_sj_amount") || StringUtils.equals((String) item.get("factor_id"), "sj_amount"));
    }

    @Override
    public String process(Map<String, Object> item, Map<String, Object> detailData,String factorValue) {
        return Objects.requireNonNull(MathUtil.serDoubleAccuracy(Double.valueOf(String.valueOf(item.get("factor_value"))), 0)).toString();
    }
}
