package com.ctrip.dcs.ops.infrastructure.util;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

public final class DateUtil {

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYYMMDD = "yyyy/MM/dd";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 获取上个月月份
     * 
     * @return
     */
    public static String getLastMonth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, -1);
        date = calendar.getTime();
        return format.format(date);
    }

    /**
     * 获取本月月份
     *
     * @return
     */
    public static String getCurrentMonth() {
        LocalDate currentDate = LocalDate.now();
        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 格式化日期
        return currentDate.format(formatter);
    }

    /**
     * 获取当天日期
     *
     * @return
     */
    public static String getCurrentDay(String pattern) {
        LocalDate currentDate = LocalDate.now();
        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        // 格式化日期
        return currentDate.format(formatter);
    }

    public static String convertDateToString(Long timestamp, String pattern) {
        // 将时间戳转换为LocalDateTime
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());

        // 创建日期时间格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        // 格式化日期时间
        return dateTime.format(formatter);
    }

    public static String convertDateToString(LocalDate localDate, String pattern) {
        // 创建日期时间格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        // 格式化日期时间
        return localDate.format(formatter);
    }

    public static long getRemainingDays() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取当前月份的最后一天
        LocalDate endOfMonth = currentDate.withDayOfMonth(currentDate.lengthOfMonth());
        // 计算当前日期到月底的天数
        return ChronoUnit.DAYS.between(currentDate, endOfMonth);
    }

    public static String getFirstDayOfMonth(String pattern) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取当月的第一天
        LocalDate firstDayOfMonth = currentDate.withDayOfMonth(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return firstDayOfMonth.format(formatter);
    }

    public static boolean isAfter(String dateStr1, String dateStr2) {
        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 将字符串转换为LocalDate对象
        LocalDate date1 = LocalDate.parse(dateStr1, formatter);
        LocalDate date2 = LocalDate.parse(dateStr2, formatter);
        return date1.isAfter(date2);
    }

    public static String getBeforeDate(LocalDate localDate, int offset) {
        LocalDate offsetDate = localDate.minusDays(offset);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return offsetDate.format(formatter);
    }

    public static String getBeforeDateTime(LocalDateTime localDate, int offset) {
        LocalDateTime offsetDate = localDate.minusDays(offset);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);
        return offsetDate.format(formatter);
    }

    public static String getBeforeDate(LocalDate localDate, int offset, String pattern) {
        LocalDate offsetDate = localDate.minusDays(offset);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return offsetDate.format(formatter);
    }

    /**
     * 获取某一个月的第一天
     * 
     * @param yearMonthStr 输入的年月字符串，格式为yyyy-MM
     * @return 包含第一天字符串，格式为yyyy/MM/dd
     */
    public static String getFirstDayOfMonth(String yearMonthStr, String pattern) {
        // 解析输入的年月字符串
        YearMonth yearMonth = YearMonth.parse(yearMonthStr);
        // 获取该月的第一天
        return yearMonth.atDay(1).format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String getLastDayOfMonth(String yearMonthStr, String pattern) {
        // 解析输入的年月字符串
        YearMonth yearMonth = YearMonth.parse(yearMonthStr);
        // 获取该月最后一天
        return yearMonth.atEndOfMonth().format(DateTimeFormatter.ofPattern(pattern));
    }

    public static long daysBetween(long timestamp) {
        // 将时间戳转换为Instant对象
        Instant givenTime = Instant.ofEpochMilli(timestamp);
        Instant currentTime = Instant.now();
        return ChronoUnit.DAYS.between(givenTime, currentTime);
    }

    public static LocalDate convertStringToDate(String queryDate, String yyyyMmDd) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(yyyyMmDd);

        // 将字符串转换为 LocalDate
        return LocalDate.parse(queryDate, formatter);
    }

    public static LocalDateTime convertStringToDateTime(String queryDate, String yyyyMmDd) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(yyyyMmDd);

        // 将字符串转换为 LocalDate
        return LocalDateTime.parse(queryDate, formatter);
    }
}
