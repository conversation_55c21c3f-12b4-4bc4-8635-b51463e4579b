package com.ctrip.dcs.ops.infrastructure.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ctrip.dcs.ops.infrastructure.value.OrderProblemResultDTO;

/**
 * Excel工具类 基于EasyExcel实现Excel文件生成
 */
public class ExcelUtil {
    /**
     * 流式生成问题订单Excel文件 - 减少内存占用
     * 
     * @param dataProvider 数据提供者回调函数，用于分批提供数据
     * @param locale 语言环境
     * @return Excel文件的字节数组
     * @throws IOException IO异常
     */
    public static byte[] generateOrderProblemExcelStream(Consumer<StreamExcelDataWriter> dataProvider, String locale) throws IOException {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            ExcelWriterBuilder writerBuilder = EasyExcel.write(outputStream);

            WriteSheet writeSheet;
            if ("en-US".equals(locale)) {
                // 英文版本
                writeSheet = EasyExcel.writerSheet("Problem Order List").head(OrderProblemExcelDataEN.class).build();
            } else {
                // 中文版本（默认）
                writeSheet = EasyExcel.writerSheet("问题订单列表").head(OrderProblemExcelData.class).build();
            }

            try (ExcelWriter excelWriter = writerBuilder.build()) {
                // 使用流式数据写入器
                StreamExcelDataWriter writer = new StreamExcelDataWriter(excelWriter, writeSheet, locale);
                dataProvider.accept(writer);
                writer.finish();
            }

            return outputStream.toByteArray();
        }
    }

    /**
     * 流式Excel数据写入器
     */
    public static class StreamExcelDataWriter {
        private final ExcelWriter excelWriter;
        private final WriteSheet writeSheet;
        private final String locale;
        private int currentRowNum;
        private boolean isFinished = false;

        public StreamExcelDataWriter(ExcelWriter excelWriter, WriteSheet writeSheet, String locale) {
            this.excelWriter = excelWriter;
            this.writeSheet = writeSheet;
            this.locale = locale;
            this.currentRowNum = 1; // 从第1行开始（第0行是表头）
        }

        /**
         * 写入一批数据
         */
        public void writeDataBatch(List<OrderProblemResultDTO> dataList) {
            if (isFinished) {
                throw new IllegalStateException("Writer has been finished, cannot write more data");
            }

            if (dataList == null || dataList.isEmpty()) {
                return;
            }

            if ("en-US".equals(locale)) {
                List<OrderProblemExcelDataEN> excelDataList = dataList.stream().map(OrderProblemExcelDataEN::fromOrderProblemResultDTO).collect(Collectors.toList());
                excelWriter.write(excelDataList, writeSheet);
            } else {
                List<OrderProblemExcelData> excelDataList = dataList.stream().map(OrderProblemExcelData::fromOrderProblemResultDTO).collect(Collectors.toList());
                excelWriter.write(excelDataList, writeSheet);
            }

            currentRowNum += dataList.size();
        }

        /**
         * 完成写入
         */
        public void finish() {
            isFinished = true;
        }
    }

    /**
     * 生成文件名
     */
    public static String generateFileName(String partyId, String period, Long cityId, String serviceType, String type) {
        StringBuilder fileName = new StringBuilder("问题订单列表");
        fileName.append("_供应商").append(partyId);
        fileName.append("_周期").append(period);
        if (cityId != null) {
            fileName.append("_城市").append(cityId);
        }
        if (serviceType != null) {
            fileName.append("_服务类型").append(serviceType);
        }
        if (type != null) {
            fileName.append("_问题类型").append(type);
        }
        fileName.append("_").append(System.currentTimeMillis());
        fileName.append(".xlsx");
        return fileName.toString();
    }
}
