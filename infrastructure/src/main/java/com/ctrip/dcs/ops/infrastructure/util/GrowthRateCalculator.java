package com.ctrip.dcs.ops.infrastructure.util;

import java.math.BigDecimal;
import java.util.Objects;

public final class GrowthRateCalculator {

    public static Double calculateGrowthRate(Double currentPeriod, Double previousPeriod) {
        if (Objects.isNull(currentPeriod) || Objects.isNull(previousPeriod) || new BigDecimal(previousPeriod).compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        return (currentPeriod - previousPeriod) / previousPeriod * 100;
    }

    public static Double calculateGrowthRate(Double currentPeriod, Double previousPeriod, int acc) {
        if (Objects.isNull(currentPeriod) || Objects.isNull(previousPeriod) || new BigDecimal(previousPeriod).compareTo(BigDecimal.ZERO) == 0) {
            return 0D;
        }
        return MathUtil.serDoubleAccuracy((currentPeriod - previousPeriod) / Math.abs(previousPeriod) * 100, acc);
    }
    public static Double calPercentage(Double numerator, Double denominator) {
        if (Objects.isNull(numerator) || Objects.isNull(denominator) || new BigDecimal(denominator).compareTo(BigDecimal.ZERO) == 0) {
            return 0D;
        }
        return MathUtil.doubleAccuracy( numerator / denominator * 100);

    }

    public static Double calPercentage(Long numerator, Long denominator, int acc) {
        if (Objects.isNull(numerator) || Objects.isNull(denominator) || new BigDecimal(denominator).compareTo(BigDecimal.ZERO) == 0) {
            return 0D;
        }
        return MathUtil.serDoubleAccuracy((Double.valueOf(numerator) / Double.valueOf(denominator) * 100), acc);
    }

}
