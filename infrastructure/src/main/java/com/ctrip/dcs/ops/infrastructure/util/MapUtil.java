package com.ctrip.dcs.ops.infrastructure.util;

import com.ctrip.dcs.luna.Log;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/24 22:03
 * @Description: map辅助类
 */
public class MapUtil {
    private static final Log log = Log.getInstance(MapUtil.class);

    public static Double getKeyAsDouble (Map<String, Object> sourceMap, String key) {
        if (MapUtils.isEmpty(sourceMap) || !sourceMap.containsKey(key)) {
            return (double) 0;
        }
        try {
            Object value = sourceMap.get(key);
            if (value instanceof Double) {
                return (double) value;
            }
            return Double.parseDouble(String.valueOf(sourceMap.get(key)));
        } catch (Exception e) {
            log.warn("MapUtils_error_getKeyAsDouble", String.format("key=%s info:%s", key, e.getMessage()));
            return (double) 0;
        }
    }

    public static Long getKeyAsLong(Map<String, Object> sourceMap, String key) {
        if (MapUtils.isEmpty(sourceMap) || !sourceMap.containsKey(key)) {
            return 0L;
        }

        try {
            Object value = sourceMap.get(key);
            if (value == null) {
                return 0L;
            }
            if (value instanceof Long) {
                return (Long) value;
            }
            if (value instanceof Number) {
                return ((Number) value).longValue();
            }
            if (value instanceof String) {
                return Long.parseLong((String) value);
            }
            // Fallback to string conversion for other types
            return Long.parseLong(String.valueOf(value));
        } catch (NumberFormatException | ClassCastException e) {
            log.warn("MapUtils_error_getKeyAsLong", String.format("key=%s info:%s", key, e.getMessage()));
            return 0L;
        }
    }


    public static Integer getKeyAsInteger(Map<String, Object> sourceMap, String key) {
        if (MapUtils.isEmpty(sourceMap) || !sourceMap.containsKey(key)) {
            return 0;
        }

        try {
            Object value = sourceMap.get(key);
            if (value == null) {
                return 0;
            }
            if (value instanceof Integer) {
                return (Integer) value;
            }
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            if (value instanceof String) {
                return Integer.valueOf((String) value);
            }
            // Fallback to string conversion for other types
            return Integer.valueOf(String.valueOf(value));
        } catch (NumberFormatException | ClassCastException e) {
            log.warn("MapUtils_error_getKeyAsLong", String.format("key=%s info:%s", key, e.getMessage()));
            return 0;
        }
    }


    public static String getKeyAsString (Map<String, Object> sourceMap, String key) {
        if (MapUtils.isEmpty(sourceMap) || !sourceMap.containsKey(key)) {
            return StringUtils.EMPTY;
        }
        return String.valueOf(sourceMap.get(key));
    }

}
