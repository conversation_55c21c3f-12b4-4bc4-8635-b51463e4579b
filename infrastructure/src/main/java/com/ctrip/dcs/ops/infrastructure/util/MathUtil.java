package com.ctrip.dcs.ops.infrastructure.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public final class MathUtil {
    public static Double serDoubleAccuracy(Double value, int newScale) {
        if (Objects.isNull(value)) {
            return null;
        }
        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(newScale, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }

    public static Double doubleAccuracy(Double value) {
        if (Objects.isNull(value)) {
            return null;
        }
        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
}
