package com.ctrip.dcs.ops.infrastructure.util;

/**
 * Created by chen<PERSON>anti<PERSON> on 2018/8/1
 */

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.apache.commons.codec.binary.Hex;

public class Md5Utils {

    private static final int size5M = 5 * 1024 * 1024;
    private static final int size10M = 10 * 1024 * 1024;

    public static String compute(byte[] fileBts) {

        byte[] tmp;
        if (fileBts.length > size10M) {
            tmp = new byte[size10M];
            System.arraycopy(fileBts, 0, tmp, 0, size5M);
            System.arraycopy(fileBts, fileBts.length - size5M, tmp, size5M, size5M);
        } else {
            tmp = fileBts;
        }
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] bts = md5.digest(tmp);
            return Hex.encodeHexString(bts);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return "";
        }
    }
}
