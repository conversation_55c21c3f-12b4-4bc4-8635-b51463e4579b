package com.ctrip.dcs.ops.infrastructure.util;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

/**
 * 问题订单Excel导出数据模型
 */
@Data
@HeadStyle(fillForegroundColor = 22, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class OrderProblemExcelData {

    @ExcelProperty(value = "订单ID", index = 0)
    @ColumnWidth(15)
    private String orderId;

    @ExcelProperty(value = "用车日期", index = 1)
    @ColumnWidth(12)
    private String useDate;

    @ExcelProperty(value = "司机ID", index = 2)
    @ColumnWidth(15)
    private String driverId;

    @ExcelProperty(value = "司机姓名", index = 3)
    @ColumnWidth(12)
    private String driverName;

    @ExcelProperty(value = "用车城市", index = 4)
    @ColumnWidth(12)
    private String cityName;

    @ExcelProperty(value = "缺陷类型", index = 5)
    @ColumnWidth(15)
    private String defectType;

    public static OrderProblemExcelData fromOrderProblemResultDTO(com.ctrip.dcs.ops.infrastructure.value.OrderProblemResultDTO dto) {
        OrderProblemExcelData data = new OrderProblemExcelData();
        data.setOrderId(dto.getOrderId());
        data.setUseDate(dto.getUseDate());
        data.setDriverId(dto.getDriverId());
        data.setDriverName(dto.getDriverName());
        data.setCityName(dto.getCityName());
        data.setDefectType(dto.getDefectType());
        return data;
    }
} 