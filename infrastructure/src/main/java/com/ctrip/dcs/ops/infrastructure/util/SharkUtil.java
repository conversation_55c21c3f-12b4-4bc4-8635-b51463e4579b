package com.ctrip.dcs.ops.infrastructure.util;

import org.apache.commons.lang3.StringUtils;

import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import com.ctrip.ibu.platform.shark.sdk.api.entity.SharkRequest;

public final class SharkUtil {
    private static final Integer WEB_APP_ID = 100029576;

    public static String get(String key, String locale, String defaultValues) {
        if (StringUtils.isBlank(key)) {
            return StringUtils.EMPTY;
        }
        SharkRequest sharkRequest = new SharkRequest();
        sharkRequest.setAppId(WEB_APP_ID);
        sharkRequest.setKey(key);
        sharkRequest.setLocale(locale);
        String sharkValue = Shark.get(sharkRequest);
        return StringUtils.isBlank(sharkValue) ? defaultValues : sharkValue;
    }

    public static String get(String key, String locale) {
        return get(key, locale, StringUtils.EMPTY);
    }
}
