package com.ctrip.dcs.ops.infrastructure.util;

import java.util.concurrent.*;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.dianping.cat.async.CatAsync;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

@Configuration
public class ThreadPoolService {

    private static final String OPS_THREAD = "ops-thread";
    private static final String OPS_INDEX_THREAD = "ops-index-thread";
    private static final String OPS_GUIDE_THREAD = "ops-guide-thread";
    private static final String DATA_THREAD = "data-thread";
    /**
     * 自定义线程名称,方便的出错的时候溯源
     */
    private static ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat(OPS_THREAD).build();
    private static ThreadFactory scoreThreadFactory = new ThreadFactoryBuilder().setNameFormat(OPS_INDEX_THREAD).build();
    private static ThreadFactory guideThreadFactory = new ThreadFactoryBuilder().setNameFormat(OPS_GUIDE_THREAD).build();
    private static ThreadFactory dataThreadFactory = new ThreadFactoryBuilder().setNameFormat(DATA_THREAD).build();

    @Bean("scoreCalThreadPool")
    public ExecutorService scoreCalThreadPool() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(20, 100, 10L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000), namedThreadFactory, new ThreadPoolExecutor.CallerRunsPolicy());
        return CatAsync.wrap(TtlExecutors.getTtlExecutorService(threadPoolExecutor));
    }

    @Bean("showDatahreadPool")
    public ExecutorService showDatahreadPool() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(3, 30, 10L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000), dataThreadFactory, new ThreadPoolExecutor.CallerRunsPolicy());
        return CatAsync.wrap(TtlExecutors.getTtlExecutorService(threadPoolExecutor));
    }

    @Bean("scoreIndexThreadPool")
    public ExecutorService scoreIndexThreadPool() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(4, 50, 10L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000), scoreThreadFactory, new ThreadPoolExecutor.CallerRunsPolicy());
        return CatAsync.wrap(TtlExecutors.getTtlExecutorService(threadPoolExecutor));
    }

    @Bean("guideInfoThreadPool")
    public ExecutorService guideInfoThreadPool() {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(3, 50, 10L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000), guideThreadFactory, new ThreadPoolExecutor.CallerRunsPolicy());
        return CatAsync.wrap(TtlExecutors.getTtlExecutorService(threadPoolExecutor));
    }

}
