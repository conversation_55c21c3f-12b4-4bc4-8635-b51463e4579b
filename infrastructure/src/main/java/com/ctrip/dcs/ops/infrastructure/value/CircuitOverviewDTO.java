package com.ctrip.dcs.ops.infrastructure.value;

import lombok.Data;

@Data
public class CircuitOverviewDTO {
    /**
     * 接单量
     */
    private Long orderCnt;
    /**
     * 完单量
     */
    private Long orderCompletionCnt;
    /**
     * 到场无车订单数
     */
    private Long noVehicleCnt;
    /**
     * 人车不符订单数
     */
    private Long vehicleAndPersonNotMatchCnt;
    /**
     * 司机端不规范订单数
     */
    private Long notStandardizedCNt;
    /**
     * 其他订单数
     */
    private Long otherCnt;
    /**
     * 到场无车率
     */
    private Double noVehicleRatio;
    /**
     * 人车不符率
     */
    private Double vehicleAndPersonNotMatchRatio;
    /**
     * 司机端未规范使用率
     */
    private Double notStandardizedRatio;
    /**
     * 到场无车是否熔断
     */
    private Integer noVehicleIsCircuited;
    /**
     * 人车不符是否熔断
     */
    private Integer vehicleAndPersonNotMatchIsCircuited;
    /**
     * 司机端不规范是否熔断
     */
    private Integer notStandardizedIsCircuited;
    /**
     * 到场无车是否预警
     */
    private Integer noVehicleisWarn;
    /**
     * 人车不符是否预警
     */
    private Integer vehicleAndPersonNotMatchisWarn;
    /**
     * 司机端不规范是否预警
     */
    private Integer notStandardizedisWarn;
}
