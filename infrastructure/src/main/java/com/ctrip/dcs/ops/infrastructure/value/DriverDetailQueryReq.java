package com.ctrip.dcs.ops.infrastructure.value;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/23 16:01
 * @Description: 司机详情查询请求参数
 */
@Setter
@Getter
public class DriverDetailQueryReq {

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 司机id
     */
    private Long driverId;
    private Long supplierId;
    private String locale;

    public boolean isOneDay() {
        return StringUtils.equals(this.getEndDate(), this.getStartDate());
    }

}
