package com.ctrip.dcs.ops.infrastructure.value;

import com.ctrip.dcs.ops.infrastructure.constant.DriverItemScoreTypeEnum;
import com.ctrip.dcs.ops.infrastructure.util.GrowthRateCalculator;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/23 16:05
 * @Description: 司机详情查询出参
 */
@Getter
@Setter
public class DriverDetailRespDTO {

    /**
     * 司机id
     */
    private Long driverId;
    /**
     * 司机等级
     */
    private String driverLevel;

    /**
     * sop合格率
     */
    private String sopPassRate;

    /**
     * 轨迹合格率
     */
    private String trackPassRate;
    /**
     * 司机各指标分数明细
     */
    private List<DriverScoreDetail> driverScoreDetails = new ArrayList<>();


    @Getter
    @Setter
    public static class DriverScoreDetail {
        /**
         * 分数
         */
        private Double currScore;

        /**
         * 上一周期的分数
         */
        private Double lastScore;
        /**
         * 类型key
         */
        private DriverItemScoreTypeEnum itemScoreTypeEnum;

        public Double fetchDayToDayRatio() {
            return GrowthRateCalculator.calculateGrowthRate(this.getCurrScore(), this.getLastScore(), 2);
        }

    }

}
