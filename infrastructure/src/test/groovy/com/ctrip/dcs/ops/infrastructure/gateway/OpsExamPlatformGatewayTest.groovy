package com.ctrip.dcs.ops.infrastructure.gateway

import com.ctrip.daas.controller.ApiResponseType
import com.ctrip.daas.controller.ApiServiceClient
import com.ctrip.dcs.ops.infrastructure.config.ApiDaasConfig
import com.ctrip.dcs.ops.infrastructure.config.DaasMetaDataConfig
import com.ctrip.dcs.ops.infrastructure.config.dto.ApiInfoDTO
import spock.lang.Specification
import spock.lang.Unroll

class OpsExamPlatformGatewayTest extends Specification {
    def testObj = new DaasGatewayV1()
    def daasMetaDataConfig = Mock(DaasMetaDataConfig)
    def apiDaasConfig = Mock(ApiDaasConfig)
    def apiServiceClient = Mock(ApiServiceClient)


    def setup() {

        testObj.apiDaasConfig = apiDaasConfig
        testObj.daasMetaDataConfig = daasMetaDataConfig
    }

    @Unroll
    def "queryDataByDaasTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasMetaDataConfig.getApiServiceClientV1() >> apiServiceClient
        daasMetaDataConfig.getAppId() >> 0
        apiDaasConfig.getApiInfo(_) >> new ApiInfoDTO(token: "token")
        apiServiceClient.invoke(_) >> new ApiResponseType(null, 0, "123", true, "[{\"cnt\":0}]")

        when:
        def result = testObj.queryDataByDaas(apiName, param)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        apiName   | param               || expectedResult
        "apiName" | ["String": "param"] || [[cnt: 0]]
    }
}
