package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.dcs.go.util.JsonUtil
import com.ctrip.dcs.ops.infrastructure.config.*
import com.ctrip.dcs.ops.infrastructure.config.dto.*
import com.ctrip.dcs.ops.infrastructure.value.TableDTO
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.gson.reflect.TypeToken
import spock.lang.Specification

import java.lang.reflect.Type

public class BaseAdapterData extends Specification {

    private static final ObjectMapper objectMapper = new ObjectMapper();


    String json = """
{
    "platform": [
        {
            "key": "driver_service_gather_score_cn",
            "type": "double",
            "resultKey": "driverServiceGatherScoreCn",
            "itemName": "司机服务分（汇总）-CN",
            "order": 1,
            "parentKey": "ctrip"
        },
        {
            "key": "driver_service_gather_score_trip",
            "type": "double",
            "resultKey": "driverServiceGatherScoreTrip",
            "itemName": "司机服务分（汇总）-Trip",
            "order": 2,
            "parentKey": "trip"
        },
        {
            "key": "service_provider_im_score_cn",
            "type": "double",
            "resultKey": "serviceProviderImScoreCn",
            "itemName": "服务商服务分-CN",
            "order": 3,
            "parentKey": "ctrip"
        },
        {
            "key": "service_provider_im_score_trip",
            "type": "double",
            "resultKey": "serviceProviderImScoreTrip",
            "itemName": "服务商服务分-Trip",
            "order": 4,
            "parentKey": "trip"
        },
        {
            "key": "service_provider_enquiry_score_cn",
            "type": "double",
            "resultKey": "serviceProviderEnquiryScoreCn",
            "itemName": "服务商商品分-CN",
            "order": 5,
            "parentKey": "ctrip"
        },
        {
            "key": "service_provider_enquiry_score_trip",
            "type": "double",
            "resultKey": "serviceProviderEnquiryScoreTrip",
            "itemName": "服务商商品分-Trip",
            "order": 6,
            "parentKey": "trip"
        },
        {
            "key": "completed_order_score_cn",
            "type": "double",
            "resultKey": "completedOrderScoreCn",
            "itemName": "订单完成分-CN",
            "order": 7,
            "parentKey": "driverServiceGatherScoreCn"
        },
        {
            "key": "completed_order_score_trip",
            "type": "double",
            "resultKey": "completedOrderScoreTrip",
            "itemName": "订单完成分-Trip",
            "order": 8,
            "parentKey": "driverServiceGatherScoreTrip"
        },
        {
            "key": "driver_sop_score_cn",
            "type": "long",
            "resultKey": "driverSopScoreCn",
            "itemName": "司机端SOP分-CN",
            "order": 9,
            "parentKey": "driverServiceGatherScoreCn"
        },
        {
            "key": "driver_sop_score_trip",
            "type": "double",
            "resultKey": "driverSopScoreTrip",
            "itemName": "司机端SOP分-Trip",
            "order": 10,
            "parentKey": "driverServiceGatherScoreTrip"
        },
        {
            "key": "completed_pickup_card_score_cn",
            "type": "double",
            "resultKey": "completedPickupCardScoreCn",
            "itemName": "订单附加服务分-举牌-CN",
            "order": 11,
            "parentKey": "driverServiceGatherScoreCn"
        },
        {
            "key": "completed_pickup_card_score_trip",
            "type": "double",
            "resultKey": "completedPickupCardScoreTrip",
            "itemName": "订单附加服务分-举牌-Trip",
            "order": 12,
            "parentKey": "driverServiceGatherScoreTrip"
        },
        {
            "key": "completed_childseat_score_cn",
            "type": "double",
            "resultKey": "completedChildseatScoreCn",
            "itemName": "订单附加服务分-儿童座椅-CN",
            "order": 13,
            "parentKey": "driverServiceGatherScoreCn"
        },
        {
            "key": "completed_childseat_score_trip",
            "type": "double",
            "resultKey": "completedChildseatScoreTrip",
            "itemName": "订单附加服务分-儿童座椅-Trip",
            "order": 14,
            "parentKey": "driverServiceGatherScoreTrip"
        },
        {
            "key": "comment_score_cn",
            "type": "double",
            "resultKey": "commentScoreCn",
            "itemName": "好评分-CN",
            "order": 15,
            "parentKey": "driverServiceGatherScoreCn"
        },
        {
            "key": "comment_score_trip",
            "type": "double",
            "resultKey": "commentScoreTrip",
            "itemName": "好评分-Trip",
            "order": 16,
            "parentKey": "driverServiceGatherScoreTrip"
        },
        {
            "key": "defect_score_cn",
            "type": "double",
            "resultKey": "defectScoreCn",
            "itemName": "违规分-CN",
            "order": 17,
            "parentKey": "driverServiceGatherScoreCn"
        },
        {
            "key": "defect_score_trip",
            "type": "double",
            "resultKey": "defectScoreTrip",
            "itemName": "违规分-Trip",
            "order": 18,
            "parentKey": "driverServiceGatherScoreTrip"
        },
        {
            "key": "vendor_first_reply_score_cn",
            "type": "double",
            "resultKey": "vendorFirstReplyScoreCn",
            "itemName": "供应商IM回复率得分-CN",
            "order": 19,
            "parentKey": "serviceProviderImScoreCn"
        },
        {
            "key": "vendor_first_reply_score_trip",
            "type": "double",
            "resultKey": "vendorFirstReplyScoreTrip",
            "itemName": "供应商IM回复率得分-Trip",
            "order": 20,
            "parentKey": "serviceProviderImScoreTrip"
        },
        {
            "key": "first_reply_intime_score_cn",
            "type": "double",
            "resultKey": "firstReplyIntimeScoreCn",
            "itemName": "供应商首次120s回复及时率得分-CN",
            "order": 21,
            "parentKey": "serviceProviderImScoreCn"
        },
        {
            "key": "first_reply_intime_score_trip",
            "type": "double",
            "resultKey": "firstReplyIntimeScoreTrip",
            "itemName": "供应商首次120s回复及时率-Trip",
            "order": 22,
            "parentKey": "serviceProviderImScoreTrip"
        },
        {
            "key": "enquiry_result_rate_cn",
            "type": "double",
            "resultKey": "enquiryResultRateCn",
            "itemName": "询价有结果率-CN",
            "order": 23,
            "parentKey": "serviceProviderEnquiryScoreCn"
        },
        {
            "key": "enquiry_result_rate_trip",
            "type": "double",
            "resultKey": "enquiryResultRateTrip",
            "itemName": "询价有结果率-Trip",
            "order": 24,
            "parentKey": "serviceProviderEnquiryScoreTrip"
        }
    ],
    "tripcar": [
        {
            "key": "driver_service_score",
            "type": "double",
            "resultKey": "driverServiceScore",
            "itemName": "司机服务分",
            "order": 1,
            "parentKey": "ctrip"
        },
        {
            "key": "vendor_service_score",
            "type": "double",
            "resultKey": "vendorServiceScore",
            "itemName": "供应商服务分",
            "order": 2,
            "parentKey": "ctrip"
        },
        {
            "key": "completed_order_score",
            "type": "double",
            "resultKey": "completedOrderScore",
            "itemName": "订单完成分",
            "order": 3,
            "parentKey": "driverServiceScore"
        },
        {
            "key": "driver_sop_score",
            "type": "long",
            "resultKey": "driverSopScore",
            "itemName": "司机端SOP分",
            "order": 4,
            "parentKey": "driverServiceScore"
        },
        {
            "key": "completed_pickup_card_score",
            "type": "double",
            "resultKey": "completedPickupCardScore",
            "itemName": "订单附加服务分-举牌",
            "order": 5,
            "parentKey": "driverServiceScore"
        },
        {
            "key": "completed_childseat_score",
            "type": "double",
            "resultKey": "completedChildseatScore",
            "itemName": "订单附加服务分-儿童座椅",
            "order": 6,
            "parentKey": "driverServiceScore"
        },
        {
            "key": "comment_score",
            "type": "double",
            "resultKey": "commentScore",
            "itemName": "好评分",
            "order": 7,
            "parentKey": "driverServiceScore"
        },
        {
            "key": "defect_score",
            "type": "double",
            "resultKey": "defectScore",
            "itemName": "违规分",
            "order": 8,
            "parentKey": "driverServiceScore"
        },
        {
            "key": "vendor_first_reply_score",
            "type": "double",
            "resultKey": "vendorFirstReplyScore",
            "itemName": "供应商IM回复率得分",
            "order": 9,
            "parentKey": "vendorServiceScore"
        },
        {
            "key": "first_reply_intime_score",
            "type": "double",
            "resultKey": "firstReplyIntimeScore",
            "itemName": "供应商首次120s回复及时",
            "order": 10,
            "parentKey": "vendorServiceScore"
        }
    ]
}
"""

    static String json1 = "{\n" +
            "    \"platform\": {\n" +
            "        \"overallScore\": \"service_provider_overall_score\",\n" +
            "        \"providerLevel\": \"service_provider_level\",\n" +
            "        \"datachangeLasttime\": \"datachange_lasttime\"\n" +
            "    },\n" +
            "    \"tripcar\": {\n" +
            "        \"overallScore\": \"vendor_overall_score\",\n" +
            "        \"providerLevel\": \"service_provider_level\",\n" +
            "        \"datachangeLasttime\": \"datachange_lasttime\"\n" +
            "    }\n" +
            "}";

    static String json2 = "{\n" +
            "    \"tripcar\": {\n" +
            "        \"downloadUrl\": \"https://docs.c-ctrip.com/files/7/dcs_fe_assets/携程专车-国际接送机业务经营表现分（OPS）考核规则.pdf\"\n" +
            "    },\n" +
            "    \"platform\": {\n" +
            "        \"downloadUrl\": \"https://docs.c-ctrip.com/files/7/dcs_fe_assets/携程用车平台-国际接送机业务经营表现分（OPS）考核规则.pdf\"\n" +
            "    }\n" +
            "}";

    public OpsExamFieldConfigTest getConfigField() {
        return new OpsExamFieldConfigTest();
    }

    class OpsExamFieldConfigTest extends OpsExamFieldConfig {
        public List<OpsExamFieldDTO> getFieldList(String key) {
            Type type = new TypeToken<Map<String, List<OpsExamFieldDTO>>>() {}.getType();
            Map<String, List<OpsExamFieldDTO>> result = JsonUtil.fromString(json, type);
            return result.get(key);
        }
    }

    public OpsExamSummaryFieldConfigTest getSummaryFiled() {
        return new OpsExamSummaryFieldConfigTest();
    }

    class OpsExamSummaryFieldConfigTest extends OpsExamSummaryFieldConfig {
        public OpsExamSummaryFieldDTO getFieldList(String key) {
            Type type = new TypeToken<Map<String, OpsExamSummaryFieldDTO>>() {}.getType();
            Map<String, OpsExamSummaryFieldDTO> result = JsonUtil.fromString(json1, type);
            return result.get(key);
        }
    }

    public OpsSourceConfigTest getSource() {
        return new OpsSourceConfigTest();
    }

    class OpsSourceConfigTest extends OpsSourceConfig {
        public OpsSourceDTO getSourceInfo(String key) {
            Type type = new TypeToken<Map<String, OpsSourceDTO>>() {}.getType();
            Map<String, OpsSourceDTO> result = JsonUtil.fromString(json2, type);
            return result.get(key);
        }
    }

    static String platform = """
[ {
  "id" : 1279146,
  "region_name" : "西北欧",
  "country_name" : "爱尔兰",
  "use_city_name" : "都柏林",
  "vendor_name" : "蜜柚旅行",
  "vendor_id" : 1000008,
  "brand_type_name" : "OTA",
  "corp_name_use" : "上海金棕榈国际旅行社有限公司",
  "corp_id_use" : 6206,
  "is_new_vendor" : 0,
  "assess_month" : "2024-10",
  "assess_quarter" : "4",
  "completed_order_score_cn" : 0.0,
  "driver_sop_score_cn" : 0.0,
  "completed_pickup_card_score_cn" : 0.0,
  "completed_childseat_score_cn" : 0.0,
  "comment_score_cn" : 0.0,
  "defect_score_cn" : 0.0,
  "vendor_first_reply_score_cn" : 5.0,
  "first_reply_intime_score_cn" : 0.0,
  "enquiry_result_rate_cn" : 0.932282793867121,
  "completed_order_score_trip" : 0.0,
  "driver_sop_score_trip" : 0.0,
  "completed_pickup_card_score_trip" : 0.0,
  "completed_childseat_score_trip" : 0.0,
  "comment_score_trip" : 0.0,
  "defect_score_trip" : 0.0,
  "vendor_first_reply_score_trip" : 0.0,
  "first_reply_intime_score_trip" : 0.0,
  "enquiry_result_rate_trip" : 0.1538461538461538,
  "driver_service_score_cn" : 0.0,
  "driver_service_defect_score_cn" : 5.0,
  "driver_service_gather_score_cn" : 1.6666666666666665,
  "service_provider_im_score_cn" : 2.5,
  "service_provider_enquiry_score_cn" : 4.66141396933561,
  "service_provider_overall_score_cn" : 2.***************,
  "driver_service_score_trip" : 0.0,
  "driver_service_defect_score_trip" : 5.0,
  "driver_service_gather_score_trip" : 1.6666666666666665,
  "service_provider_im_score_trip" : 0.0,
  "service_provider_enquiry_score_trip" : 0.76923076923077,
  "service_provider_overall_score_trip" : 1.153846153846154,
  "service_provider_overall_score" : 2.***************,
  "service_provider_level" : "C",
  "is_twice_d_level" : 0,
  "is_add_new_city" : 0,
  "is_new_vendor_name" : "否",
  "is_twice_d_level_name" : "否",
  "is_add_new_city_name" : "否",
  "hive_d" : "2024-10-06",
  "datachange_lasttime" : 1728871804032,
  "use_city_id" : "803"
}, {
  "id" : 1331191,
  "region_name" : "西北欧",
  "country_name" : "爱尔兰",
  "use_city_name" : "都柏林",
  "vendor_name" : "蜜柚旅行",
  "vendor_id" : 1000008,
  "brand_type_name" : "OTA",
  "corp_name_use" : "上海金棕榈国际旅行社有限公司",
  "corp_id_use" : 6206,
  "is_new_vendor" : 0,
  "assess_month" : "2024-10",
  "assess_quarter" : "4",
  "completed_order_score_cn" : 0.0,
  "driver_sop_score_cn" : 0.0,
  "completed_pickup_card_score_cn" : 0.0,
  "completed_childseat_score_cn" : 0.0,
  "comment_score_cn" : 0.0,
  "defect_score_cn" : 0.0,
  "vendor_first_reply_score_cn" : 5.0,
  "first_reply_intime_score_cn" : 0.0,
  "enquiry_result_rate_cn" : 0.9387308533916849,
  "completed_order_score_trip" : 0.0,
  "driver_sop_score_trip" : 0.0,
  "completed_pickup_card_score_trip" : 0.0,
  "completed_childseat_score_trip" : 0.0,
  "comment_score_trip" : 0.0,
  "defect_score_trip" : 0.0,
  "vendor_first_reply_score_trip" : 0.0,
  "first_reply_intime_score_trip" : 0.0,
  "enquiry_result_rate_trip" : 0.08,
  "driver_service_score_cn" : 0.0,
  "driver_service_defect_score_cn" : 5.0,
  "driver_service_gather_score_cn" : 1.6666666666666665,
  "service_provider_im_score_cn" : 2.5,
  "service_provider_enquiry_score_cn" : 4.69365426695842,
  "service_provider_overall_score_cn" : 2.***************,
  "driver_service_score_trip" : 0.0,
  "driver_service_defect_score_trip" : 5.0,
  "driver_service_gather_score_trip" : 1.6666666666666665,
  "service_provider_im_score_trip" : 0.0,
  "service_provider_enquiry_score_trip" : 0.4,
  "service_provider_overall_score_trip" : 1.08,
  "service_provider_overall_score" : 2.***************,
  "service_provider_level" : "C",
  "is_twice_d_level" : 0,
  "is_add_new_city" : 0,
  "is_new_vendor_name" : "否",
  "is_twice_d_level_name" : "否",
  "is_add_new_city_name" : "否",
  "hive_d" : "2024-10-07",
  "datachange_lasttime" : 1728871804032,
  "use_city_id" : "803"
}, {
  "id" : 1382823,
  "region_name" : "西北欧",
  "country_name" : "爱尔兰",
  "use_city_name" : "都柏林",
  "vendor_name" : "蜜柚旅行",
  "vendor_id" : 1000008,
  "brand_type_name" : "OTA",
  "corp_name_use" : "上海金棕榈国际旅行社有限公司",
  "corp_id_use" : 6206,
  "is_new_vendor" : 0,
  "assess_month" : "2024-10",
  "assess_quarter" : "4",
  "completed_order_score_cn" : 0.4,
  "driver_sop_score_cn" : 0.0,
  "completed_pickup_card_score_cn" : 0.0,
  "completed_childseat_score_cn" : 0.0,
  "comment_score_cn" : 0.0,
  "defect_score_cn" : 0.0,
  "vendor_first_reply_score_cn" : 5.0,
  "first_reply_intime_score_cn" : 0.0,
  "enquiry_result_rate_cn" : 0.9430459408432977,
  "completed_order_score_trip" : 0.0,
  "driver_sop_score_trip" : 0.0,
  "completed_pickup_card_score_trip" : 0.0,
  "completed_childseat_score_trip" : 0.0,
  "comment_score_trip" : 0.0,
  "defect_score_trip" : 0.0,
  "vendor_first_reply_score_trip" : 0.0,
  "first_reply_intime_score_trip" : 0.0,
  "enquiry_result_rate_trip" : 0.1428571428571429,
  "driver_service_score_cn" : 0.****************,
  "driver_service_defect_score_cn" : 5.0,
  "driver_service_gather_score_cn" : 2.0270270270270268,
  "service_provider_im_score_cn" : 2.5,
  "service_provider_enquiry_score_cn" : 4.71522970421649,
  "service_provider_overall_score_cn" : 2.****************,
  "driver_service_score_trip" : 0.0,
  "driver_service_defect_score_trip" : 5.0,
  "driver_service_gather_score_trip" : 1.6666666666666665,
  "service_provider_im_score_trip" : 0.0,
  "service_provider_enquiry_score_trip" : 0.71428571428571,
  "service_provider_overall_score_trip" : 1.142857142857142,
  "service_provider_overall_score" : 2.****************,
  "service_provider_level" : "B",
  "is_twice_d_level" : 0,
  "is_add_new_city" : 0,
  "is_new_vendor_name" : "否",
  "is_twice_d_level_name" : "否",
  "is_add_new_city_name" : "否",
  "hive_d" : "2024-10-08",
  "datachange_lasttime" : 1728871804032,
  "use_city_id" : "803"
}, {
  "id" : 1435484,
  "region_name" : "西北欧",
  "country_name" : "爱尔兰",
  "use_city_name" : "都柏林",
  "vendor_name" : "蜜柚旅行",
  "vendor_id" : 1000008,
  "brand_type_name" : "OTA",
  "corp_name_use" : "上海金棕榈国际旅行社有限公司",
  "corp_id_use" : 6206,
  "is_new_vendor" : 0,
  "assess_month" : "2024-10",
  "assess_quarter" : "4",
  "completed_order_score_cn" : 0.4,
  "driver_sop_score_cn" : 0.0,
  "completed_pickup_card_score_cn" : 0.0,
  "completed_childseat_score_cn" : 0.0,
  "comment_score_cn" : 0.0,
  "defect_score_cn" : 0.0,
  "vendor_first_reply_score_cn" : 5.0,
  "first_reply_intime_score_cn" : 0.0,
  "enquiry_result_rate_cn" : 0.9451202263083451,
  "completed_order_score_trip" : 0.0,
  "driver_sop_score_trip" : 0.0,
  "completed_pickup_card_score_trip" : 0.0,
  "completed_childseat_score_trip" : 0.0,
  "comment_score_trip" : 0.0,
  "defect_score_trip" : 0.0,
  "vendor_first_reply_score_trip" : 0.0,
  "first_reply_intime_score_trip" : 0.0,
  "enquiry_result_rate_trip" : 0.1379310344827586,
  "driver_service_score_cn" : 0.5,
  "driver_service_defect_score_cn" : 5.0,
  "driver_service_gather_score_cn" : 1.9999999999999996,
  "service_provider_im_score_cn" : 2.5,
  "service_provider_enquiry_score_cn" : 4.72560113154173,
  "service_provider_overall_score_cn" : 2.6451202263083458,
  "driver_service_score_trip" : 0.0,
  "driver_service_defect_score_trip" : 5.0,
  "driver_service_gather_score_trip" : 1.6666666666666665,
  "service_provider_im_score_trip" : 0.0,
  "service_provider_enquiry_score_trip" : 0.68965517241379,
  "service_provider_overall_score_trip" : 1.137931034482758,
  "service_provider_overall_score" : 2.6451202263083458,
  "service_provider_level" : "B",
  "is_twice_d_level" : 0,
  "is_add_new_city" : 0,
  "is_new_vendor_name" : "否",
  "is_twice_d_level_name" : "否",
  "is_add_new_city_name" : "否",
  "hive_d" : "2024-10-09",
  "datachange_lasttime" : 1728871804032,
  "use_city_id" : "803"
}, {
  "id" : 1487508,
  "region_name" : "西北欧",
  "country_name" : "爱尔兰",
  "use_city_name" : "都柏林",
  "vendor_name" : "蜜柚旅行",
  "vendor_id" : 1000008,
  "brand_type_name" : "OTA",
  "corp_name_use" : "上海金棕榈国际旅行社有限公司",
  "corp_id_use" : 6206,
  "is_new_vendor" : 0,
  "assess_month" : "2024-10",
  "assess_quarter" : "4",
  "completed_order_score_cn" : 0.4,
  "driver_sop_score_cn" : 0.0,
  "completed_pickup_card_score_cn" : 0.0,
  "completed_childseat_score_cn" : 0.0,
  "comment_score_cn" : 0.0,
  "defect_score_cn" : 0.0,
  "vendor_first_reply_score_cn" : 5.0,
  "first_reply_intime_score_cn" : 0.0,
  "enquiry_result_rate_cn" : 0.9455095862764884,
  "completed_order_score_trip" : 0.0,
  "driver_sop_score_trip" : 0.0,
  "completed_pickup_card_score_trip" : 0.0,
  "completed_childseat_score_trip" : 0.0,
  "comment_score_trip" : 0.0,
  "defect_score_trip" : 0.0,
  "vendor_first_reply_score_trip" : 0.0,
  "first_reply_intime_score_trip" : 0.0,
  "enquiry_result_rate_trip" : 0.1333333333333333,
  "driver_service_score_cn" : 0.5,
  "driver_service_defect_score_cn" : 5.0,
  "driver_service_gather_score_cn" : 1.9999999999999996,
  "service_provider_im_score_cn" : 2.5,
  "service_provider_enquiry_score_cn" : 4.72754793138244,
  "service_provider_overall_score_cn" : 2.645509586276488,
  "driver_service_score_trip" : 0.0,
  "driver_service_defect_score_trip" : 5.0,
  "driver_service_gather_score_trip" : 1.6666666666666665,
  "service_provider_im_score_trip" : 0.0,
  "service_provider_enquiry_score_trip" : 0.66666666666667,
  "service_provider_overall_score_trip" : 1.133333333333334,
  "service_provider_overall_score" : 2.645509586276488,
  "service_provider_level" : "B",
  "is_twice_d_level" : 0,
  "is_add_new_city" : 0,
  "is_new_vendor_name" : "否",
  "is_twice_d_level_name" : "否",
  "is_add_new_city_name" : "否",
  "hive_d" : "2024-10-10",
  "datachange_lasttime" : 1728871804032,
  "use_city_id" : "803"
}, {
  "id" : 1539707,
  "region_name" : "西北欧",
  "country_name" : "爱尔兰",
  "use_city_name" : "都柏林",
  "vendor_name" : "蜜柚旅行",
  "vendor_id" : 1000008,
  "brand_type_name" : "OTA",
  "corp_name_use" : "上海金棕榈国际旅行社有限公司",
  "corp_id_use" : 6206,
  "is_new_vendor" : 0,
  "assess_month" : "2024-10",
  "assess_quarter" : "4",
  "completed_order_score_cn" : 0.4,
  "driver_sop_score_cn" : 0.0,
  "completed_pickup_card_score_cn" : 0.0,
  "completed_childseat_score_cn" : 0.0,
  "comment_score_cn" : 0.0,
  "defect_score_cn" : 0.0,
  "vendor_first_reply_score_cn" : 5.0,
  "first_reply_intime_score_cn" : 0.0,
  "enquiry_result_rate_cn" : 0.9402015677491601,
  "completed_order_score_trip" : 0.0,
  "driver_sop_score_trip" : 0.0,
  "completed_pickup_card_score_trip" : 0.0,
  "completed_childseat_score_trip" : 0.0,
  "comment_score_trip" : 0.0,
  "defect_score_trip" : 0.0,
  "vendor_first_reply_score_trip" : 0.0,
  "first_reply_intime_score_trip" : 0.0,
  "enquiry_result_rate_trip" : 0.1290322580645161,
  "driver_service_score_cn" : 0.*****************,
  "driver_service_defect_score_cn" : 5.0,
  "driver_service_gather_score_cn" : 1.9696969696969695,
  "service_provider_im_score_cn" : 2.5,
  "service_provider_enquiry_score_cn" : 4.7010078387458,
  "service_provider_overall_score_cn" : 2.622019749567342,
  "driver_service_score_trip" : 0.0,
  "driver_service_defect_score_trip" : 5.0,
  "driver_service_gather_score_trip" : 1.6666666666666665,
  "service_provider_im_score_trip" : 0.0,
  "service_provider_enquiry_score_trip" : 0.64516129032258,
  "service_provider_overall_score_trip" : 1.129032258064516,
  "service_provider_overall_score" : 2.622019749567342,
  "service_provider_level" : "B",
  "is_twice_d_level" : 0,
  "is_add_new_city" : 0,
  "is_new_vendor_name" : "否",
  "is_twice_d_level_name" : "否",
  "is_add_new_city_name" : "否",
  "hive_d" : "2024-10-11",
  "datachange_lasttime" : 1728871804032,
  "use_city_id" : "803"
}, {
  "id" : 1591997,
  "region_name" : "西北欧",
  "country_name" : "爱尔兰",
  "use_city_name" : "都柏林",
  "vendor_name" : "蜜柚旅行",
  "vendor_id" : 1000008,
  "brand_type_name" : "OTA",
  "corp_name_use" : "上海金棕榈国际旅行社有限公司",
  "corp_id_use" : 6206,
  "is_new_vendor" : 0,
  "assess_month" : "2024-10",
  "assess_quarter" : "4",
  "completed_order_score_cn" : 0.4,
  "driver_sop_score_cn" : 0.0,
  "completed_pickup_card_score_cn" : 0.0,
  "completed_childseat_score_cn" : 0.0,
  "comment_score_cn" : 0.0,
  "defect_score_cn" : 0.0,
  "vendor_first_reply_score_cn" : 5.0,
  "first_reply_intime_score_cn" : 0.0,
  "enquiry_result_rate_cn" : 0.942839582052858,
  "completed_order_score_trip" : 0.0,
  "driver_sop_score_trip" : 0.0,
  "completed_pickup_card_score_trip" : 0.0,
  "completed_childseat_score_trip" : 0.0,
  "comment_score_trip" : 0.0,
  "defect_score_trip" : 0.0,
  "vendor_first_reply_score_trip" : 0.0,
  "first_reply_intime_score_trip" : 0.0,
  "enquiry_result_rate_trip" : 0.1764705882352941,
  "driver_service_score_cn" : 0.*****************,
  "driver_service_defect_score_cn" : 5.0,
  "driver_service_gather_score_cn" : 1.9696969696969695,
  "service_provider_im_score_cn" : 2.5,
  "service_provider_enquiry_score_cn" : 4.71419791026429,
  "service_provider_overall_score_cn" : 2.62465776387104,
  "driver_service_score_trip" : 0.0,
  "driver_service_defect_score_trip" : 5.0,
  "driver_service_gather_score_trip" : 1.6666666666666665,
  "service_provider_im_score_trip" : 0.0,
  "service_provider_enquiry_score_trip" : 0.88235294117647,
  "service_provider_overall_score_trip" : 1.176470588235294,
  "service_provider_overall_score" : 2.62465776387104,
  "service_provider_level" : "B",
  "is_twice_d_level" : 0,
  "is_add_new_city" : 0,
  "is_new_vendor_name" : "否",
  "is_twice_d_level_name" : "否",
  "is_add_new_city_name" : "否",
  "hive_d" : "2024-10-12",
  "datachange_lasttime" : 1728871804032,
  "use_city_id" : "803"
}, {
  "id" : 1645099,
  "region_name" : "西北欧",
  "country_name" : "爱尔兰",
  "use_city_name" : "都柏林",
  "vendor_name" : "蜜柚旅行",
  "vendor_id" : 1000008,
  "brand_type_name" : "OTA",
  "corp_name_use" : "上海金棕榈国际旅行社有限公司",
  "corp_id_use" : 6206,
  "is_new_vendor" : 0,
  "assess_month" : "2024-10",
  "assess_quarter" : "4",
  "completed_order_score_cn" : 0.4,
  "driver_sop_score_cn" : 0.0,
  "completed_pickup_card_score_cn" : 0.0,
  "completed_childseat_score_cn" : 0.0,
  "comment_score_cn" : 0.0,
  "defect_score_cn" : 0.0,
  "vendor_first_reply_score_cn" : 5.0,
  "first_reply_intime_score_cn" : 0.0,
  "enquiry_result_rate_cn" : 0.9415781487101669,
  "completed_order_score_trip" : 0.0,
  "driver_sop_score_trip" : 0.0,
  "completed_pickup_card_score_trip" : 0.0,
  "completed_childseat_score_trip" : 0.0,
  "comment_score_trip" : 0.0,
  "defect_score_trip" : 0.0,
  "vendor_first_reply_score_trip" : 0.0,
  "first_reply_intime_score_trip" : 0.0,
  "enquiry_result_rate_trip" : 0.1764705882352941,
  "driver_service_score_cn" : 0.3846153846153846,
  "driver_service_defect_score_cn" : 5.0,
  "driver_service_gather_score_cn" : 1.9230769230769227,
  "service_provider_im_score_cn" : 2.5,
  "service_provider_enquiry_score_cn" : 4.70789074355083,
  "service_provider_overall_score_cn" : 2.59542430255632,
  "driver_service_score_trip" : 0.0,
  "driver_service_defect_score_trip" : 5.0,
  "driver_service_gather_score_trip" : 1.6666666666666665,
  "service_provider_im_score_trip" : 0.0,
  "service_provider_enquiry_score_trip" : 0.88235294117647,
  "service_provider_overall_score_trip" : 1.176470588235294,
  "service_provider_overall_score" : 2.59542430255632,
  "service_provider_level" : "B",
  "is_twice_d_level" : 0,
  "is_add_new_city" : 0,
  "is_new_vendor_name" : "否",
  "is_twice_d_level_name" : "否",
  "is_add_new_city_name" : "否",
  "hive_d" : "2024-10-13",
  "datachange_lasttime" : 1728871804032,
  "use_city_id" : "803"
}, {
  "id" : 1697522,
  "region_name" : "西北欧",
  "country_name" : "爱尔兰",
  "use_city_name" : "都柏林",
  "vendor_name" : "蜜柚旅行",
  "vendor_id" : 1000008,
  "brand_type_name" : "OTA",
  "corp_name_use" : "上海金棕榈国际旅行社有限公司",
  "corp_id_use" : 6206,
  "is_new_vendor" : 0,
  "assess_month" : "2024-10",
  "assess_quarter" : "4",
  "completed_order_score_cn" : 0.4,
  "driver_sop_score_cn" : 0.0,
  "completed_pickup_card_score_cn" : 0.0,
  "completed_childseat_score_cn" : 0.0,
  "comment_score_cn" : 0.0,
  "defect_score_cn" : 0.0,
  "vendor_first_reply_score_cn" : 5.0,
  "first_reply_intime_score_cn" : 0.0,
  "enquiry_result_rate_cn" : 0.9417766051011434,
  "completed_order_score_trip" : 0.0,
  "driver_sop_score_trip" : 0.0,
  "completed_pickup_card_score_trip" : 0.0,
  "completed_childseat_score_trip" : 0.0,
  "comment_score_trip" : 0.0,
  "defect_score_trip" : 0.0,
  "vendor_first_reply_score_trip" : 0.0,
  "first_reply_intime_score_trip" : 0.0,
  "enquiry_result_rate_trip" : 0.1666666666666667,
  "driver_service_score_cn" : 0.3076923076923077,
  "driver_service_defect_score_cn" : 5.0,
  "driver_service_gather_score_cn" : 1.8717948717948716,
  "service_provider_im_score_cn" : 2.5,
  "service_provider_enquiry_score_cn" : 4.70888302550572,
  "service_provider_overall_score_cn" : 2.5648535281780673,
  "driver_service_score_trip" : 0.0,
  "driver_service_defect_score_trip" : 5.0,
  "driver_service_gather_score_trip" : 1.6666666666666665,
  "service_provider_im_score_trip" : 0.0,
  "service_provider_enquiry_score_trip" : 0.83333333333333,
  "service_provider_overall_score_trip" : 1.166666666666666,
  "service_provider_overall_score" : 2.5648535281780673,
  "service_provider_level" : "B",
  "is_twice_d_level" : 0,
  "is_add_new_city" : 0,
  "is_new_vendor_name" : "否",
  "is_twice_d_level_name" : "否",
  "is_add_new_city_name" : "否",
  "hive_d" : "2024-10-14",
  "datachange_lasttime" : 1728871804032,
  "use_city_id" : "803"
} ]

"""


    List<Map<String, Object>> getresult() {
        return objectMapper.readValue(platform, new TypeReference<List<Map<String, Object>>>() {})
    }


    static String ctripJson = """[ {
  "id" : 22002,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 8.4,
  "driver_sop_score" : 6,
  "completed_pickup_card_score" : 0.4,
  "completed_childseat_score" : 0.0,
  "comment_score" : 0.0,
  "defect_score" : 0.0,
  "vendor_first_reply_score" : 5.0,
  "first_reply_intime_score" : 5.0,
  "driver_service_score" : 1.1702617153949575,
  "vendor_service_score" : 5.0,
  "vendor_overall_score" : 2.3191832007764703,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-02",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 24648,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 14.0,
  "driver_sop_score" : 14,
  "completed_pickup_card_score" : 0.6000000000000001,
  "completed_childseat_score" : 0.0,
  "comment_score" : 0.0,
  "defect_score" : 0.0,
  "vendor_first_reply_score" : 5.0,
  "first_reply_intime_score" : 5.0,
  "driver_service_score" : 1.****************,
  "vendor_service_score" : 5.0,
  "vendor_overall_score" : 2.51945622319033,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-03",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 27294,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 19.599999999999994,
  "driver_sop_score" : 18,
  "completed_pickup_card_score" : 1.0,
  "completed_childseat_score" : 0.0,
  "comment_score" : 0.0,
  "defect_score" : 0.0,
  "vendor_first_reply_score" : 5.0,
  "first_reply_intime_score" : 5.0,
  "driver_service_score" : 1.586587304671755,
  "vendor_service_score" : 5.0,
  "vendor_overall_score" : 2.6106111132702283,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-04",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 29940,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 25.6,
  "driver_sop_score" : 28,
  "completed_pickup_card_score" : 1.0,
  "completed_childseat_score" : 0.2,
  "comment_score" : 0.5,
  "defect_score" : 0.0,
  "vendor_first_reply_score" : 5.0,
  "first_reply_intime_score" : 4.615384615384616,
  "driver_service_score" : 1.7427251313046983,
  "vendor_service_score" : 4.7435897435897445,
  "vendor_overall_score" : 2.642984514990212,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-05",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 32586,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 31.200000000000014,
  "driver_sop_score" : 38,
  "completed_pickup_card_score" : 1.0,
  "completed_childseat_score" : 0.2,
  "comment_score" : 0.5,
  "defect_score" : 0.0,
  "vendor_first_reply_score" : 5.0,
  "first_reply_intime_score" : 4.***************,
  "driver_service_score" : 1.8506462351830666,
  "vendor_service_score" : 4.6078431372549025,
  "vendor_overall_score" : 2.6778053058046174,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-06",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 35232,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 38.**************,
  "driver_sop_score" : 51,
  "completed_pickup_card_score" : 1.2,
  "completed_childseat_score" : 0.2,
  "comment_score" : 1.0,
  "defect_score" : 0.0,
  "vendor_first_reply_score" : 5.0,
  "first_reply_intime_score" : 4.***************,
  "driver_service_score" : 1.9628426812012425,
  "vendor_service_score" : 4.62962962962963,
  "vendor_overall_score" : 2.7628787657297584,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-07",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 37878,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 42.00000000000001,
  "driver_sop_score" : 54,
  "completed_pickup_card_score" : 1.2000000000000002,
  "completed_childseat_score" : 0.4,
  "comment_score" : 1.5,
  "defect_score" : 0.0,
  "vendor_first_reply_score" : 5.0,
  "first_reply_intime_score" : 4.545454545454545,
  "driver_service_score" : 1.9960736544852753,
  "vendor_service_score" : 4.696969696969697,
  "vendor_overall_score" : 2.8063424672306017,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-08",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 40524,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 44.80000000000001,
  "driver_sop_score" : 59,
  "completed_pickup_card_score" : 1.2,
  "completed_childseat_score" : 0.4,
  "comment_score" : 1.5,
  "defect_score" : -42.0,
  "vendor_first_reply_score" : 5.0,
  "first_reply_intime_score" : 4.6000000000000005,
  "driver_service_score" : 1.8122446968003694,
  "vendor_service_score" : 4.733333333333334,
  "vendor_overall_score" : 2.6885712877602588,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-09",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 43170,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 47.99999999999999,
  "driver_sop_score" : 64,
  "completed_pickup_card_score" : 1.2000000000000002,
  "completed_childseat_score" : 0.4,
  "comment_score" : 2.3,
  "defect_score" : -42.0,
  "vendor_first_reply_score" : 5.0,
  "first_reply_intime_score" : 4.642857142857143,
  "driver_service_score" : 1.8686444383948257,
  "vendor_service_score" : 4.761904761904763,
  "vendor_overall_score" : 2.7366225354478066,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-10",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 45816,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 51.599999999999994,
  "driver_sop_score" : 67,
  "completed_pickup_card_score" : 1.2000000000000002,
  "completed_childseat_score" : 0.4,
  "comment_score" : 2.8,
  "defect_score" : -42.0,
  "vendor_first_reply_score" : 5.0,
  "first_reply_intime_score" : 4.642857142857143,
  "driver_service_score" : 1.9084850188786497,
  "vendor_service_score" : 4.761904761904763,
  "vendor_overall_score" : 2.7645109417864835,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-11",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 48462,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 54.4,
  "driver_sop_score" : 72,
  "completed_pickup_card_score" : 1.2,
  "completed_childseat_score" : 0.4,
  "comment_score" : 2.8,
  "defect_score" : -42.0,
  "vendor_first_reply_score" : 4.84375,
  "first_reply_intime_score" : 4.53125,
  "driver_service_score" : 1.9484129657786011,
  "vendor_service_score" : 4.635416666666667,
  "vendor_overall_score" : 2.7545140760450204,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-12",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 51108,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 57.59999999999996,
  "driver_sop_score" : 78,
  "completed_pickup_card_score" : 1.2000000000000002,
  "completed_childseat_score" : 0.4,
  "comment_score" : 3.3,
  "defect_score" : -60.0,
  "vendor_first_reply_score" : 4.84375,
  "first_reply_intime_score" : 4.53125,
  "driver_service_score" : 1.9057958803678683,
  "vendor_service_score" : 4.635416666666667,
  "vendor_overall_score" : 2.7246821162575077,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-13",
  "datachange_lasttime" : 1728871803999
}, {
  "id" : 53754,
  "region_name" : "西北欧",
  "country_name" : "法国",
  "use_city_name" : "巴黎",
  "vendor_name" : "携程专车",
  "corp_name_use" : "Ctrip Travel holding (hongkong) Ltd.",
  "corp_id_use" : 6206,
  "assess_month" : "2024-10",
  "assess_quarter" : 4,
  "completed_order_score" : 60.79999999999998,
  "driver_sop_score" : 81,
  "completed_pickup_card_score" : 1.4,
  "completed_childseat_score" : 0.4,
  "comment_score" : 3.3,
  "defect_score" : -66.0,
  "vendor_first_reply_score" : 4.848484848484849,
  "first_reply_intime_score" : 4.545454545454545,
  "driver_service_score" : 1.9079485216122722,
  "vendor_service_score" : 4.646464646464647,
  "vendor_overall_score" : 2.7295033590679845,
  "service_provider_level" : "该城市考核月份未配置档位维表",
  "is_twice_c_or_d_level" : 0,
  "is_twice_c_or_d_level_name" : "否",
  "use_city_id" : "192",
  "hive_d" : "2024-10-14",
  "datachange_lasttime" : 1728871803999
} ]"""

    List<Map<String, Object>> getresult1() {
        return objectMapper.readValue(ctripJson, new TypeReference<List<Map<String, Object>>>() {})
    }


    static String index_config = """{
    "sale_30d": {
        "indexType": "firstType",
        "indexKey": "salesScore",
        "indexName": "销售额分",
        "businessGuideComponentType": "bussiness_first",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "dynamicText",
                "businessGuideJudgeType": "section",
                "businessGuideRange": [
                    {
                        "leftRank": 1,
                        "rightRank": 1,
                        "scorevalue": "lastRank",
                        "key": "lastRank"
                    },
                    {
                        "leftRank": 2,
                        "rightRank": 6,
                        "scorevalue": "preRank",
                        "key": "preRank"
                    },
                    {
                        "leftRank": 7,
                        "rightRank": 12,
                        "scorevalue": "6",
                        "key": "distanceRanking"
                    },
                    {
                        "leftRank": 13,
                        "scorevalue": "12",
                        "key": "distanceRanking"
                    }
                ]
            }
        ]
    },
    "atv_90d": {
        "indexType": "secondType",
        "indexKey": "singleCustomerValueScore",
        "indexName": "单客价值分",
        "businessGuideComponentType": "bussiness_second",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "dynamicText",
                "businessGuideJudgeType": "median",
                "businessGuideRange": [
                    {
                        "scorevalue": "up",
                        "key": "singleCustomerValueScoreUp"
                    },
                    {
                        "scorevalue": "down",
                        "key": "singleCustomerValueScoreDown"
                    }
                ]
            }
        ]
    },
    "order_30d": {
        "indexType": "secondType",
        "indexKey": "orderNumScore",
        "indexName": "订单量分",
        "businessGuideComponentType": "bussiness_third",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "dynamicText",
                "businessGuideJudgeType": "section",
                "businessGuideRange": [
                    {
                        "leftRank": 1,
                        "rightRank": 1,
                        "scorevalue": "lastRank",
                        "key": "lastRank"
                    },
                    {
                        "leftRank": 2,
                        "rightRank": 6,
                        "scorevalue": "preRank",
                        "key": "preRank"
                    },
                    {
                        "leftRank": 7,
                        "rightRank": 12,
                        "scorevalue": "6",
                        "key": "distanceRanking"
                    },
                    {
                        "leftRank": 13,
                        "scorevalue": "12",
                        "key": "distanceRanking"
                    }
                ]
            },
            {
                "businessGuideType": "url",
                "name": "大搜投流",
                "urlName": "去大搜投流",
                "key": "searchDrain",
                "url": "https://dimg04.c-ctrip.com/images/1ut2x12000geghm4p6DDC.png"
            },
            {
                "businessGuideType": "url",
                "name": "综搜投流",
                "urlName": "去综搜投流",
                "key": "searchStream",
                "url": "https://vbooking.ctrip.com/micro/vendor/help/vbk/helpCenterDetail?articleId=3949&sourceFormPage=***********&layoutapp=VBK&layoutauth=vbk"
            },
            {
                "businessGuideType": "url",
                "name": "攻略引流",
                "urlName": "去攻略引流",
                "key": "srategyDrainage"
            }
        ]
    },
    "imreply": {
        "indexType": "secondType",
        "indexKey": "IMReplyScore",
        "indexName": "IM回复分",
        "businessGuideComponentType": "bussiness_fourth",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "IMReplyScore"
            },
            {
                "businessGuideType": "url",
                "url": "https://dimg04.c-ctrip.com/images/1ut6c12000gegi8kkD5F2.png"
            }
        ]
    },
    "imtrans": {
        "indexType": "secondType",
        "indexKey": "IMConversionScore",
        "indexName": "IM转化分",
        "businessGuideComponentType": "bussiness_second",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "dynamicText",
                "businessGuideJudgeType": "median",
                "businessGuideRange": [
                    {
                        "scorevalue": "up",
                        "key": "IMConversionScoreUp"
                    },
                    {
                        "scorevalue": "down",
                        "key": "IMConversionScoreDown"
                    }
                ]
            }
        ]
    },
    "grade": {
        "indexType": "firstType",
        "indexKey": "levelScore",
        "indexName": "等级分",
        "businessGuideComponentType": "bussiness_first",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "levelScore"
            }
        ]
    },
    "taken": {
        "indexType": "secondType",
        "indexKey": "acceptOrderScore",
        "indexName": "接单分",
        "businessGuideComponentType": "bussiness_first",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "acceptOrderScore"
            }
        ]
    },
    "tconfirm": {
        "indexType": "secondType",
        "indexKey": "immediateConfirmScore",
        "indexName": "及时确认时间分",
        "businessGuideComponentType": "bussiness_first",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "immediateConfirmScore"
            }
        ]
    },
    "rdis": {
        "indexType": "firstType",
        "indexKey": "dispatchStandardScore",
        "indexName": "派遣规范分",
        "businessGuideComponentType": "bussiness_third",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "dispatchStandardScore"
            },
            {
                "businessGuideType": "url",
                "name": "及时正确派遣",
                "key": "dispatchStandardScoreUrl",
                "urlName": "及时正确派遣",
                "url": "http://vendor.package.ctripcorp.com/vendor/help/vbk/helpCenterDetail?articleId=1982 "
            }
        ]
    },
    "complain": {
        "indexType": "thirdType",
        "indexKey": "complaintScore",
        "businessGuideComponentType": "bussiness_first",
        "indexName": "投诉分",
        "rangeTypeInfo": {
            "rangeType": "valueMatch",
            "range": [
                {
                    "level": "excellent",
                    "scoreLeft": "5",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "complaintScore"
            }
        ]
    },
    "comment": {
        "indexType": "secondType",
        "indexKey": "commentScore",
        "businessGuideComponentType": "bussiness_first",
        "indexName": "点评分",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "commentScore"
            }
        ]
    },
    "prod_trans": {
        "indexType": "secondType",
        "indexKey": "productConversionScore",
        "indexName": "产品转化分",
        "businessGuideComponentType": "bussiness_second",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "dynamicText",
                "businessGuideJudgeType": "median",
                "businessGuideRange": [
                    {
                        "scorevalue": "up",
                        "key": "productConversionScoreUp"
                    },
                    {
                        "scorevalue": "down",
                        "key": "productConversionScoreDown"
                    }
                ]
            }
        ]
    },
    "expo": {
        "indexType": "secondType",
        "indexKey": "exposureClickScore",
        "indexName": "曝光点击分",
        "businessGuideComponentType": "bussiness_second",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "dynamicText",
                "businessGuideJudgeType": "median",
                "businessGuideRange": [
                    {
                        "scorevalue": "up",
                        "key": "exposureClickScoreUp"
                    },
                    {
                        "scorevalue": "down",
                        "key": "exposureClickScoreDown"
                    }
                ]
            }
        ]
    },
    "car_img": {
        "indexType": "fourthType",
        "indexKey": "realTimePhotosOfVehiclesScore",
        "indexName": "车辆实拍图片分",
        "businessGuideComponentType": "bussiness_fourth",
        "rangeTypeInfo": {
            "rangeType": "valueMatch",
            "range": [
                {
                    "level": "excellent",
                    "scoreLeft": "5",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "realTimePhotosOfVehiclesScore"
            },
            {
                "businessGuideType": "url",
                "name": "",
                "key": "",
                "url": "https://dimg04.c-ctrip.com/images/1ut0x12000geghivaCBC5.png"
            }
        ]
    },
    "imp_tag": {
        "indexType": "fifthType",
        "indexKey": "importantLabelCoveragePoints",
        "indexName": "重要标签覆盖分",
        "businessGuideComponentType": "bussiness_fifth",
        "rangeTypeInfo": {
            "rangeType": "valueMatch",
            "range": [
                {
                    "level": "excellent",
                    "scoreLeft": "5",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "table",
                "businessGuideJudgeType": "table_first"
            }
        ]
    },
    "normal_tag": {
        "indexType": "fifthType",
        "indexKey": "commonLabelCoveragePoints",
        "indexName": "普通标签覆盖分",
        "businessGuideComponentType": "bussiness_sixth",
        "rangeTypeInfo": {
            "rangeType": "valueMatch",
            "range": [
                {
                    "level": "excellent",
                    "scoreLeft": "5",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "table",
                "businessGuideJudgeType": "table_second"
            }
        ]
    },
    "coupon": {
        "indexType": "secondType",
        "indexKey": "discountScore",
        "indexName": "优惠分",
        "businessGuideComponentType": "bussiness_fourth",
        "rangeTypeInfo": {
            "rangeType": "section",
            "range": [
                {
                    "rightRank": 6,
                    "rankright": "6",
                    "level": "excellent",
                    "scoreLeft": "rightRank",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "leftRank": 6,
                    "rightRank": 12,
                    "rankleft": "7",
                    "rankright": "12",
                    "level": "good",
                    "scoreLeft": "rightRank",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                },
                {
                    "leftRank": 12,
                    "rankleft": "12",
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "leftRank",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "discountScore"
            },
            {
                "businessGuideType": "url",
                "name": "",
                "key": "",
                "url": "https://dimg04.c-ctrip.com/images/1ut0t12000gegi7na0345.png"
            }
        ]
    },
    "view_img": {
        "indexType": "fifthType",
        "indexKey": "sceneryPicturesScore",
        "indexName": "沿途风光图片分",
        "businessGuideComponentType": "bussiness_first",
        "rangeTypeInfo": {
            "rangeType": "valueMatch",
            "range": [
                {
                    "level": "excellent",
                    "scoreLeft": "5",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "sceneryPicturesScore"
            }
        ]
    },
    "line_img": {
        "indexType": "fifthType",
        "indexKey": "gameplayRoutePictureScore",
        "indexName": "玩法线路图片分",
        "businessGuideComponentType": "bussiness_first",
        "rangeTypeInfo": {
            "rangeType": "valueMatch",
            "range": [
                {
                    "level": "excellent",
                    "scoreLeft": "5",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "gameplayRoutePictureScore"
            }
        ]
    },
    "coop": {
        "indexType": "sixthType",
        "indexKey": "cooperateScore",
        "indexName": "配合分",
        "businessGuideComponentType": "bussiness_first",
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "cooperateScore"
            }
        ]
    },
    "obey": {
        "indexType": "eighthType",
        "indexKey": "disciplineScore",
        "indexName": "守纪分",
        "businessGuideComponentType": "bussiness_first",
        "rangeTypeInfo": {
            "rangeType": "valueMatch",
            "range": [
                {
                    "level": "excellent",
                    "scoreLeft": "5",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": true
                },
                {
                    "level": "poor",
                    "scoreLeft": "0",
                    "scoreRight": "5",
                    "leftInterval": true,
                    "rightInterval": false
                }
            ]
        },
        "businessGuide": [
            {
                "businessGuideType": "staticText",
                "key": "disciplineScore"
            }
        ]
    },
    "newcomer": {
        "indexType": "seventhType",
        "indexKey": "newcomerScore",
        "indexName": "新人分"
    }
}"""

    public OPSIndexScoreConfigTest getOpsIndexScoreConfigTest() {
        return new OPSIndexScoreConfigTest();
    }

    class OPSIndexScoreConfigTest extends OPSIndexScoreConfig {
        public OPSIndexScoreInfoDTO getOPSIndexScoreInfo(String key) {
            Type type = new TypeToken<Map<String, OPSIndexScoreInfoDTO>>() {}.getType();
            Map<String, OPSIndexScoreInfoDTO> result = JsonUtil.fromString(index_config, type);
            return result.get(key);
        }

        public List<String> getFieldList() {
            Type type = new TypeToken<Map<String, OPSIndexScoreInfoDTO>>() {}.getType();
            Map<String, OPSIndexScoreInfoDTO> result = JsonUtil.fromString(index_config, type);
            return result.keySet().stream().toList();
        }

        public String getDBFieldMap(String key) {
            Type type = new TypeToken<Map<String, OPSIndexScoreInfoDTO>>() {}.getType();
            Map<String, OPSIndexScoreInfoDTO> result = JsonUtil.fromString(index_config, type);
            Map<String, String> map = new HashMap<>();
            result.forEach((key1, value) -> map.put(value.getIndexKey(), key1));
            return map.get(key);
        }
    }

    static String order_str = """{
    "sale_30d": {
        "indexName": "销售额分",
        "weightLevel": "weightHigh",
        "order": "A"
    },
    "atv_90d": {
        "indexName": "单客价值分",
        "weightLevel": "weightMiddle",
        "order": "B"
    },
    "order_30d": {
        "indexName": "订单量分",
        "weightLevel": "weightHigh",
        "order": "C"
    },
    "imreply": {
        "indexName": "IM回复分",
        "weightLevel": "weightHigh",
        "order": "D"
    },
    "imtrans": {
        "indexName": "IM转化分",
        "weightLevel": "weightHigh",
        "order": "E"
    },
    "grade": {
        "indexName": "等级分",
        "weightLevel": "weightMiddle",
        "order": "F"
    },
    "taken": {
        "indexName": "接单分",
        "weightLevel": "weightMiddle",
        "order": "G"
    },
    "tconfirm": {
        "indexName": "及时确认时间分",
        "weightLevel": "weightMiddle",
        "order": "H"
    },
    "rdis": {
        "indexName": "派遣规范分",
        "weightLevel": "weightMiddle",
        "order": "I"
    },
    "complain": {
        "indexName": "投诉分",
        "weightLevel": "weightMiddle",
        "order": "J"
    },
    "comment": {
        "indexName": "点评分",
        "weightLevel": "weightHigh",
        "order": "K"
    },
    "prod_trans": {
        "indexName": "产品转化分",
        "weightLevel": "weightHigh",
        "order": "L"
    },
    "expo": {
        "indexName": "曝光点击分",
        "weightLevel": "weightHigh",
        "order": "M"
    },
    "car_img": {
        "indexName": "车辆实拍图片分",
        "weightLevel": "weightLow",
        "order": "N"
    },
    "imp_tag": {
        "indexName": "重要标签覆盖分",
        "weightLevel": "weightMiddle",
        "order": "O"
    },
    "normal_tag": {
        "indexName": "普通标签覆盖分",
        "weightLevel": "weightLow",
        "order": "P"
    },
    "coupon": {
        "indexName": "优惠分",
        "weightLevel": "weightLow",
        "order": "Q"
    },
    "view_img": {
        "indexName": "沿途风光图片分",
        "weightLevel": "weightLow",
        "order": "R"
    },
    "line_img": {
        "indexName": "玩法线路图片分",
        "weightLevel": "weightLow",
        "order": "S"
    },
    "coop": {
        "indexName": "配合分",
        "weightLevel": "weightLow",
        "order": "T"
    },
    "obey": {
        "indexName": "守纪分",
        "weightLevel": "weightMiddle",
        "order": "U"
    },
    "newcomer": {
        "indexName": "新人分",
        "weightLevel": "weightHigh",
        "order": "V"
    }
}"""

    public OPSOrderConfigTest getOpsOrderConfigTest() {
        return new OPSOrderConfigTest();
    }

    class OPSOrderConfigTest extends OPSOrderConfig {
        public OPSOrderDTO getOPSOrderInfo(String key) {
            Type type = new TypeToken<Map<String, OPSOrderDTO>>() {}.getType();
            Map<String, OPSOrderDTO> result = JsonUtil.fromString(order_str, type);
            return result.get(key);
        }
    }

    List<Map<String, Object>> getresult2() {
        return objectMapper.readValue(json_str, new TypeReference<List<Map<String, Object>>>() {})
    }

    List<Map<String, Object>> getresult3() {
        return objectMapper.readValue(json23, new TypeReference<List<Map<String, Object>>>() {})
    }

    static String json_str = """[
    {
        "id": 98954899,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "经营分",
        "index_name_l2": "订单量分",
        "index_name_l3": "订单量分90d",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "订单量,虚假交易订单量",
        "index_id_l1": "business",
        "index_id_l2": "order",
        "index_id_l3": "order_90d",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98954976,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "产品分",
        "index_name_l2": "信息分",
        "index_name_l3": "玩法线路图片分",
        "index_pre_abs_l3": 4,
        "index_abs_l3": 4,
        "index_score_l3": 4,
        "index_list": "满分产品数量,总产品数量",
        "index_id_l1": "product",
        "index_id_l2": "info",
        "index_id_l3": "line_img",
        "ops_rank": 1,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98955018,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "服务分",
        "index_name_l2": "im转化分",
        "index_name_l3": "im转化分",
        "index_pre_abs_l3": 0.11,
        "index_abs_l3": 0.11,
        "index_score_l3": 5,
        "index_list": "咨询转化率,已咨询uv总和",
        "index_id_l1": "service",
        "index_id_l2": "imtrans",
        "index_id_l3": "imtrans",
        "ops_rank": 1,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98955046,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "服务分",
        "index_name_l2": "im回复分",
        "index_name_l3": "im回复分",
        "index_pre_abs_l3": 578.22,
        "index_abs_l3": 578.22,
        "index_score_l3": 3.45,
        "index_list": "平均回复时长,消息数量",
        "index_id_l1": "service",
        "index_id_l2": "imreply",
        "index_id_l3": "imreply",
        "ops_rank": 3,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98955074,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "产品分",
        "index_name_l2": "政策友好分",
        "index_name_l3": "重要标签覆盖分",
        "index_pre_abs_l3": 3,
        "index_abs_l3": 3,
        "index_score_l3": 3,
        "index_list": "满分产品数量,总产品数量",
        "index_id_l1": "product",
        "index_id_l2": "policy",
        "index_id_l3": "imp_tag",
        "ops_rank": 5,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98955123,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "产品分",
        "index_name_l2": "曝光点击分",
        "index_name_l3": "曝光点击分",
        "index_pre_abs_l3": 0.18,
        "index_abs_l3": 0.18,
        "index_score_l3": 1.67,
        "index_list": "产品曝光点击率,有曝光的产品数量",
        "index_id_l1": "product",
        "index_id_l2": "expo",
        "index_id_l3": "expo",
        "ops_rank": 4,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98955151,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "产品分",
        "index_name_l2": "政策友好分",
        "index_name_l3": "优惠分",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "优惠率",
        "index_id_l1": "product",
        "index_id_l2": "policy",
        "index_id_l3": "coupon",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98955193,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "服务分",
        "index_name_l2": "点评分",
        "index_name_l3": "点评分",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 3,
        "index_list": "点评平均分",
        "index_id_l1": "service",
        "index_id_l2": "comment",
        "index_id_l3": "comment",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98955235,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "经营分",
        "index_name_l2": "单客价分",
        "index_name_l3": "单客价分60d",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "客单价,订单量,虚假交易订单量",
        "index_id_l1": "business",
        "index_id_l2": "atv",
        "index_id_l3": "atv_60d",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98955473,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "服务分",
        "index_name_l2": "服务质量分",
        "index_name_l3": "及时确认时间分",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "平均确认时长,已确认订单量",
        "index_id_l1": "service",
        "index_id_l2": "qservice",
        "index_id_l3": "tconfirm",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98955515,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "经营分",
        "index_name_l2": "销售额分",
        "index_name_l3": "销售额分60d",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV",
        "index_id_l1": "business",
        "index_id_l2": "sale",
        "index_id_l3": "sale_60d",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 98955557,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "经营分",
        "index_name_l2": "销售额分",
        "index_name_l3": "销售额分30d",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV",
        "index_id_l1": "business",
        "index_id_l2": "sale",
        "index_id_l3": "sale_30d",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 100924748,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "产品分",
        "index_name_l2": "信息分",
        "index_name_l3": "沿途风光图片分",
        "index_pre_abs_l3": 5,
        "index_abs_l3": 5,
        "index_score_l3": 5,
        "index_list": "满分产品数量,总产品数量",
        "index_id_l1": "product",
        "index_id_l2": "info",
        "index_id_l3": "view_img",
        "ops_rank": 5,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 100924783,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "经营分",
        "index_name_l2": "销售额分",
        "index_name_l3": "销售额分90d",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV",
        "index_id_l1": "business",
        "index_id_l2": "sale",
        "index_id_l3": "sale_90d",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 100924825,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "产品分",
        "index_name_l2": "产品转化分",
        "index_name_l3": "产品转化分",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "产品详情页转化率,产品详情页浏览用户数量",
        "index_id_l1": "product",
        "index_id_l2": "prod_trans",
        "index_id_l3": "prod_trans",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 100924867,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "经营分",
        "index_name_l2": "订单量分",
        "index_name_l3": "订单量分60d",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "订单量,虚假交易订单量",
        "index_id_l1": "business",
        "index_id_l2": "order",
        "index_id_l3": "order_60d",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 100924937,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "服务分",
        "index_name_l2": "司导分",
        "index_name_l3": "等级分",
        "index_pre_abs_l3": 12,
        "index_abs_l3": 12,
        "index_score_l3": 3.44,
        "index_list": "中级认证司导数量,初级认证司导数量,其他司导数量",
        "index_id_l1": "service",
        "index_id_l2": "guide",
        "index_id_l3": "grade",
        "ops_rank": 2,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 100924986,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "产品分",
        "index_name_l2": "信息分",
        "index_name_l3": "车辆实拍图片分",
        "index_pre_abs_l3": 5,
        "index_abs_l3": 5,
        "index_score_l3": 5,
        "index_list": "满分产品数量,总产品数量",
        "index_id_l1": "product",
        "index_id_l2": "info",
        "index_id_l3": "car_img",
        "ops_rank": 1,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 100924993,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "经营分",
        "index_name_l2": "单客价分",
        "index_name_l3": "单客价分90d",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "客单价,订单量,虚假交易订单量",
        "index_id_l1": "business",
        "index_id_l2": "atv",
        "index_id_l3": "atv_90d",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 102854949,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "服务分",
        "index_name_l2": "服务质量分",
        "index_name_l3": "派遣规范分",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 3,
        "index_list": "派遣合规率,需派遣订单量",
        "index_id_l1": "service",
        "index_id_l2": "qservice",
        "index_id_l3": "rdis",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 102854991,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "经营分",
        "index_name_l2": "订单量分",
        "index_name_l3": "订单量分30d",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "订单量,虚假交易订单量",
        "index_id_l1": "business",
        "index_id_l2": "order",
        "index_id_l3": "order_30d",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 102855033,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "合作分",
        "index_name_l2": "守纪分",
        "index_name_l3": "守纪分",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 5,
        "index_list": "违规数量+违规等级",
        "index_id_l1": "cooperate",
        "index_id_l2": "obey",
        "index_id_l3": "obey",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 102855110,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "产品分",
        "index_name_l2": "政策友好分",
        "index_name_l3": "普通标签覆盖分",
        "index_pre_abs_l3": 1.5,
        "index_abs_l3": 1.5,
        "index_score_l3": 1.5,
        "index_list": "满分产品数量,总产品数量",
        "index_id_l1": "product",
        "index_id_l2": "policy",
        "index_id_l3": "normal_tag",
        "ops_rank": 1,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 102855117,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "合作分",
        "index_name_l2": "新人分",
        "index_name_l3": "新人分",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "入驻天数",
        "index_id_l1": "cooperate",
        "index_id_l2": "newcomer",
        "index_id_l3": "newcomer",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 102855159,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "合作分",
        "index_name_l2": "配合分",
        "index_name_l3": "配合分",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "已报名成功的活动数量",
        "index_id_l1": "cooperate",
        "index_id_l2": "coop",
        "index_id_l3": "coop",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 102855201,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "服务分",
        "index_name_l2": "服务质量分",
        "index_name_l3": "投诉分",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 5,
        "index_list": "投诉订单量",
        "index_id_l1": "service",
        "index_id_l2": "qservice",
        "index_id_l3": "complain",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 102855243,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "经营分",
        "index_name_l2": "单客价分",
        "index_name_l3": "单客价分30d",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 0,
        "index_list": "客单价,订单量,虚假交易订单量",
        "index_id_l1": "business",
        "index_id_l2": "atv",
        "index_id_l3": "atv_30d",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    },
    {
        "id": 102855607,
        "date_time": "2024-11-06",
        "vendor_id": 6206,
        "advisor_id": 312098,
        "site_id": 179,
        "site_type": "city",
        "index_name_l1": "服务分",
        "index_name_l2": "服务质量分",
        "index_name_l3": "接单分",
        "index_pre_abs_l3": 0,
        "index_abs_l3": 0,
        "index_score_l3": 3,
        "index_list": "接单率,支付单量",
        "index_id_l1": "service",
        "index_id_l2": "qservice",
        "index_id_l3": "taken",
        "ops_rank": 6,
        "hive_d": "2024-11-06",
        "datachange_lasttime": 1730945397824
    }
]"""


    static String json23 = """[{"id":4313,"index_id_l3":"view_img","index_name_l3":"沿途风光图片分","weight":0.1,"duration":"当前","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4320,"index_id_l3":"car_img","index_name_l3":"车辆实拍图片分","weight":0.1,"duration":"当前","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4327,"index_id_l3":"line_img","index_name_l3":"玩法线路图片分","weight":0.1,"duration":"当前","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4334,"index_id_l3":"newcomer","index_name_l3":"新人分","weight":3.0,"duration":"当前","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4341,"index_id_l3":"obey","index_name_l3":"守纪分","weight":0.1,"duration":"60","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4348,"index_id_l3":"coupon","index_name_l3":"优惠分","weight":0.1,"duration":"30","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4355,"index_id_l3":"complain","index_name_l3":"投诉分","weight":0.5,"duration":"60","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4362,"index_id_l3":"taken","index_name_l3":"接单分","weight":0.1,"duration":"7","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4369,"index_id_l3":"coop","index_name_l3":"配合分","weight":0.1,"duration":"当前","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4376,"index_id_l3":"rdis","index_name_l3":"派遣规范分","weight":0.1,"duration":"60","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4383,"index_id_l3":"tconfirm","index_name_l3":"及时确认时间分","weight":0.1,"duration":"7","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4390,"index_id_l3":"imp_tag","index_name_l3":"重要标签覆盖分","weight":0.1,"duration":"当前","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4397,"index_id_l3":"normal_tag","index_name_l3":"普通标签覆盖分","weight":0.1,"duration":"当前","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4404,"index_id_l3":"prod_trans","index_name_l3":"产品转化分","weight":2.2,"duration":"7","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4411,"index_id_l3":"expo","index_name_l3":"曝光点击分","weight":1.2,"duration":"7","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4418,"index_id_l3":"imreply","index_name_l3":"im回复分","weight":1.0,"duration":"7","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4425,"index_id_l3":"imtrans","index_name_l3":"im转化分","weight":2.7,"duration":"7","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4432,"index_id_l3":"grade","index_name_l3":"等级分","weight":5.58,"duration":"当前","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4439,"index_id_l3":"comment","index_name_l3":"点评分","weight":8.3,"duration":"60","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4446,"index_id_l3":"atv_90d","index_name_l3":"单客价分","weight":4.1,"duration":"90","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4453,"index_id_l3":"order_30d","index_name_l3":"订单量分","weight":3.62,"duration":"30","hive_d":"2024-11-06","datachange_lasttime":1730822562756},{"id":4460,"index_id_l3":"sale_30d","index_name_l3":"销售额分","weight":11.6,"duration":"30","hive_d":"2024-11-06","datachange_lasttime":1730822562756}]"""

    static String json2313 = """{
    "imp_tag": [
        {
            "tagName": "随时退",
            "category": "重要标签",
            "rules": "商品中勾选",
            "labelLocation": "预定规则-退改规则-取消类型-随时退"
        },
        {
            "tagName": "提前1天免费退",
            "category": "重要标签",
            "rules": "商品中勾选",
            "labelLocation": "【预定规则】-【退改规则】-【取消类型-按规则退】-设置对应规则"
        },
        {
            "tagName": "亲子游",
            "category": "重要标签",
            "rules": "系统计算",
            "labelLocation": "符合以下4个条件，外网自动挂亲子游标签<br>1.有有效的商务7座车型；<br>2.服务设施：儿童座椅、婴幼儿推车、儿童玩具、应急药箱、点歌服务、餐巾纸、赠送矿泉水；<br>3.产品信息中出现“亲子”关键字（产品标题、产品经理推荐、详情里的文字）；<br>4.标题中关联了亲子POI（博物馆、采摘、乐园等）"
        }
    ],
    "normal_tag": [
        {
            "tagName": "可订今日",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "可订明日",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "立即确认",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "2年内新车",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "应急药箱",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "可带宠物",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "配充电宝",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "儿童座椅",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "配轮椅",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "婴幼儿推车",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "提供雨具",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "赠矿泉水",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "景区耳麦",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "车载wifi",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "驾龄5年+",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "驾龄10年+",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "免费接机",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "配自拍杆",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "配航拍器",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "配氧气瓶",
            "category": "普通",
            "rules": "商品标签中勾选"
        },
        {
            "tagName": "赠送地图",
            "category": "普通",
            "rules": "商品标签中勾选"
        }
    ]
}"""

    class OPSIndexTableConfigTest extends OPSIndexTableConfig {
        public List<TableDTO> getTableIndexName(String key) {
            Type type = new TypeToken<Map<String, List<TableDTO>>>() {}.getType();
            Map<String, List<TableDTO>> result = JsonUtil.fromString(json2313, type);
            return result.get(key);
        }
    }

    public OPSIndexTableConfigTest getOpsIndexTableConfigTest() {
        OPSIndexTableConfigTest opsIndexTableConfig = new OPSIndexTableConfigTest()
        return opsIndexTableConfig
    }


}
