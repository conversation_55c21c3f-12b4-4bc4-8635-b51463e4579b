package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV1
import com.ctrip.dcs.ops.infrastructure.util.DateUtil
import spock.lang.Specification
import spock.lang.Unroll

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*

class CheckCycleServiceTest extends Specification {
    def testObj = new CheckCycleService()
    def opsExamPlatformGateway = Mock(DaasGatewayV1)

    def setup() {

        testObj.daasGatewayV1 = opsExamPlatformGateway
    }
//
//    @Unroll
//    def "queryPeriodListTest"() {
//        given: "设定相关方法入参"
//        and: "Mock相关接口返回"
//        opsExamPlatformGateway.queryDataByDaas(CHECK_CYCLE_LIST_PLATFORM_DF, _) >> [["assess_month": DateUtil.getLastMonth()], ["assess_month": DateUtil.getCurrentMonth()]]
//        opsExamPlatformGateway.queryDataByDaas(CHECK_CYCLE_LIST_TRIPCAR_DF, _) >> [["assess_month": DateUtil.getLastMonth()], ["assess_month": DateUtil.getCurrentMonth()]]
//        opsExamPlatformGateway.queryDataByDaas(CHECK_CYCLE_LIST_PLATFORM_MI, _) >> [["assess_month": "2024-09"]]
//        opsExamPlatformGateway.queryDataByDaas(CHECK_CYCLE_LIST_TRIPCAR_MI, _) >> [["assess_month": "2024-09"]]
//
//        when:
//        def result = testObj.queryPeriodList(type, partyId)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        type       | partyId || expectedResult
//        "platform" | "6206"  || ["2025-01", "2025-02", "2024-09"] as Set<String>
//        "tripcar"  | "6206"  || ["2025-01", "2025-02", "2024-09"] as Set<String>
//    }
}
