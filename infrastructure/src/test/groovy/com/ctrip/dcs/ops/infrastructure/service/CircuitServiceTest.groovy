package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.dcs.ops.infrastructure.config.CircuitTypeConfig
import com.ctrip.dcs.ops.infrastructure.gateway.CityGateway
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.gateway.NepheleGateway
import com.ctrip.dcs.ops.infrastructure.util.ExcelUtil
import com.ctrip.dcs.ops.infrastructure.value.*
import org.apache.commons.lang3.reflect.FieldUtils
import spock.lang.Specification

class CircuitServiceTest extends Specification {

    CircuitService circuitService
    DaasGatewayV2 daasGatewayV2
    CityGateway cityGateway
    NepheleGateway nepheleGateway
    CircuitTypeConfig circuitTypeConfig

    def setup() {
        circuitService = new CircuitService()
        daasGatewayV2 = Mock(DaasGatewayV2)
        cityGateway = Mock(CityGateway)
        nepheleGateway = Mock(NepheleGateway)
        circuitTypeConfig = Mock(CircuitTypeConfig)

        FieldUtils.writeField(circuitService, "daasGatewayV2", daasGatewayV2, true)
        FieldUtils.writeField(circuitService, "cityGateway", cityGateway, true)
        FieldUtils.writeField(circuitService, "nepheleGateway", nepheleGateway, true)
        FieldUtils.writeField(circuitService, "circuitTypeConfig", circuitTypeConfig, true)
    }

    def "test queryOrderProblemList with city name mapping"() {
        given:
        String partyId = "123"
        String period = "2024-01"
        Long cityId = 1L
        String serviceType = "test"
        String type = "novehicle"
        String locale = "zh-CN"
        Integer pageNo = 1
        Integer size = 10

        // Mock count query result
        List<Map<String, Object>> countResult = [
            ["cnt": 2]
        ]

        // Mock data query result
        List<Map<String, Object>> dataResult = [
            [
                "order_id": 1001L,
                "use_day_local": "2024-01-15",
                "driver_id_last": 2001L,
                "driver_name_last": "张三",
                "type": "novehicle",
                "use_city_id": 101L
            ],
            [
                "order_id": 1002L,
                "use_day_local": "2024-01-16",
                "driver_id_last": 2002L,
                "driver_name_last": "李四",
                "type": "novehicle",
                "use_city_id": 102L
            ]
        ]

        // Mock city data
        City city1 = Mock(City)
        city1.getTranslationName() >> "北京"
        City city2 = Mock(City)
        city2.getTranslationName() >> "上海"
        
        Map<Long, City> cityMap = [
            101L: city1,
            102L: city2
        ]

        when:
        OrderProblemPageDTO result = circuitService.queryOrderProblemList(
                partyId, period, cityId, serviceType, type, locale, pageNo, size, "useDate", "descend"
        )

        then:
        // First call for count
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> countResult
        // Second call for data
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> dataResult
        // Call to get city names
        1 * cityGateway.getCityName([101L, 102L], locale) >> cityMap

        and:
        result != null
        result.pageDTO != null
        result.pageDTO.totalSize == 2
        result.orderProblemResultDTOS != null
        result.orderProblemResultDTOS.size() == 2

        and: "First order problem has correct city name"
        OrderProblemResultDTO firstOrder = result.orderProblemResultDTOS[0]
        firstOrder.orderId == 1001L
        firstOrder.cityId == 101L
        firstOrder.cityName == "北京"
        firstOrder.driverName == "张三"

        and: "Second order problem has correct city name"
        OrderProblemResultDTO secondOrder = result.orderProblemResultDTOS[1]
        secondOrder.orderId == 1002L
        secondOrder.cityId == 102L
        secondOrder.cityName == "上海"
        secondOrder.driverName == "李四"
    }

    def "test queryOrderProblemList with empty result"() {
        given:
        String partyId = "123"
        String period = "2024-01"
        Long cityId = 1L
        String serviceType = "test"
        String type = "novehicle"
        String locale = "zh-CN"
        Integer pageNo = 1
        Integer size = 10

        when:
        OrderProblemPageDTO result = circuitService.queryOrderProblemList(
                partyId, period, cityId, serviceType, type, locale, pageNo, size, "useDate", "descend"
        )

        then:
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> []
        0 * cityGateway.getCityName(_, _)

        and:
        result != null
        result.pageDTO == null
        result.orderProblemResultDTOS == null
    }

    def "test uploadOrderProblemList success"() {
        given:
        String partyId = "123"
        String period = "2024-01"
        Long cityId = 1L
        String serviceType = "test"
        String type = "novehicle"
        String locale = "zh-CN"
        String expectedDownloadUrl = "https://example.com/download/test.xlsx"

        // Mock first page query (count)
        List<Map<String, Object>> countResult = [["cnt": 2]]

        // Mock first page query (data)
        List<Map<String, Object>> dataResult = [
            [
                "order_id": 1001L,
                "use_day_local": "2024-01-15",
                "driver_id_last": 2001L,
                "driver_name_last": "张三",
                "type": "novehicle",
                "use_city_id": 101L
            ],
            [
                "order_id": 1002L,
                "use_day_local": "2024-01-16",
                "driver_id_last": 2002L,
                "driver_name_last": "李四",
                "type": "novehicle",
                "use_city_id": 102L
            ]
        ]

        // Mock city mapping
        Map<Long, City> cityMap = [
            101L: new City(translationName: "北京"),
            102L: new City(translationName: "上海")
        ]

        when:
        String result = circuitService.uploadOrderProblemList(partyId, period, cityId, serviceType, type, locale)

        then:
        // First call for count
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> countResult
        // Second call for data
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> dataResult
        // Call to get city names
        1 * cityGateway.getCityName([101L, 102L], locale) >> cityMap
        // Call to upload Excel file
        1 * nepheleGateway.upload(_, _) >> expectedDownloadUrl

        and:
        result == expectedDownloadUrl
    }

    def "test uploadOrderProblemList with empty data"() {
        given:
        String partyId = "123"
        String period = "2024-01"
        Long cityId = 1L
        String serviceType = "test"
        String type = "novehicle"
        String locale = "zh-CN"

        when:
        circuitService.uploadOrderProblemList(partyId, period, cityId, serviceType, type, locale)

        then:
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> []
        0 * cityGateway.getCityName(_, _)
        0 * nepheleGateway.upload(_, _)

        and:
        RuntimeException ex = thrown()
        ex.message.contains("没有查询到问题订单数据")
    }

    // ========== Protected Methods Tests ==========

    def "test buildBaseParams"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"

        // Mock CircuitTypeConfig
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]

        // Mock queryCircuitMinDate
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        when:
        Map<String, Object> result = circuitService.buildBaseParams(partyId, period, cityId, serviceType)

        then:
        result != null
        result["hiveD"] == "2024-01-01"
        result["corpIdUse"] == 123L
        result["useCityId"] == cityId
        result["serviceType"] == "TAXI"
    }

    def "test queryDataWithEmptyCheck with data"() {
        given:
        String apiName = "testApi"
        Map<String, Object> params = ["key": "value"]
        String defaultResult = "default"
        List<Map<String, Object>> queryResult = [["data": "test"]]

        when:
        String result = circuitService.queryDataWithEmptyCheck(apiName, params, defaultResult) { data ->
            return "processed"
        }

        then:
        1 * daasGatewayV2.queryDataByDaas(apiName, params) >> queryResult
        result == "processed"
    }

    def "test queryDataWithEmptyCheck with empty data"() {
        given:
        String apiName = "testApi"
        Map<String, Object> params = ["key": "value"]
        String defaultResult = "default"

        when:
        String result = circuitService.queryDataWithEmptyCheck(apiName, params, defaultResult) { data ->
            return "processed"
        }

        then:
        1 * daasGatewayV2.queryDataByDaas(apiName, params) >> []
        result == defaultResult
    }

    def "test buildPageInfo"() {
        given:
        Integer total = 25
        Integer size = 10

        when:
        PageDTO result = circuitService.buildPageInfo(total, size)

        then:
        result != null
        result.totalSize == 25
        result.totalPages == 3
    }

    def "test buildPageInfo with exact division"() {
        given:
        Integer total = 20
        Integer size = 10

        when:
        PageDTO result = circuitService.buildPageInfo(total, size)

        then:
        result != null
        result.totalSize == 20
        result.totalPages == 2
    }

    def "test mapToCircuitOverviewDTO"() {
        given:
        Map<String, Object> dataMap = [
            "ven_taken_cnt": 100L,
            "ven_complete_cnt": 95L,
            "indemn_source_after_appe_cnt": 5L,
            "people_vehi_no_match_cnt": 3L,
            "standard_notin_line_cnt": 2L,
            "nocarbf_xnota_cnt": 1L,
            "nocar_after_rate": 0.05,
            "people_vehi_no_match_rate": 0.03,
            "standard_in_line_rate": 0.98,
            "if_bk_nocar_after": 1,
            "if_bk_people_vehi_no_match": 0,
            "if_bk_standard_in_line": 0,
            "if_alarm_nocar_after": 1,
            "if_alarm_people_vehi_no_match": 0,
            "if_alarm_standard_in_line": 0
        ]

        when:
        CircuitOverviewDTO result = circuitService.mapToCircuitOverviewDTO(dataMap)

        then:
        result != null
        result.orderCnt == 100L
        result.orderCompletionCnt == 95L
        result.noVehicleCnt == 5L
        result.vehicleAndPersonNotMatchCnt == 3L
        result.notStandardizedCNt == 2L
        result.otherCnt == 1L
        result.noVehicleRatio == 5.0
        result.vehicleAndPersonNotMatchRatio == 3.0
        result.notStandardizedRatio == 98.0
        result.noVehicleIsCircuited == 1
        result.vehicleAndPersonNotMatchIsCircuited == 0
        result.notStandardizedIsCircuited == 0
        result.noVehicleisWarn == 1
        result.vehicleAndPersonNotMatchisWarn == 0
        result.notStandardizedisWarn == 0
    }

    def "test processProblemOverviewData"() {
        given:
        List<Map<String, Object>> queryResult = [
            ["type": "novehicle", "cnt": 10],
            ["type": "mismatch", "cnt": 5],
            ["type": "nonstandard", "cnt": 3]
        ]
        String locale = "zh-CN"

        // Mock CircuitTypeConfig
        circuitTypeConfig.getProblemTypeMap() >> [
            "novehicle": "到场无车",
            "mismatch": "人车不符",
            "nonstandard": "不规范"
        ]

        when:
        List<ProblemOverviewDTO> result = circuitService.processProblemOverviewData(queryResult, locale)

        then:
        result != null
        result.size() == 3

        // 验证按cnt从大到小排序
        result[0].cnt >= result[1].cnt
        result[1].cnt >= result[2].cnt

        // 验证比例计算
        result.each { dto ->
            assert dto.ratio != null
            assert dto.ratio >= 0
        }
    }

    def "test executePagedQuery with data"() {
        given:
        String apiName = "testApi"
        Map<String, Object> baseParams = ["base": "param"]
        Integer pageNo = 1
        Integer size = 10
        String defaultResult = "default"

        // Mock count result
        List<Map<String, Object>> countResult = [["cnt": 25]]

        // Mock data result
        List<Map<String, Object>> dataResult = [
            ["id": 1, "name": "test1"],
            ["id": 2, "name": "test2"]
        ]

        when:
        String result = circuitService.executePagedQuery(apiName, baseParams, pageNo, size, defaultResult,
            { dataList -> dataList.collect { ["processed": it.id] } },
            { resultList, pageDTO -> "success:${resultList.size()}:${pageDTO.totalSize}" }
        )

        then:
        // First call for count
        1 * daasGatewayV2.queryDataByDaas(apiName, { Map params ->
            params.offsetCnt == 0 && params.limitCnt == 10 && params.countTotal == true
        }) >> countResult

        // Second call for data
        1 * daasGatewayV2.queryDataByDaas(apiName, { Map params ->
            params.offsetCnt == 0 && params.limitCnt == 10 && params.countTotal == false
        }) >> dataResult

        result == "success:2:25"
    }

    def "test executePagedQuery with empty count result"() {
        given:
        String apiName = "testApi"
        Map<String, Object> baseParams = ["base": "param"]
        Integer pageNo = 1
        Integer size = 10
        String defaultResult = "default"

        when:
        String result = circuitService.executePagedQuery(apiName, baseParams, pageNo, size, defaultResult,
            { dataList -> dataList },
            { resultList, pageDTO -> "success" }
        )

        then:
        1 * daasGatewayV2.queryDataByDaas(apiName, _) >> []
        result == defaultResult
    }

    def "test mapToOrderProblemResultDTO"() {
        given:
        Map<String, Object> dataMap = [
            "id": 1001L,
            "order_id": "ORDER123",
            "use_day_local": "2024-01-15",
            "driver_id_last": "DRIVER001",
            "driver_name_last": "张三",
            "type": "novehicle",
            "use_city_id": 101L
        ]
        String locale = "zh-CN"

        // Mock CircuitTypeConfig
        circuitTypeConfig.getOrderProblemTypeMap() >> ["novehicle": "到场无车"]

        when:
        OrderProblemResultDTO result = circuitService.mapToOrderProblemResultDTO(dataMap, locale)

        then:
        result != null
        result.id == 1001L
        result.orderId == "ORDER123"
        result.useDate == "2024-01-15"
        result.driverId == "DRIVER001"
        result.driverName == "张三"
        result.cityId == 101L
    }

    def "test enrichOrderProblemWithCityNames"() {
        given:
        List<OrderProblemResultDTO> resultList = [
            new OrderProblemResultDTO(cityId: 101L),
            new OrderProblemResultDTO(cityId: 102L),
            new OrderProblemResultDTO(cityId: null)
        ]
        String locale = "zh-CN"

        // Mock city data
        City city1 = Mock(City)
        city1.getTranslationName() >> "北京"
        City city2 = Mock(City)
        city2.getTranslationName() >> "上海"

        Map<Long, City> cityMap = [
            101L: city1,
            102L: city2
        ]

        when:
        circuitService.enrichOrderProblemWithCityNames(resultList, locale)

        then:
        1 * cityGateway.getCityName([101L, 102L], locale) >> cityMap

        resultList[0].cityName == "北京"
        resultList[1].cityName == "上海"
        resultList[2].cityName == null
    }

    def "test enrichOrderProblemWithCityNames with empty list"() {
        given:
        List<OrderProblemResultDTO> resultList = []
        String locale = "zh-CN"

        when:
        circuitService.enrichOrderProblemWithCityNames(resultList, locale)

        then:
        0 * cityGateway.getCityName(_, _)
    }

    def "test mapToDriverProblemResultDTO"() {
        given:
        Map<String, Object> dataMap = [
            "driver_id_last": "DRIVER001",
            "driver_name_last": "张三",
            "use_city_id": 101L,
            "standard_notin_line_cnt": "5"
        ]

        when:
        DriverProblemResultDTO result = circuitService.mapToDriverProblemResultDTO(dataMap)

        then:
        result != null
        result.driverId == "DRIVER001"
        result.driverName == "张三"
        result.cityId == 101L
        result.defectCnt == "5"
    }

    def "test enrichDriverProblemWithCityNames"() {
        given:
        List<DriverProblemResultDTO> resultList = [
            new DriverProblemResultDTO(cityId: 101L),
            new DriverProblemResultDTO(cityId: 102L),
            new DriverProblemResultDTO(cityId: null)
        ]
        String locale = "zh-CN"

        // Mock city data
        City city1 = Mock(City)
        city1.getTranslationName() >> "北京"
        City city2 = Mock(City)
        city2.getTranslationName() >> "上海"

        Map<Long, City> cityMap = [
            101L: city1,
            102L: city2
        ]

        when:
        circuitService.enrichDriverProblemWithCityNames(resultList, locale)

        then:
        1 * cityGateway.getCityName([101L, 102L], locale) >> cityMap

        resultList[0].cityName == "北京"
        resultList[1].cityName == "上海"
        resultList[2].cityName == null
    }

    def "test enrichDriverProblemWithCityNames with empty list"() {
        given:
        List<DriverProblemResultDTO> resultList = []
        String locale = "zh-CN"

        when:
        circuitService.enrichDriverProblemWithCityNames(resultList, locale)

        then:
        0 * cityGateway.getCityName(_, _)
    }

    def "test queryOrderProblemListByIdCursor with data"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String type = "novehicle"
        String locale = "zh-CN"
        Long lastId = 100L
        Integer limitSize = 10

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        circuitTypeConfig.getReverseProblemTypeMap() >> ["novehicle": "NO_VEHICLE"]
        circuitTypeConfig.getOrderProblemTypeMap() >> ["NO_VEHICLE": "到场无车"]
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        // Mock query result
        List<Map<String, Object>> dataResult = [
            [
                "id": 101L,
                "order_id": "ORDER123",
                "use_day_local": "2024-01-15",
                "driver_id_last": "DRIVER001",
                "driver_name_last": "张三",
                "type": "NO_VEHICLE",
                "use_city_id": 101L
            ]
        ]

        // Mock city data
        City city = Mock(City)
        city.getTranslationName() >> "北京"
        Map<Long, City> cityMap = [101L: city]

        when:
        List<OrderProblemResultDTO> result = circuitService.queryOrderProblemListByIdCursor(
            partyId, period, cityId, serviceType, type, locale, lastId, limitSize
        )

        then:
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemListByIdCursor", { Map params ->
            params.lastId == lastId && params.limitCnt == limitSize
        }) >> dataResult
        1 * cityGateway.getCityName([101L], locale) >> cityMap

        result != null
        result.size() == 1
        result[0].id == 101L
        result[0].cityName == "北京"
    }

    def "test queryOrderProblemListByIdCursor with empty result"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String type = "novehicle"
        String locale = "zh-CN"
        Long lastId = null
        Integer limitSize = 10

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        circuitTypeConfig.getReverseProblemTypeMap() >> ["novehicle": "NO_VEHICLE"]
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        when:
        List<OrderProblemResultDTO> result = circuitService.queryOrderProblemListByIdCursor(
            partyId, period, cityId, serviceType, type, locale, lastId, limitSize
        )

        then:
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemListByIdCursor", { Map params ->
            params.lastId == 0L && params.limitCnt == limitSize
        }) >> []
        0 * cityGateway.getCityName(_, _)

        result != null
        result.isEmpty()
    }

    def "test streamQueryOrderProblems"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String type = "novehicle"
        String locale = "zh-CN"
        ExcelUtil.StreamExcelDataWriter writer = Mock(ExcelUtil.StreamExcelDataWriter)

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        circuitTypeConfig.getReverseProblemTypeMap() >> ["novehicle": "NO_VEHICLE"]
        circuitTypeConfig.getOrderProblemTypeMap() >> ["NO_VEHICLE": "到场无车"]
        circuitTypeConfig.getLimitSize() >> 2
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        // Mock first batch result (full batch)
        List<Map<String, Object>> firstBatch = [
            [
                "id": 101L,
                "order_id": "ORDER123",
                "use_day_local": "2024-01-15",
                "driver_id_last": "DRIVER001",
                "driver_name_last": "张三",
                "type": "NO_VEHICLE",
                "use_city_id": 101L
            ],
            [
                "id": 102L,
                "order_id": "ORDER124",
                "use_day_local": "2024-01-16",
                "driver_id_last": "DRIVER002",
                "driver_name_last": "李四",
                "type": "NO_VEHICLE",
                "use_city_id": 102L
            ]
        ]

        // Mock second batch result (partial batch - indicates end)
        List<Map<String, Object>> secondBatch = [
            [
                "id": 103L,
                "order_id": "ORDER125",
                "use_day_local": "2024-01-17",
                "driver_id_last": "DRIVER003",
                "driver_name_last": "王五",
                "type": "NO_VEHICLE",
                "use_city_id": 103L
            ]
        ]

        // Mock city data
        City city1 = Mock(City)
        city1.getTranslationName() >> "北京"
        City city2 = Mock(City)
        city2.getTranslationName() >> "上海"
        City city3 = Mock(City)
        city3.getTranslationName() >> "广州"

        Map<Long, City> cityMap1 = [101L: city1, 102L: city2]
        Map<Long, City> cityMap2 = [103L: city3]

        when:
        circuitService.streamQueryOrderProblems(partyId, period, cityId, serviceType, type, locale, writer)

        then:
        // First call with lastId = 0L
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemListByIdCursor", { Map params ->
            params.lastId == 0L && params.limitCnt == 2
        }) >> firstBatch

        // Second call with lastId = 102L
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemListByIdCursor", { Map params ->
            params.lastId == 102L && params.limitCnt == 2
        }) >> secondBatch

        // City name enrichment calls
        1 * cityGateway.getCityName([101L, 102L], locale) >> cityMap1
        1 * cityGateway.getCityName([103L], locale) >> cityMap2

        // Writer calls
        2 * writer.writeDataBatch(_)
    }

    // ========== Public Methods Tests ==========

    def "test queryCircuitOverviewData with data"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        // Mock query result
        List<Map<String, Object>> queryResult = [
            [
                "ven_taken_cnt": 100L,
                "ven_complete_cnt": 95L,
                "indemn_source_after_appe_cnt": 5L,
                "people_vehi_no_match_cnt": 3L,
                "standard_notin_line_cnt": 2L,
                "nocarbf_xnota_cnt": 1L,
                "nocar_after_rate": 0.05,
                "people_vehi_no_match_rate": 0.03,
                "standard_in_line_rate": 0.98,
                "if_bk_nocar_after": 1,
                "if_bk_people_vehi_no_match": 0,
                "if_bk_standard_in_line": 0,
                "if_alarm_nocar_after": 1,
                "if_alarm_people_vehi_no_match": 0,
                "if_alarm_standard_in_line": 0
            ]
        ]

        when:
        CircuitOverviewDTO result = circuitService.queryCircuitOverviewData(partyId, period, cityId, serviceType)

        then:
        1 * daasGatewayV2.queryDataByDaas("queryCircuitOverviewData", _) >> queryResult

        result != null
        result.orderCnt == 100L
        result.orderCompletionCnt == 95L
        result.noVehicleCnt == 5L
        result.vehicleAndPersonNotMatchCnt == 3L
        result.notStandardizedCNt == 2L
        result.otherCnt == 1L
    }

    def "test queryCircuitOverviewData with empty result"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        when:
        CircuitOverviewDTO result = circuitService.queryCircuitOverviewData(partyId, period, cityId, serviceType)

        then:
        1 * daasGatewayV2.queryDataByDaas("queryCircuitOverviewData", _) >> []

        result != null
        result.orderCnt == null
        result.orderCompletionCnt == null
    }

    def "test queryProblemOverviewData with data"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String locale = "zh-CN"

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        circuitTypeConfig.getProblemTypeMap() >> [
            "novehicle": "到场无车",
            "mismatch": "人车不符"
        ]
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        // Mock query result
        List<Map<String, Object>> queryResult = [
            ["type": "novehicle", "cnt": 10],
            ["type": "mismatch", "cnt": 5]
        ]

        when:
        List<ProblemOverviewDTO> result = circuitService.queryProblemOverviewData(partyId, period, cityId, serviceType, locale)

        then:
        1 * daasGatewayV2.queryDataByDaas("queryProblemOverviewData", _) >> queryResult

        result != null
        result.size() == 2
        result[0].cnt >= result[1].cnt // 验证排序
    }

    def "test queryProblemOverviewData with empty result"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String locale = "zh-CN"

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        when:
        List<ProblemOverviewDTO> result = circuitService.queryProblemOverviewData(partyId, period, cityId, serviceType, locale)

        then:
        1 * daasGatewayV2.queryDataByDaas("queryProblemOverviewData", _) >> []

        result != null
        result.isEmpty()
    }

    def "test queryDriverProblemList with data"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String locale = "zh-CN"
        Integer pageNo = 1
        Integer size = 10
        String field = "defectCnt"
        String sortOrder = "descend"

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        // Mock count result
        List<Map<String, Object>> countResult = [["cnt": 2]]

        // Mock data result
        List<Map<String, Object>> dataResult = [
            [
                "driver_id_last": "DRIVER001",
                "driver_name_last": "张三",
                "use_city_id": 101L,
                "standard_notin_line_cnt": "5"
            ],
            [
                "driver_id_last": "DRIVER002",
                "driver_name_last": "李四",
                "use_city_id": 102L,
                "standard_notin_line_cnt": "3"
            ]
        ]

        // Mock city data
        City city1 = Mock(City)
        city1.getTranslationName() >> "北京"
        City city2 = Mock(City)
        city2.getTranslationName() >> "上海"
        Map<Long, City> cityMap = [101L: city1, 102L: city2]

        when:
        DriverProblemPageDTO result = circuitService.queryDriverProblemList(
            partyId, period, cityId, serviceType, locale, pageNo, size, field, sortOrder
        )

        then:
        // First call for count
        1 * daasGatewayV2.queryDataByDaas("queryDrvProblemList", { Map params ->
            params.countTotal == true
        }) >> countResult

        // Second call for data
        1 * daasGatewayV2.queryDataByDaas("queryDrvProblemList", { Map params ->
            params.countTotal == false
        }) >> dataResult

        1 * cityGateway.getCityName([101L, 102L], locale) >> cityMap

        result != null
        result.pageDTO != null
        result.pageDTO.totalSize == 2
        result.driverProblemResultDTOS != null
        result.driverProblemResultDTOS.size() == 2
        result.driverProblemResultDTOS[0].cityName == "北京"
        result.driverProblemResultDTOS[1].cityName == "上海"
    }

    def "test queryDriverProblemList with empty result"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String locale = "zh-CN"
        Integer pageNo = 1
        Integer size = 10
        String field = "defectCnt"
        String sortOrder = "descend"

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        when:
        DriverProblemPageDTO result = circuitService.queryDriverProblemList(
            partyId, period, cityId, serviceType, locale, pageNo, size, field, sortOrder
        )

        then:
        1 * daasGatewayV2.queryDataByDaas("queryDrvProblemList", _) >> []
        0 * cityGateway.getCityName(_, _)

        result != null
        result.pageDTO == null
        result.driverProblemResultDTOS == null
    }

    def "test uploadOrderProblemList with default type parameter"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String locale = "zh-CN"
        String expectedDownloadUrl = "https://example.com/download/test.xlsx"

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        circuitTypeConfig.getLimitSize() >> 100
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        // Mock query result for streaming
        List<Map<String, Object>> dataResult = [
            [
                "id": 1001L,
                "order_id": "ORDER123",
                "use_day_local": "2024-01-15",
                "driver_id_last": "DRIVER001",
                "driver_name_last": "张三",
                "type": "novehicle",
                "use_city_id": 101L
            ]
        ]

        // Mock city data
        City city = Mock(City)
        city.getTranslationName() >> "北京"
        Map<Long, City> cityMap = [101L: city]

        when:
        String result = circuitService.uploadOrderProblemList(partyId, period, cityId, serviceType, locale)

        then:
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemListByIdCursor", _) >> dataResult
        1 * cityGateway.getCityName([101L], locale) >> cityMap
        1 * nepheleGateway.upload(_, _) >> expectedDownloadUrl

        result == expectedDownloadUrl
    }

    def "test uploadOrderProblemList with specific type parameter"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String type = "novehicle"
        String locale = "zh-CN"
        String expectedDownloadUrl = "https://example.com/download/test.xlsx"

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        circuitTypeConfig.getReverseProblemTypeMap() >> ["novehicle": "NO_VEHICLE"]
        circuitTypeConfig.getOrderProblemTypeMap() >> ["NO_VEHICLE": "到场无车"]
        circuitTypeConfig.getLimitSize() >> 100
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        // Mock query result for streaming
        List<Map<String, Object>> dataResult = [
            [
                "id": 1001L,
                "order_id": "ORDER123",
                "use_day_local": "2024-01-15",
                "driver_id_last": "DRIVER001",
                "driver_name_last": "张三",
                "type": "NO_VEHICLE",
                "use_city_id": 101L
            ]
        ]

        // Mock city data
        City city = Mock(City)
        city.getTranslationName() >> "北京"
        Map<Long, City> cityMap = [101L: city]

        when:
        String result = circuitService.uploadOrderProblemList(partyId, period, cityId, serviceType, type, locale)

        then:
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemListByIdCursor", { Map params ->
            params.type == "NO_VEHICLE"
        }) >> dataResult
        1 * cityGateway.getCityName([101L], locale) >> cityMap
        1 * nepheleGateway.upload(_, _) >> expectedDownloadUrl

        result == expectedDownloadUrl
    }

    def "test uploadOrderProblemList with exception handling"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String type = "novehicle"
        String locale = "zh-CN"

        // Mock dependencies
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        circuitTypeConfig.getReverseProblemTypeMap() >> ["novehicle": "NO_VEHICLE"]
        circuitTypeConfig.getLimitSize() >> 100
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        // Mock exception during query
        daasGatewayV2.queryDataByDaas("queryOrderProblemListByIdCursor", _) >> { throw new RuntimeException("Database error") }

        when:
        circuitService.uploadOrderProblemList(partyId, period, cityId, serviceType, type, locale)

        then:
        RuntimeException ex = thrown()
        ex.message.contains("导出问题订单Excel失败")
    }

    // ========== Edge Cases and Error Handling Tests ==========

    def "test buildBaseParams with null serviceType"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = null

        // Mock CircuitTypeConfig
        circuitTypeConfig.getServiceTypeMap() >> [:]

        // Mock queryCircuitMinDate
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]

        when:
        Map<String, Object> result = circuitService.buildBaseParams(partyId, period, cityId, serviceType)

        then:
        result != null
        result["serviceType"] == null
    }

    def "test processProblemOverviewData with empty queryResult"() {
        given:
        List<Map<String, Object>> queryResult = []
        String locale = "zh-CN"

        // Mock CircuitTypeConfig
        circuitTypeConfig.getProblemTypeMap() >> [
            "novehicle": "到场无车",
            "mismatch": "人车不符"
        ]

        when:
        List<ProblemOverviewDTO> result = circuitService.processProblemOverviewData(queryResult, locale)

        then:
        result != null
        result.size() == 2 // 仍然会根据配置创建DTO，但cnt为0
        result.every { it.cnt == 0 }
        result.every { it.ratio == 0.0 }
    }

    def "test enrichOrderProblemWithCityNames with null cityIds"() {
        given:
        List<OrderProblemResultDTO> resultList = [
            new OrderProblemResultDTO(cityId: null),
            new OrderProblemResultDTO(cityId: null)
        ]
        String locale = "zh-CN"

        when:
        circuitService.enrichOrderProblemWithCityNames(resultList, locale)

        then:
        0 * cityGateway.getCityName(_, _)
        resultList.every { it.cityName == null }
    }
}
