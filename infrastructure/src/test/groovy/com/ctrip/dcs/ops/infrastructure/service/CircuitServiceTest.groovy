package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.dcs.ops.infrastructure.gateway.CityGateway
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.gateway.NepheleGateway
import com.ctrip.dcs.ops.infrastructure.value.OrderProblemResultDTO
import com.ctrip.dcs.ops.infrastructure.value.OrderProblemPageDTO
import org.apache.commons.lang3.reflect.FieldUtils
import spock.lang.Specification

class CircuitServiceTest extends Specification {

    CircuitService circuitService
    DaasGatewayV2 daasGatewayV2
    CityGateway cityGateway
    NepheleGateway nepheleGateway

    def setup() {
        circuitService = new CircuitService()
        daasGatewayV2 = Mock(DaasGatewayV2)
        cityGateway = Mock(CityGateway)
        nepheleGateway = Mock(NepheleGateway)

        FieldUtils.writeField(circuitService, "daasGatewayV2", daasGatewayV2, true)
        FieldUtils.writeField(circuitService, "cityGateway", cityGateway, true)
        FieldUtils.writeField(circuitService, "nepheleGateway", nepheleGateway, true)
    }

    def "test queryOrderProblemList with city name mapping"() {
        given:
        String partyId = "123"
        String period = "2024-01"
        Long cityId = 1L
        String serviceType = "test"
        String type = "novehicle"
        String locale = "zh-CN"
        Integer pageNo = 1
        Integer size = 10

        // Mock count query result
        List<Map<String, Object>> countResult = [
            ["cnt": 2]
        ]

        // Mock data query result
        List<Map<String, Object>> dataResult = [
            [
                "order_id": 1001L,
                "use_day_local": "2024-01-15",
                "driver_id_last": 2001L,
                "driver_name_last": "张三",
                "type": "novehicle",
                "use_city_id": 101L
            ],
            [
                "order_id": 1002L,
                "use_day_local": "2024-01-16",
                "driver_id_last": 2002L,
                "driver_name_last": "李四",
                "type": "novehicle",
                "use_city_id": 102L
            ]
        ]

        // Mock city data
        City city1 = Mock(City)
        city1.getTranslationName() >> "北京"
        City city2 = Mock(City)
        city2.getTranslationName() >> "上海"
        
        Map<Long, City> cityMap = [
            101L: city1,
            102L: city2
        ]

        when:
        OrderProblemPageDTO result = circuitService.queryOrderProblemList(
                partyId, period, cityId, serviceType, type, locale, pageNo, size, "useDate", "descend"
        )

        then:
        // First call for count
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> countResult
        // Second call for data
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> dataResult
        // Call to get city names
        1 * cityGateway.getCityName([101L, 102L], locale) >> cityMap

        and:
        result != null
        result.pageDTO != null
        result.pageDTO.totalSize == 2
        result.orderProblemResultDTOS != null
        result.orderProblemResultDTOS.size() == 2

        and: "First order problem has correct city name"
        OrderProblemResultDTO firstOrder = result.orderProblemResultDTOS[0]
        firstOrder.orderId == 1001L
        firstOrder.cityId == 101L
        firstOrder.cityName == "北京"
        firstOrder.driverName == "张三"

        and: "Second order problem has correct city name"
        OrderProblemResultDTO secondOrder = result.orderProblemResultDTOS[1]
        secondOrder.orderId == 1002L
        secondOrder.cityId == 102L
        secondOrder.cityName == "上海"
        secondOrder.driverName == "李四"
    }

    def "test queryOrderProblemList with empty result"() {
        given:
        String partyId = "123"
        String period = "2024-01"
        Long cityId = 1L
        String serviceType = "test"
        String type = "novehicle"
        String locale = "zh-CN"
        Integer pageNo = 1
        Integer size = 10

        when:
        OrderProblemPageDTO result = circuitService.queryOrderProblemList(
                partyId, period, cityId, serviceType, type, locale, pageNo, size, "useDate", "descend"
        )

        then:
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> []
        0 * cityGateway.getCityName(_, _)

        and:
        result != null
        result.pageDTO == null
        result.orderProblemResultDTOS == null
    }

    def "test uploadOrderProblemList success"() {
        given:
        String partyId = "123"
        String period = "2024-01"
        Long cityId = 1L
        String serviceType = "test"
        String type = "novehicle"
        String locale = "zh-CN"
        String expectedDownloadUrl = "https://example.com/download/test.xlsx"

        // Mock first page query (count)
        List<Map<String, Object>> countResult = [["cnt": 2]]

        // Mock first page query (data)
        List<Map<String, Object>> dataResult = [
            [
                "order_id": 1001L,
                "use_day_local": "2024-01-15",
                "driver_id_last": 2001L,
                "driver_name_last": "张三",
                "type": "novehicle",
                "use_city_id": 101L
            ],
            [
                "order_id": 1002L,
                "use_day_local": "2024-01-16",
                "driver_id_last": 2002L,
                "driver_name_last": "李四",
                "type": "novehicle",
                "use_city_id": 102L
            ]
        ]

        // Mock city mapping
        Map<Long, City> cityMap = [
            101L: new City(translationName: "北京"),
            102L: new City(translationName: "上海")
        ]

        when:
        String result = circuitService.uploadOrderProblemList(partyId, period, cityId, serviceType, type, locale)

        then:
        // First call for count
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> countResult
        // Second call for data
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> dataResult
        // Call to get city names
        1 * cityGateway.getCityName([101L, 102L], locale) >> cityMap
        // Call to upload Excel file
        1 * nepheleGateway.upload(_, _) >> expectedDownloadUrl

        and:
        result == expectedDownloadUrl
    }

    def "test uploadOrderProblemList with empty data"() {
        given:
        String partyId = "123"
        String period = "2024-01"
        Long cityId = 1L
        String serviceType = "test"
        String type = "novehicle"
        String locale = "zh-CN"

        when:
        circuitService.uploadOrderProblemList(partyId, period, cityId, serviceType, type, locale)

        then:
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemList", _) >> []
        0 * cityGateway.getCityName(_, _)
        0 * nepheleGateway.upload(_, _)

        and:
        RuntimeException ex = thrown()
        ex.message.contains("没有查询到问题订单数据")
    }
}
