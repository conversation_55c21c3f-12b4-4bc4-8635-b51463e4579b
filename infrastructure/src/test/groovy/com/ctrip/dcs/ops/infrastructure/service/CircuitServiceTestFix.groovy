package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.dcs.ops.infrastructure.config.CircuitTypeConfig
import com.ctrip.dcs.ops.infrastructure.gateway.CityGateway
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.gateway.NepheleGateway
import org.apache.commons.lang3.reflect.FieldUtils
import spock.lang.Specification

/**
 * 简化的测试类，用于验证修复是否有效
 */
class CircuitServiceTestFix extends Specification {

    CircuitService circuitService
    DaasGatewayV2 daasGatewayV2
    CityGateway cityGateway
    NepheleGateway nepheleGateway
    CircuitTypeConfig circuitTypeConfig

    def setup() {
        circuitService = new CircuitService()
        daasGatewayV2 = Mock(DaasGatewayV2)
        cityGateway = Mock(CityGateway)
        nepheleGateway = Mock(NepheleGateway)
        circuitTypeConfig = Mock(CircuitTypeConfig)

        FieldUtils.writeField(circuitService, "daasGatewayV2", daasGatewayV2, true)
        FieldUtils.writeField(circuitService, "cityGateway", cityGateway, true)
        FieldUtils.writeField(circuitService, "nepheleGateway", nepheleGateway, true)
        FieldUtils.writeField(circuitService, "circuitTypeConfig", circuitTypeConfig, true)
    }

    def "test uploadOrderProblemList with default type parameter - fixed"() {
        given:
        String partyId = "123"
        String period = "2024-01-01"
        Long cityId = 1L
        String serviceType = "taxi"
        String locale = "zh-CN"
        String expectedDownloadUrl = "https://example.com/download/test.xlsx"
        
        // Mock dependencies - 确保所有必要的方法都被mock
        circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
        circuitTypeConfig.getOrderProblemTypeMap() >> ["novehicle": "到场无车"]
        circuitTypeConfig.getLimitSize() >> 100
        daasGatewayV2.queryDataByDaas("queryCircuitMinDate", [:]) >> [["minDate": "2024-01-01"]]
        
        // Mock query result for streaming
        List<Map<String, Object>> dataResult = [
            [
                "id": 1001L,
                "order_id": "ORDER123",
                "use_day_local": "2024-01-15",
                "driver_id_last": "DRIVER001",
                "driver_name_last": "张三",
                "type": "novehicle",
                "use_city_id": 101L
            ]
        ]
        
        // Mock city data
        City city = Mock(City)
        city.getTranslationName() >> "北京"
        Map<Long, City> cityMap = [101L: city]

        when:
        String result = circuitService.uploadOrderProblemList(partyId, period, cityId, serviceType, locale)

        then:
        1 * daasGatewayV2.queryDataByDaas("queryOrderProblemListByIdCursor", _) >> dataResult
        1 * cityGateway.getCityName([101L], locale) >> cityMap
        1 * nepheleGateway.upload(_, _) >> expectedDownloadUrl
        
        result == expectedDownloadUrl
    }

    def "test mapToOrderProblemResultDTO - fixed"() {
        given:
        Map<String, Object> dataMap = [
            "id": 1001L,
            "order_id": "ORDER123",
            "use_day_local": "2024-01-15",
            "driver_id_last": "DRIVER001",
            "driver_name_last": "张三",
            "type": "novehicle",
            "use_city_id": 101L
        ]
        String locale = "zh-CN"
        
        // Mock CircuitTypeConfig - 确保getOrderProblemTypeMap不返回null
        circuitTypeConfig.getOrderProblemTypeMap() >> ["novehicle": "到场无车"]

        when:
        def result = circuitService.mapToOrderProblemResultDTO(dataMap, locale)

        then:
        result != null
        result.id == 1001L
        result.orderId == "ORDER123"
        result.useDate == "2024-01-15"
        result.driverId == "DRIVER001"
        result.driverName == "张三"
        result.cityId == 101L
        result.defectType == "到场无车"
    }
}
