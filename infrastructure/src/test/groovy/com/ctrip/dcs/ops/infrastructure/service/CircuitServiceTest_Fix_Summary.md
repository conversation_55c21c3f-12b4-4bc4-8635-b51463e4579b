# CircuitService 测试修复总结

## 问题描述
执行 `test uploadOrderProblemList with default type parameter` 测试方法时报错：

```
java.lang.RuntimeException: 导出问题订单Excel失败: ID游标查询问题订单数据失败，lastOrderId: 0, 错误: Cannot invoke "java.util.Map.get(Object)" because the return value of "com.ctrip.dcs.ops.infrastructure.config.CircuitTypeConfig.getOrderProblemTypeMap()" is null
```

## 根本原因
测试中没有正确mock `CircuitTypeConfig.getOrderProblemTypeMap()` 方法，导致该方法返回null，在 `mapToOrderProblemResultDTO` 方法中调用 `map.get()` 时抛出 `NullPointerException`。

## 修复内容

### 1. 修复的测试方法
以下测试方法已添加必要的mock配置：

1. **test uploadOrderProblemList with default type parameter**
   - 添加 `circuitTypeConfig.getOrderProblemTypeMap() >> ["novehicle": "到场无车"]`

2. **test uploadOrderProblemList with specific type parameter**
   - 已经正确配置了mock

3. **test uploadOrderProblemList with exception handling**
   - 添加 `circuitTypeConfig.getOrderProblemTypeMap() >> ["NO_VEHICLE": "到场无车"]`

4. **test uploadOrderProblemList success**
   - 重构为使用流式查询方式
   - 添加完整的mock配置

5. **test uploadOrderProblemList with empty data**
   - 重构为使用流式查询方式
   - 添加完整的mock配置

6. **test queryOrderProblemList with city name mapping**
   - 添加完整的mock配置
   - 修正测试数据结构

7. **test queryOrderProblemList with empty result**
   - 添加完整的mock配置

8. **test mapToOrderProblemResultDTO**
   - 添加 `circuitTypeConfig.getOrderProblemTypeMap()` mock
   - 添加 `result.defectType != null` 验证

9. **test queryOrderProblemListByIdCursor with data**
   - 已经正确配置了mock

10. **test queryOrderProblemListByIdCursor with empty result**
    - 添加 `circuitTypeConfig.getOrderProblemTypeMap()` mock

### 2. 关键修复点

#### Mock配置完整性
确保所有涉及到 `CircuitTypeConfig` 的方法都被正确mock：
```groovy
circuitTypeConfig.getServiceTypeMap() >> ["taxi": "TAXI"]
circuitTypeConfig.getOrderProblemTypeMap() >> ["novehicle": "到场无车"]
circuitTypeConfig.getReverseProblemTypeMap() >> ["novehicle": "NO_VEHICLE"]
circuitTypeConfig.getLimitSize() >> 100
```

#### 测试方法重构
将使用分页查询的测试方法重构为使用流式查询，以匹配实际的实现：
- 从 `queryOrderProblemList` API 改为 `queryOrderProblemListByIdCursor` API
- 更新测试数据结构和验证逻辑

#### 数据一致性
确保测试数据中的字段类型和实际代码期望的类型一致：
- `order_id` 从 `Long` 改为 `String`
- `driver_id_last` 从 `Long` 改为 `String`
- 添加必要的 `id` 字段

### 3. 创建验证测试
创建了 `CircuitServiceTestFix.groovy` 文件，包含简化的测试用例来验证修复是否有效。

## 修复验证
修复后的测试应该能够正常运行，不再出现 `NullPointerException`。所有涉及到 `uploadOrderProblemList` 和相关方法的测试都应该通过。

## 注意事项
1. 确保所有使用 `CircuitTypeConfig` 的测试方法都正确配置了mock
2. 测试数据结构要与实际代码期望的结构保持一致
3. 流式查询和分页查询的测试方式不同，需要相应调整mock配置
