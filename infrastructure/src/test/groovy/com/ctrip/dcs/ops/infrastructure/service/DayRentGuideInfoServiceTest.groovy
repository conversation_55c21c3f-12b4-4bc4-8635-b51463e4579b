package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.basebiz.implus.vendor.contract.GetVenAgentStatusResponseType
import com.ctrip.basebiz.implus.vendor.contract.VendorAgentStatus
import com.ctrip.dcs.ops.infrastructure.config.OpsCommonConfig
import com.ctrip.dcs.ops.infrastructure.gateway.CrmBackedServiceProxy
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV1
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.gateway.IMPlusVendorServiceProxy
import com.ctrip.dcs.ops.infrastructure.util.MockExcutorService
import com.ctrip.tour.tripservice.crm.backedservice.contract.GetPublishAdvisorListResponseType
import com.ctrip.tour.tripservice.crm.backedservice.contract.dto.AdvisorDTO
import com.ctrip.tour.tripservice.crm.backedservice.contract.dto.BasicDTO
import org.apache.commons.lang3.reflect.FieldUtils
import org.apache.commons.lang3.tuple.Pair
import spock.lang.Unroll

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*

class DayRentGuideInfoServiceTest extends BaseAdapterData {
    def testObj = new DayRentGuideInfoService()
    def daasGatewayV1 = Mock(DaasGatewayV1)
    def opsCommonConfig = Mock(OpsCommonConfig)
    def opsIndexScoreConfig = getOpsIndexScoreConfigTest()
    def crmBackedServiceProxy = Mock(CrmBackedServiceProxy)
    def guideInfoThreadPool = new MockExcutorService()
    def daasGatewayV2 = Mock(DaasGatewayV2)
    def imPlusVendorServiceProxy = Mock(IMPlusVendorServiceProxy)

    def setup() {

        testObj.opsIndexScoreConfig = opsIndexScoreConfig
        FieldUtils.writeField(testObj, "imPlusVendorServiceProxy", imPlusVendorServiceProxy, true)
        testObj.opsCommonConfig = opsCommonConfig
        testObj.crmBackedServiceProxy = crmBackedServiceProxy
        FieldUtils.writeField(testObj, "daasGatewayV2", daasGatewayV2, true)
        testObj.daasGatewayV1 = daasGatewayV1
        testObj.guideInfoThreadPool = guideInfoThreadPool
    }

    @Unroll
    def "queryDataTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasGatewayV1.queryDataByDaas(QUERY_OPS_SORT_TOTAL, _) >> [["id": 10299675, "vendor_id": 6206, "advisor_id": 312098, "district_id": 196, "district_name": 0, "ops_total_score": 1.91, "ops_rank": 8, "advisor_name": "徐丽", "uid": "TODG_kk3zibyvvp1", "hive_d": "2024-11-05", "datachange_lasttime": 1730873550819], ["id": 10507925, "vendor_id": 6206, "advisor_id": 312098, "district_id": 196, "district_name": 0, "ops_total_score": 1.86, "ops_rank": 8, "advisor_name": "徐丽", "uid": "CSM0000000209165", "hive_d": "2024-11-06", "datachange_lasttime": 1730962092911]]
        daasGatewayV2.queryDataByDaas(QUERY_MAX_RANK_OPS_TOTAL, _) >> [["result": 24]]
        opsCommonConfig.getTotalScore() >> 5d
        opsCommonConfig.getOptimizationCnt() >> 3
        crmBackedServiceProxy.getPublishAdvisorList(_) >> new GetPublishAdvisorListResponseType(items: [new AdvisorDTO(basic: new BasicDTO(uid: "CSM0000000209165", logoImgUrl: "logoImgUrl"))])
        daasGatewayV2.queryDataByDaas(QUERY_OPS_TOTAL_SCORE, _) >> [["id": 10507925, "vendor_id": 6206, "advisor_id": 312098, "district_id": 196, "district_name": 0, "ops_total_score": 1.86, "ops_rank": 8, "advisor_name": "徐丽", "uid": "CSM0000000209165", "hive_d": "2024-11-06", "datachange_lasttime": 1730962092911], ["id": 10507932, "vendor_id": 1200937, "advisor_id": 166785, "district_id": 196, "district_name": 0, "ops_total_score": 1.96, "ops_rank": 7, "advisor_name": "张剑", "uid": "TODG_kk4233cwftl", "hive_d": "2024-11-06", "datachange_lasttime": 1730947206210], ["id": 10507939, "vendor_id": 1373445, "advisor_id": 430284, "district_id": 196, "district_name": 0, "ops_total_score": 2.26, "ops_rank": 6, "advisor_name": "唐佳佳", "uid": "CSM0000001170922", "hive_d": "2024-11-06", "datachange_lasttime": 1730888115961], ["id": 10508618, "vendor_id": 1191921, "advisor_id": 7487, "district_id": 196, "district_name": 0, "ops_total_score": 1.56, "ops_rank": 9, "advisor_name": "康庄璐", "uid": "TODG_kk4233cwfts", "hive_d": "2024-11-06", "datachange_lasttime": 1730947206169]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_WEIGHT, _) >> [["id": 4313, "index_id_l3": "view_img", "index_name_l3": "沿途风光图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4320, "index_id_l3": "car_img", "index_name_l3": "车辆实拍图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4327, "index_id_l3": "line_img", "index_name_l3": "玩法线路图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4334, "index_id_l3": "newcomer", "index_name_l3": "新人分", "weight": 3.0, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4341, "index_id_l3": "obey", "index_name_l3": "守纪分", "weight": 0.1, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4348, "index_id_l3": "coupon", "index_name_l3": "优惠分", "weight": 0.1, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4355, "index_id_l3": "complain", "index_name_l3": "投诉分", "weight": 0.5, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4362, "index_id_l3": "taken", "index_name_l3": "接单分", "weight": 0.1, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4369, "index_id_l3": "coop", "index_name_l3": "配合分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4376, "index_id_l3": "rdis", "index_name_l3": "派遣规范分", "weight": 0.1, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4383, "index_id_l3": "tconfirm", "index_name_l3": "及时确认时间分", "weight": 0.1, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4390, "index_id_l3": "imp_tag", "index_name_l3": "重要标签覆盖分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4397, "index_id_l3": "normal_tag", "index_name_l3": "普通标签覆盖分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4404, "index_id_l3": "prod_trans", "index_name_l3": "产品转化分", "weight": 2.2, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4411, "index_id_l3": "expo", "index_name_l3": "曝光点击分", "weight": 1.2, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4418, "index_id_l3": "imreply", "index_name_l3": "im回复分", "weight": 1.0, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4425, "index_id_l3": "imtrans", "index_name_l3": "im转化分", "weight": 2.7, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4432, "index_id_l3": "grade", "index_name_l3": "等级分", "weight": 5.58, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4439, "index_id_l3": "comment", "index_name_l3": "点评分", "weight": 8.3, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4446, "index_id_l3": "atv_90d", "index_name_l3": "单客价分", "weight": 4.1, "duration": "90", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4453, "index_id_l3": "order_30d", "index_name_l3": "订单量分", "weight": 3.62, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4460, "index_id_l3": "sale_30d", "index_name_l3": "销售额分", "weight": 11.6, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL, _) >> [["id": 98960758, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "信息分", "index_name_l3": "车辆实拍图片分", "index_pre_abs_l3": 5.0, "index_abs_l3": 5.0, "index_score_l3": 5.0, "index_list": "满分产品数量,总产品数量", "index_id_l1": "product", "index_id_l2": "info", "index_id_l3": "car_img", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 98960884, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "司导分", "index_name_l3": "等级分", "index_pre_abs_l3": 12.0, "index_abs_l3": 12.0, "index_score_l3": 1.88, "index_list": "中级认证司导数量,初级认证司导数量,其他司导数量", "index_id_l1": "service", "index_id_l2": "guide", "index_id_l3": "grade", "ops_rank": 15, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 98960996, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "政策友好分", "index_name_l3": "优惠分", "index_pre_abs_l3": 1.0, "index_abs_l3": 0.0, "index_score_l3": 1.0, "index_list": "优惠率", "index_id_l1": "product", "index_id_l2": "policy", "index_id_l3": "coupon", "ops_rank": 1, "hive_d": "2024-11-06", "datachange_lasttime": 1730947493550], ["id": 98961164, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "服务质量分", "index_name_l3": "投诉分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 5.0, "index_list": "投诉订单量", "index_id_l1": "service", "index_id_l2": "qservice", "index_id_l3": "complain", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 98961192, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "合作分", "index_name_l2": "守纪分", "index_name_l3": "守纪分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 5.0, "index_list": "违规数量+违规等级", "index_id_l1": "cooperate", "index_id_l2": "obey", "index_id_l3": "obey", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 98961500, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "im转化分", "index_name_l3": "im转化分", "index_pre_abs_l3": 0.11, "index_abs_l3": 0.11, "index_score_l3": 4.02, "index_list": "咨询转化率,已咨询uv总和", "index_id_l1": "service", "index_id_l2": "imtrans", "index_id_l3": "imtrans", "ops_rank": 3, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 98961556, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_30d", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 98961724, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "订单量分", "index_name_l3": "订单量分60d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "order", "index_id_l3": "order_60d", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 98962088, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "服务质量分", "index_name_l3": "接单分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 3.0, "index_list": "接单率,支付单量", "index_id_l1": "service", "index_id_l2": "qservice", "index_id_l3": "taken", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 100933820, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "合作分", "index_name_l2": "配合分", "index_name_l3": "配合分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "已报名成功的活动数量", "index_id_l1": "cooperate", "index_id_l2": "coop", "index_id_l3": "coop", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 100933988, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "单客价分", "index_name_l3": "单客价分60d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "客单价,订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "atv", "index_id_l3": "atv_60d", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 100934142, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "im回复分", "index_name_l3": "im回复分", "index_pre_abs_l3": 578.22, "index_abs_l3": 578.22, "index_score_l3": 1.4, "index_list": "平均回复时长,消息数量", "index_id_l1": "service", "index_id_l2": "imreply", "index_id_l3": "imreply", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 100934233, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "政策友好分", "index_name_l3": "重要标签覆盖分", "index_pre_abs_l3": 3.0, "index_abs_l3": 3.0, "index_score_l3": 5.0, "index_list": "满分产品数量,总产品数量", "index_id_l1": "product", "index_id_l2": "policy", "index_id_l3": "imp_tag", "ops_rank": 16, "hive_d": "2024-11-06", "datachange_lasttime": 1730946786687], ["id": 100934352, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "曝光点击分", "index_name_l3": "曝光点击分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "产品曝光点击率,有曝光的产品数量", "index_id_l1": "product", "index_id_l2": "expo", "index_id_l3": "expo", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 100934681, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "信息分", "index_name_l3": "玩法线路图片分", "index_pre_abs_l3": 5.0, "index_abs_l3": 4.0, "index_score_l3": 4.0, "index_list": "满分产品数量,总产品数量", "index_id_l1": "product", "index_id_l2": "info", "index_id_l3": "line_img", "ops_rank": 4, "hive_d": "2024-11-06", "datachange_lasttime": 1730947683730], ["id": 100934863, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分90d", "index_pre_abs_l3": 200.0, "index_abs_l3": 200.0, "index_score_l3": 1.5, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_90d", "ops_rank": 8, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 100934926, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "产品转化分", "index_name_l3": "产品转化分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "产品详情页转化率,产品详情页浏览用户数量", "index_id_l1": "product", "index_id_l2": "prod_trans", "index_id_l3": "prod_trans", "ops_rank": 21, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 100935080, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "订单量分", "index_name_l3": "订单量分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "order", "index_id_l3": "order_30d", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 100935290, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "服务质量分", "index_name_l3": "及时确认时间分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "平均确认时长,已确认订单量", "index_id_l1": "service", "index_id_l2": "qservice", "index_id_l3": "tconfirm", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 102861361, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "单客价分", "index_name_l3": "单客价分90d", "index_pre_abs_l3": 200.0, "index_abs_l3": 43.24, "index_score_l3": 1.19, "index_list": "客单价,订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "atv", "index_id_l3": "atv_90d", "ops_rank": 8, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 102861424, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "单客价分", "index_name_l3": "单客价分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "客单价,订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "atv", "index_id_l3": "atv_30d", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 102861592, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "政策友好分", "index_name_l3": "普通标签覆盖分", "index_pre_abs_l3": 1.5, "index_abs_l3": 1.5, "index_score_l3": 1.5, "index_list": "满分产品数量,总产品数量", "index_id_l1": "product", "index_id_l2": "policy", "index_id_l3": "normal_tag", "ops_rank": 3, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 102861620, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "合作分", "index_name_l2": "新人分", "index_name_l3": "新人分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "入驻天数", "index_id_l1": "cooperate", "index_id_l2": "newcomer", "index_id_l3": "newcomer", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 102861914, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "点评分", "index_name_l3": "点评分", "index_pre_abs_l3": 5.0, "index_abs_l3": 5.0, "index_score_l3": 5.0, "index_list": "点评平均分", "index_id_l1": "service", "index_id_l2": "comment", "index_id_l3": "comment", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 102862089, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "订单量分", "index_name_l3": "订单量分90d", "index_pre_abs_l3": 1.0, "index_abs_l3": 1.0, "index_score_l3": 0.42, "index_list": "订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "order", "index_id_l3": "order_90d", "ops_rank": 8, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 102862348, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分60d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_60d", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 102862516, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "服务质量分", "index_name_l3": "派遣规范分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 3.0, "index_list": "派遣合规率,需派遣订单量", "index_id_l1": "service", "index_id_l2": "qservice", "index_id_l3": "rdis", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 102862901, "date_time": "2024-11-06", "vendor_id": 6206, "advisor_id": 312098, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "信息分", "index_name_l3": "沿途风光图片分", "index_pre_abs_l3": 5.0, "index_abs_l3": 5.0, "index_score_l3": 5.0, "index_list": "满分产品数量,总产品数量", "index_id_l1": "product", "index_id_l2": "info", "index_id_l3": "view_img", "ops_rank": 20, "hive_d": "2024-11-06", "datachange_lasttime": 1730945397824], ["id": 98960590, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "服务质量分", "index_name_l3": "投诉分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 5.0, "index_list": "投诉订单量", "index_id_l1": "service", "index_id_l2": "qservice", "index_id_l3": "complain", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730866756720], ["id": 98960730, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "信息分", "index_name_l3": "车辆实拍图片分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "满分产品数量,总产品数量", "index_id_l1": "product", "index_id_l2": "info", "index_id_l3": "car_img", "ops_rank": 9, "hive_d": "2024-11-06", "datachange_lasttime": 1730866756720], ["id": 98961101, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "政策友好分", "index_name_l3": "优惠分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "优惠率", "index_id_l1": "product", "index_id_l2": "policy", "index_id_l3": "coupon", "ops_rank": 8, "hive_d": "2024-11-06", "datachange_lasttime": 1730866756720], ["id": 98961304, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "合作分", "index_name_l2": "守纪分", "index_name_l3": "守纪分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 5.0, "index_list": "违规数量+违规等级", "index_id_l1": "cooperate", "index_id_l2": "obey", "index_id_l3": "obey", "ops_rank": 7, "hive_d": "2024-11-06", "datachange_lasttime": 1730866756720], ["id": 98961388, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "im转化分", "index_name_l3": "im转化分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "咨询转化率,已咨询uv总和", "index_id_l1": "service", "index_id_l2": "imtrans", "index_id_l3": "imtrans", "ops_rank": 19, "hive_d": "2024-11-06", "datachange_lasttime": 1730866756720], ["id": 98961528, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "司导分", "index_name_l3": "等级分", "index_pre_abs_l3": 2.5, "index_abs_l3": 2.5, "index_score_l3": 0.45, "index_list": "中级认证司导数量,初级认证司导数量,其他司导数量", "index_id_l1": "service", "index_id_l2": "guide", "index_id_l3": "grade", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730866756720], ["id": 98961654, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_30d", "ops_rank": 9, "hive_d": "2024-11-06", "datachange_lasttime": 1730866756720], ["id": 98961850, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "订单量分", "index_name_l3": "订单量分60d", "index_pre_abs_l3": 2.0, "index_abs_l3": 2.0, "index_score_l3": 1.05, "index_list": "订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "order", "index_id_l3": "order_60d", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730866756720], ["id": 98962221, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "服务质量分", "index_name_l3": "接单分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 3.0, "index_list": "接单率,支付单量", "index_id_l1": "service", "index_id_l2": "qservice", "index_id_l3": "taken", "ops_rank": 4, "hive_d": "2024-11-06", "datachange_lasttime": 1730866756720], ["id": 100933953, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "合作分", "index_name_l2": "配合分", "index_name_l3": "配合分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "已报名成功的活动数量", "index_id_l1": "cooperate", "index_id_l2": "coop", "index_id_l3": "coop", "ops_rank": 4, "hive_d": "2024-11-06", "datachange_lasttime": 1730866776173], ["id": 100934114, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "单客价分", "index_name_l3": "单客价分60d", "index_pre_abs_l3": 590.0, "index_abs_l3": 266.45, "index_score_l3": 2.56, "index_list": "客单价,订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "atv", "index_id_l3": "atv_60d", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730866776173], ["id": 100934149, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "im回复分", "index_name_l3": "im回复分", "index_pre_abs_l3": 834.85, "index_abs_l3": 834.85, "index_score_l3": 1.25, "index_list": "平均回复时长,消息数量", "index_id_l1": "service", "index_id_l2": "imreply", "index_id_l3": "imreply", "ops_rank": 4, "hive_d": "2024-11-06", "datachange_lasttime": 1730866776173], ["id": 100934317, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "政策友好分", "index_name_l3": "重要标签覆盖分", "index_pre_abs_l3": 3.0, "index_abs_l3": 3.0, "index_score_l3": 3.0, "index_list": "满分产品数量,总产品数量", "index_id_l1": "product", "index_id_l2": "policy", "index_id_l3": "imp_tag", "ops_rank": 4, "hive_d": "2024-11-06", "datachange_lasttime": 1730866776173], ["id": 100934422, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "曝光点击分", "index_name_l3": "曝光点击分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "产品曝光点击率,有曝光的产品数量", "index_id_l1": "product", "index_id_l2": "expo", "index_id_l3": "expo", "ops_rank": 13, "hive_d": "2024-11-06", "datachange_lasttime": 1730866776173], ["id": 100934653, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "信息分", "index_name_l3": "玩法线路图片分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "满分产品数量,总产品数量", "index_id_l1": "product", "index_id_l2": "info", "index_id_l3": "line_img", "ops_rank": 8, "hive_d": "2024-11-06", "datachange_lasttime": 1730866776173], ["id": 100934884, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分90d", "index_pre_abs_l3": 1980.0, "index_abs_l3": 1980.0, "index_score_l3": 3.08, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_90d", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730866776173], ["id": 100935045, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "产品转化分", "index_name_l3": "产品转化分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "产品详情页转化率,产品详情页浏览用户数量", "index_id_l1": "product", "index_id_l2": "prod_trans", "index_id_l3": "prod_trans", "ops_rank": 4, "hive_d": "2024-11-06", "datachange_lasttime": 1730866776173], ["id": 100935178, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "订单量分", "index_name_l3": "订单量分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "order", "index_id_l3": "order_30d", "ops_rank": 9, "hive_d": "2024-11-06", "datachange_lasttime": 1730866776173], ["id": 100935423, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "服务质量分", "index_name_l3": "及时确认时间分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "平均确认时长,已确认订单量", "index_id_l1": "service", "index_id_l2": "qservice", "index_id_l3": "tconfirm", "ops_rank": 4, "hive_d": "2024-11-06", "datachange_lasttime": 1730866776173], ["id": 102861242, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "点评分", "index_name_l3": "点评分", "index_pre_abs_l3": 5.0, "index_abs_l3": 5.0, "index_score_l3": 5.0, "index_list": "点评平均分", "index_id_l1": "service", "index_id_l2": "comment", "index_id_l3": "comment", "ops_rank": 1, "hive_d": "2024-11-06", "datachange_lasttime": 1730866794456], ["id": 102861382, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "单客价分", "index_name_l3": "单客价分90d", "index_pre_abs_l3": 660.0, "index_abs_l3": 428.11, "index_score_l3": 3.05, "index_list": "客单价,订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "atv", "index_id_l3": "atv_90d", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730866794456], ["id": 102861522, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "单客价分", "index_name_l3": "单客价分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "客单价,订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "atv", "index_id_l3": "atv_30d", "ops_rank": 9, "hive_d": "2024-11-06", "datachange_lasttime": 1730866794456], ["id": 102861753, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "合作分", "index_name_l2": "新人分", "index_name_l3": "新人分", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "入驻天数", "index_id_l1": "cooperate", "index_id_l2": "newcomer", "index_id_l3": "newcomer", "ops_rank": 4, "hive_d": "2024-11-06", "datachange_lasttime": 1730866794456], ["id": 102861942, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "服务分", "index_name_l2": "服务质量分", "index_name_l3": "派遣规范分", "index_pre_abs_l3": 1.0, "index_abs_l3": 0.47, "index_score_l3": 2.33, "index_list": "派遣合规率,需派遣订单量", "index_id_l1": "service", "index_id_l2": "qservice", "index_id_l3": "rdis", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730866794456], ["id": 102862110, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "订单量分", "index_name_l3": "订单量分90d", "index_pre_abs_l3": 3.0, "index_abs_l3": 3.0, "index_score_l3": 1.67, "index_list": "订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "order", "index_id_l3": "order_90d", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730866794456], ["id": 102862285, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "政策友好分", "index_name_l3": "普通标签覆盖分", "index_pre_abs_l3": 1.0, "index_abs_l3": 1.0, "index_score_l3": 1.0, "index_list": "满分产品数量,总产品数量", "index_id_l1": "product", "index_id_l2": "policy", "index_id_l3": "normal_tag", "ops_rank": 4, "hive_d": "2024-11-06", "datachange_lasttime": 1730866794456], ["id": 102862474, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分60d", "index_pre_abs_l3": 1180.0, "index_abs_l3": 1180.0, "index_score_l3": 2.67, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_60d", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730866794456], ["id": 102862880, "date_time": "2024-11-06", "vendor_id": 1200937, "advisor_id": 166785, "site_id": 196, "site_type": "city", "index_name_l1": "产品分", "index_name_l2": "信息分", "index_name_l3": "沿途风光图片分", "index_pre_abs_l3": 3.0, "index_abs_l3": 3.0, "index_score_l3": 3.0, "index_list": "满分产品数量,总产品数量", "index_id_l1": "product", "index_id_l2": "info", "index_id_l3": "view_img", "ops_rank": 23, "hive_d": "2024-11-06", "datachange_lasttime": 1730866794456]]
        imPlusVendorServiceProxy.getVenAgentStatus(_) >> new GetVenAgentStatusResponseType(statusList: ["CSM0000000209165": new VendorAgentStatus(isWorkTime: 0)])
        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getQueryDate() >> Pair.of("2024-11-05", "2024-11-05 12:23:30")
        spy.setWorkStatus(_) >> Boolean.TRUE
        when:
        def result = spy.queryData(siteId, partyId, locale)

        then: "验证返回结果里属性值是否符合预期"
        result.getOptimization().size() == expectedResult
        where: "表格方式验证多种分支调用场景"
        siteId | partyId | locale  || expectedResult
        196L   | 6206L   | "zh-CN" || 1

    }
}
