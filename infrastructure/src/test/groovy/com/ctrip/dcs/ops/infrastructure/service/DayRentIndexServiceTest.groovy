package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.dcs.ops.infrastructure.config.OpsCommonConfig
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.gateway.IMPlusVendorServiceProxy
import com.ctrip.dcs.ops.infrastructure.gateway.OPSRedisGateway
import com.ctrip.dcs.ops.infrastructure.service.indexhandler.*
import com.ctrip.dcs.ops.infrastructure.util.MockExcutorService
import org.apache.commons.lang3.reflect.FieldUtils
import org.apache.commons.lang3.tuple.Pair
import spock.lang.Unroll

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*

class DayRentIndexServiceTest extends BaseAdapterData {
    def testObj = new DayRentIndexService()
    def opsIndexScoreConfig = getOpsIndexScoreConfigTest()
    def opsRedisGateway = Mock(OPSRedisGateway)
    def opsIndexTableConfig = getOpsIndexTableConfigTest()
    def opsCommonConfig = Mock(OpsCommonConfig)
    def scoreIndexThreadPool = new MockExcutorService()
    def daasGatewayV2 = Mock(DaasGatewayV2)
    def imPlusVendorServiceProxy = Mock(IMPlusVendorServiceProxy)
    def isNewcomerIndexHandler = Spy(IsNewcomerIndexHandler)
    def obeyIndexHandler = Spy(ObeyIndexHandler)
    def remainDaysIndexHandler = Spy(RemainDaysIndexHandler)
    def saleIndexHandler = Spy(SaleIndexHandler)
    def List<IndexHandler> indexHandlerList = Arrays.asList(isNewcomerIndexHandler, obeyIndexHandler, remainDaysIndexHandler, saleIndexHandler)

    def setup() {

        testObj.opsIndexScoreConfig = opsIndexScoreConfig
        FieldUtils.writeField(testObj, "imPlusVendorServiceProxy", imPlusVendorServiceProxy, true)
        testObj.opsCommonConfig = opsCommonConfig
        testObj.opsIndexTableConfig = opsIndexTableConfig
        FieldUtils.writeField(testObj, "daasGatewayV2", daasGatewayV2, true)
        testObj.scoreIndexThreadPool = scoreIndexThreadPool
        testObj.opsRedisGateway = opsRedisGateway
        testObj.indexHandlerList = indexHandlerList
    }

    @Unroll
    def "queryDataTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, _) >> [["id": 99220605, "date_time": "2024-11-06", "vendor_id": 1292508, "advisor_id": 308781, "site_id": 502, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_30d", "ops_rank": 6, "hive_d": "2024-11-06", "datachange_lasttime": 1730866759370], ["id": 99220598, "date_time": "2024-11-06", "vendor_id": 1373445, "advisor_id": 430284, "site_id": 502, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_30d", "ops_rank": 7, "hive_d": "2024-11-06", "datachange_lasttime": 1730866759370]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_FACTOR_BY_INDEX, ["siteId": 151, "vendorId": 1185479, "indexIdL3": "sale_30d", "hiveD": "2024-11-14"]) >> [["id": 646639834, "advisor_id": 312098, "vendor_id": 6206, "site_id": 502, "site_type": "city", "index_id_l3": "sale_30d", "index_name_l3": "销售额分30d", "factor_id": "not_sj_amount", "factor_value": 600.0, "factor_name": "GMV（除升级订单）", "hive_d": "2024-11-06", "datachange_lasttime": 1730945397898], ["id": 646710730, "advisor_id": 312098, "vendor_id": 6206, "site_id": 502, "site_type": "city", "index_id_l3": "sale_30d", "index_name_l3": "销售额分30d", "factor_id": "sj_amount", "factor_value": 0.0, "factor_name": "GMV（升级金银铜）", "hive_d": "2024-11-06", "datachange_lasttime": 1730945397898], ["id": 646765932, "advisor_id": 312098, "vendor_id": 6206, "site_id": 502, "site_type": "city", "index_id_l3": "sale_30d", "index_name_l3": "销售额分30d", "factor_id": "avg_cent", "factor_value": 0.13, "factor_name": "平均毛利率", "hive_d": "2024-11-06", "datachange_lasttime": 1730945397898]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_FACTOR_BY_INDEX, ["siteId": 151, "vendorId": 1185479, "indexIdL3": "obey", "hiveD": "2024-11-14"]) >> [["id": 1115513596, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "a_plus_cnt", "factor_value": 0, "factor_name": "违规类别A+", "hive_d": "2024-11-14", "datachange_lasttime": 1731557711294], ["id": 1120326964, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "a_cnt", "factor_value": 0, "factor_name": "违规类别A", "hive_d": "2024-11-14", "datachange_lasttime": 1731557748092], ["id": 1125140332, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "b_cnt", "factor_value": 0, "factor_name": "违规类别B", "hive_d": "2024-11-14", "datachange_lasttime": 1731557782201], ["id": 1129953000, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "c_cnt", "factor_value": 0, "factor_name": "违规类别C", "hive_d": "2024-11-14", "datachange_lasttime": 1731557821183], ["id": 1134766368, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "d_cnt", "factor_value": 0, "factor_name": "违规类别D", "hive_d": "2024-11-14", "datachange_lasttime": 1731557854830]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_FACTOR_BY_INDEX, ["siteId": 151, "vendorId": 1185479, "indexIdL3": "newcomer", "hiveD": "2024-11-14"]) >> [["id": 1134953982, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "newcomer", "index_name_l3": "新人分", "factor_id": "new_days", "factor_value": 0, "factor_name": "入驻天数", "hive_d": "2024-11-14", "datachange_lasttime": 1731557856252], ["id": 1135168749, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "newcomer", "index_name_l3": "新人分", "factor_id": "is_new", "factor_value": 0, "factor_name": "是否新人", "hive_d": "2024-11-14", "datachange_lasttime": 1731557857826], ["id": 1135383516, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "newcomer", "index_name_l3": "新人分", "factor_id": "remain_days", "factor_value": 0, "factor_name": "剩余加权天数", "hive_d": "2024-11-14", "datachange_lasttime": 1731557859472]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_WEIGHT, _) >> [["id": 4313, "index_id_l3": "view_img", "index_name_l3": "沿途风光图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4320, "index_id_l3": "car_img", "index_name_l3": "车辆实拍图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4327, "index_id_l3": "line_img", "index_name_l3": "玩法线路图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4334, "index_id_l3": "newcomer", "index_name_l3": "新人分", "weight": 3.0, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4341, "index_id_l3": "obey", "index_name_l3": "守纪分", "weight": 0.1, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4348, "index_id_l3": "coupon", "index_name_l3": "优惠分", "weight": 0.1, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4355, "index_id_l3": "complain", "index_name_l3": "投诉分", "weight": 0.5, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4362, "index_id_l3": "taken", "index_name_l3": "接单分", "weight": 0.1, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4369, "index_id_l3": "coop", "index_name_l3": "配合分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4376, "index_id_l3": "rdis", "index_name_l3": "派遣规范分", "weight": 0.1, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4383, "index_id_l3": "tconfirm", "index_name_l3": "及时确认时间分", "weight": 0.1, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4390, "index_id_l3": "imp_tag", "index_name_l3": "重要标签覆盖分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4397, "index_id_l3": "normal_tag", "index_name_l3": "普通标签覆盖分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4404, "index_id_l3": "prod_trans", "index_name_l3": "产品转化分", "weight": 2.2, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4411, "index_id_l3": "expo", "index_name_l3": "曝光点击分", "weight": 1.2, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4418, "index_id_l3": "imreply", "index_name_l3": "im回复分", "weight": 1.0, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4425, "index_id_l3": "imtrans", "index_name_l3": "im转化分", "weight": 2.7, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4432, "index_id_l3": "grade", "index_name_l3": "等级分", "weight": 5.58, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4439, "index_id_l3": "comment", "index_name_l3": "点评分", "weight": 8.3, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4446, "index_id_l3": "atv_90d", "index_name_l3": "单客价分", "weight": 4.1, "duration": "90", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4453, "index_id_l3": "order_30d", "index_name_l3": "订单量分", "weight": 3.62, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4460, "index_id_l3": "sale_30d", "index_name_l3": "销售额分", "weight": 11.6, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, _) >> [["id": 99220605, "date_time": "2024-11-06", "vendor_id": 1292508, "advisor_id": 308781, "site_id": 502, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_30d", "ops_rank": 6, "hive_d": "2024-11-06", "datachange_lasttime": 1730866759370], ["id": 99220598, "date_time": "2024-11-06", "vendor_id": 1373445, "advisor_id": 430284, "site_id": 502, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_30d", "ops_rank": 7, "hive_d": "2024-11-06", "datachange_lasttime": 1730866759370]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_MAX_RANK, _) >> [["opsRank": 7]]
        opsRedisGateway.get(_) >> "12"
        opsRedisGateway.set(_, _, _) >> Boolean.TRUE
        opsCommonConfig.getCurrentDuration() >> "当前"
        opsCommonConfig.getExpireSeconds() >> 0

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getQueryDate() >> Pair.of("2024-11-14", "2024-11-14 12:23:30")
        when:
        def result = spy.queryData(partyId, siteId, indexName, locale)

        then: "验证返回结果里属性值是否符合预期"
        result.getFactorDetails().size() == expectedResult
        where: "表格方式验证多种分支调用场景"
        indexName                      | siteId | partyId  | locale  || expectedResult
        "salesScore"                   | 151L   | 1185479L | "zh-CN" || 3
        "IMConversionScore"            | 151L   | 1185479L | "zh-CN" || 3
        "dispatchStandardScore"        | 151L   | 1185479L | "zh-CN" || 3
        "importantLabelCoveragePoints" | 151L   | 1185479L | "zh-CN" || 3
        "disciplineScore"              | 151L   | 1185479L | "zh-CN" || 3
        "newcomerScore"                | 151L   | 1185479L | "zh-CN" || 3
    }

    @Unroll
    def "queryDataTest1"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, ["indexName": "obey", "siteId": 151, "vendorId": 1185479, "hiveD": "2024-11-14"]) >> [["id": 160593014, "date_time": "2024-11-14", "vendor_id": 1185479, "advisor_id": 7044, "site_id": 151, "site_type": "city", "index_name_l1": "合作分", "index_name_l2": "守纪分", "index_name_l3": "守纪分", "index_pre_abs_l3": 2, "index_abs_l3": 2, "index_score_l3": 3, "index_list": "违规数量+违规等级", "index_id_l1": "cooperate", "index_id_l2": "obey", "index_id_l3": "obey", "ops_rank": 90, "hive_d": "2024-11-14", "datachange_lasttime": 1731557733251]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, ["indexName": "sale_30d", "siteId": 151, "vendorId": 1185479, "hiveD": "2024-11-14"]) >> [["id": 158625223, "date_time": "2024-11-14", "vendor_id": 1185479, "advisor_id": 7044, "site_id": 151, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分30d", "index_pre_abs_l3": 775700, "index_abs_l3": 775700, "index_score_l3": 4.61, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_30d", "ops_rank": 2, "hive_d": "2024-11-14", "datachange_lasttime": 1731557712219]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, ["indexName": "newcomer", "siteId": 151, "vendorId": 1185479, "hiveD": "2024-11-14"]) >> [["id": 156627206, "date_time": "2024-11-14", "vendor_id": 1185479, "advisor_id": 7044, "site_id": 151, "site_type": "city", "index_name_l1": "合作分", "index_name_l2": "新人分", "index_name_l3": "新人分", "index_pre_abs_l3": 0, "index_abs_l3": 0, "index_score_l3": 0, "index_list": "入驻天数", "index_id_l1": "cooperate", "index_id_l2": "newcomer", "index_id_l3": "newcomer", "ops_rank": 3, "hive_d": "2024-11-14", "datachange_lasttime": 1731557690086]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_FACTOR_BY_INDEX, ["siteId": 151, "vendorId": 1185479, "indexIdL3": "sale_30d", "hiveD": "2024-11-14"]) >> [["id": 646639834, "advisor_id": 312098, "vendor_id": 6206, "site_id": 502, "site_type": "city", "index_id_l3": "sale_30d", "index_name_l3": "销售额分30d", "factor_id": "not_sj_amount", "factor_value": 600.0, "factor_name": "GMV（除升级订单）", "hive_d": "2024-11-06", "datachange_lasttime": 1730945397898], ["id": 646710730, "advisor_id": 312098, "vendor_id": 6206, "site_id": 502, "site_type": "city", "index_id_l3": "sale_30d", "index_name_l3": "销售额分30d", "factor_id": "sj_amount", "factor_value": 0.0, "factor_name": "GMV（升级金银铜）", "hive_d": "2024-11-06", "datachange_lasttime": 1730945397898], ["id": 646765932, "advisor_id": 312098, "vendor_id": 6206, "site_id": 502, "site_type": "city", "index_id_l3": "sale_30d", "index_name_l3": "销售额分30d", "factor_id": "avg_cent", "factor_value": 0.13, "factor_name": "平均毛利率", "hive_d": "2024-11-06", "datachange_lasttime": 1730945397898]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_FACTOR_BY_INDEX, ["siteId": 151, "vendorId": 1185479, "indexIdL3": "obey", "hiveD": "2024-11-14"]) >> [["id": 1115513596, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "a_plus_cnt", "factor_value": 0, "factor_name": "违规类别A+", "hive_d": "2024-11-14", "datachange_lasttime": 1731557711294], ["id": 1120326964, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "a_cnt", "factor_value": 0, "factor_name": "违规类别A", "hive_d": "2024-11-14", "datachange_lasttime": 1731557748092], ["id": 1125140332, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "b_cnt", "factor_value": 0, "factor_name": "违规类别B", "hive_d": "2024-11-14", "datachange_lasttime": 1731557782201], ["id": 1129953000, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "c_cnt", "factor_value": 0, "factor_name": "违规类别C", "hive_d": "2024-11-14", "datachange_lasttime": 1731557821183], ["id": 1134766368, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "d_cnt", "factor_value": 0, "factor_name": "违规类别D", "hive_d": "2024-11-14", "datachange_lasttime": 1731557854830]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_FACTOR_BY_INDEX, ["siteId": 151, "vendorId": 1185479, "indexIdL3": "newcomer", "hiveD": "2024-11-14"]) >> [["id": 1134953982, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "newcomer", "index_name_l3": "新人分", "factor_id": "new_days", "factor_value": 0, "factor_name": "入驻天数", "hive_d": "2024-11-14", "datachange_lasttime": 1731557856252], ["id": 1135168749, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "newcomer", "index_name_l3": "新人分", "factor_id": "is_new", "factor_value": 0, "factor_name": "是否新人", "hive_d": "2024-11-14", "datachange_lasttime": 1731557857826], ["id": 1135383516, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "newcomer", "index_name_l3": "新人分", "factor_id": "remain_days", "factor_value": 0, "factor_name": "剩余加权天数", "hive_d": "2024-11-14", "datachange_lasttime": 1731557859472]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_WEIGHT, _) >> [["id": 4313, "index_id_l3": "view_img", "index_name_l3": "沿途风光图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4320, "index_id_l3": "car_img", "index_name_l3": "车辆实拍图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4327, "index_id_l3": "line_img", "index_name_l3": "玩法线路图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4334, "index_id_l3": "newcomer", "index_name_l3": "新人分", "weight": 3.0, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4341, "index_id_l3": "obey", "index_name_l3": "守纪分", "weight": 0.1, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4348, "index_id_l3": "coupon", "index_name_l3": "优惠分", "weight": 0.1, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4355, "index_id_l3": "complain", "index_name_l3": "投诉分", "weight": 0.5, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4362, "index_id_l3": "taken", "index_name_l3": "接单分", "weight": 0.1, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4369, "index_id_l3": "coop", "index_name_l3": "配合分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4376, "index_id_l3": "rdis", "index_name_l3": "派遣规范分", "weight": 0.1, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4383, "index_id_l3": "tconfirm", "index_name_l3": "及时确认时间分", "weight": 0.1, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4390, "index_id_l3": "imp_tag", "index_name_l3": "重要标签覆盖分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4397, "index_id_l3": "normal_tag", "index_name_l3": "普通标签覆盖分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4404, "index_id_l3": "prod_trans", "index_name_l3": "产品转化分", "weight": 2.2, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4411, "index_id_l3": "expo", "index_name_l3": "曝光点击分", "weight": 1.2, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4418, "index_id_l3": "imreply", "index_name_l3": "im回复分", "weight": 1.0, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4425, "index_id_l3": "imtrans", "index_name_l3": "im转化分", "weight": 2.7, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4432, "index_id_l3": "grade", "index_name_l3": "等级分", "weight": 5.58, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4439, "index_id_l3": "comment", "index_name_l3": "点评分", "weight": 8.3, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4446, "index_id_l3": "atv_90d", "index_name_l3": "单客价分", "weight": 4.1, "duration": "90", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4453, "index_id_l3": "order_30d", "index_name_l3": "订单量分", "weight": 3.62, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4460, "index_id_l3": "sale_30d", "index_name_l3": "销售额分", "weight": 11.6, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_MAX_RANK, _) >> [["opsRank": 7]]
        opsRedisGateway.get(_) >> "12"
        opsRedisGateway.set(_, _, _) >> Boolean.TRUE
        opsCommonConfig.getCurrentDuration() >> "当前"
        opsCommonConfig.getExpireSeconds() >> 0

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getQueryDate() >> Pair.of("2024-11-14", "2024-11-14 12:23:30")
        when:
        def result = spy.queryData(partyId, siteId, indexName, locale)

        then: "验证返回结果里属性值是否符合预期"
        result.getFactorDetails().size() == expectedResult
        where: "表格方式验证多种分支调用场景"
        indexName         | siteId | partyId  | locale  || expectedResult
        "disciplineScore" | 151L   | 1185479L | "zh-CN" || 0
        "newcomerScore"   | 151L   | 1185479L | "zh-CN" || 2
        "salesScore"      | 151L   | 1185479L | "zh-CN" || 3
    }

    @Unroll
    def "queryDataTest21"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, ["indexName": "obey", "siteId": 151, "vendorId": 1185479, "hiveD": "2024-11-14"]) >> [["id": 160593014, "date_time": "2024-11-14", "vendor_id": 1185479, "advisor_id": 7044, "site_id": 151, "site_type": "city", "index_name_l1": "合作分", "index_name_l2": "守纪分", "index_name_l3": "守纪分", "index_pre_abs_l3": 2, "index_abs_l3": 2, "index_score_l3": 3, "index_list": "违规数量+违规等级", "index_id_l1": "cooperate", "index_id_l2": "obey", "index_id_l3": "obey", "ops_rank": 90, "hive_d": "2024-11-14", "datachange_lasttime": 1731557733251]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, ["indexName": "sale_30d", "siteId": 151, "vendorId": 1185479, "hiveD": "2024-11-14"]) >> [["id": 158625223, "date_time": "2024-11-14", "vendor_id": 1185479, "advisor_id": 7044, "site_id": 151, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "销售额分", "index_name_l3": "销售额分30d", "index_pre_abs_l3": 775700, "index_abs_l3": 775700, "index_score_l3": 4.61, "index_list": "非代订品类GMV(除升级订单),代订品类GMV(除升级订单),升级金银铜GMV,虚假交易GMV", "index_id_l1": "business", "index_id_l2": "sale", "index_id_l3": "sale_30d", "ops_rank": 2, "hive_d": "2024-11-14", "datachange_lasttime": 1731557712219]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, ["indexName": "newcomer", "siteId": 151, "vendorId": 1185479, "hiveD": "2024-11-14"]) >> [["id": 156627206, "date_time": "2024-11-14", "vendor_id": 1185479, "advisor_id": 7044, "site_id": 151, "site_type": "city", "index_name_l1": "合作分", "index_name_l2": "新人分", "index_name_l3": "新人分", "index_pre_abs_l3": 0, "index_abs_l3": 0, "index_score_l3": 0, "index_list": "入驻天数", "index_id_l1": "cooperate", "index_id_l2": "newcomer", "index_id_l3": "newcomer", "ops_rank": 3, "hive_d": "2024-11-14", "datachange_lasttime": 1731557690086]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_FACTOR_BY_INDEX, ["siteId": 151, "vendorId": 1185479, "indexIdL3": "sale_30d", "hiveD": "2024-11-14"]) >> [["id": 646639834, "advisor_id": 312098, "vendor_id": 6206, "site_id": 502, "site_type": "city", "index_id_l3": "sale_30d", "index_name_l3": "销售额分30d", "factor_id": "not_sj_amount", "factor_value": 600.0, "factor_name": "GMV（除升级订单）", "hive_d": "2024-11-06", "datachange_lasttime": 1730945397898], ["id": 646710730, "advisor_id": 312098, "vendor_id": 6206, "site_id": 502, "site_type": "city", "index_id_l3": "sale_30d", "index_name_l3": "销售额分30d", "factor_id": "sj_amount", "factor_value": 0.0, "factor_name": "GMV（升级金银铜）", "hive_d": "2024-11-06", "datachange_lasttime": 1730945397898], ["id": 646765932, "advisor_id": 312098, "vendor_id": 6206, "site_id": 502, "site_type": "city", "index_id_l3": "sale_30d", "index_name_l3": "销售额分30d", "factor_id": "avg_cent", "factor_value": 0.13, "factor_name": "平均毛利率", "hive_d": "2024-11-06", "datachange_lasttime": 1730945397898]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_FACTOR_BY_INDEX, ["siteId": 151, "vendorId": 1185479, "indexIdL3": "obey", "hiveD": "2024-11-14"]) >> [["id": 1115513596, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "a_plus_cnt", "factor_value": 0, "factor_name": "违规类别A+", "hive_d": "2024-11-14", "datachange_lasttime": 1731557711294], ["id": 1120326964, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "a_cnt", "factor_value": 0, "factor_name": "违规类别A", "hive_d": "2024-11-14", "datachange_lasttime": 1731557748092], ["id": 1125140332, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "b_cnt", "factor_value": 0, "factor_name": "违规类别B", "hive_d": "2024-11-14", "datachange_lasttime": 1731557782201], ["id": 1129953000, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "c_cnt", "factor_value": 0, "factor_name": "违规类别C", "hive_d": "2024-11-14", "datachange_lasttime": 1731557821183], ["id": 1134766368, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "obey", "index_name_l3": "守纪分", "factor_id": "d_cnt", "factor_value": 0, "factor_name": "违规类别D", "hive_d": "2024-11-14", "datachange_lasttime": 1731557854830]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_FACTOR_BY_INDEX, ["siteId": 151, "vendorId": 1185479, "indexIdL3": "newcomer", "hiveD": "2024-11-14"]) >> [["id": 1134953982, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "newcomer", "index_name_l3": "新人分", "factor_id": "new_days", "factor_value": 0, "factor_name": "入驻天数", "hive_d": "2024-11-14", "datachange_lasttime": 1731557856252], ["id": 1135168749, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "newcomer", "index_name_l3": "新人分", "factor_id": "is_new", "factor_value": 1, "factor_name": "是否新人", "hive_d": "2024-11-14", "datachange_lasttime": 1731557857826], ["id": 1135383516, "advisor_id": 7044, "vendor_id": 1185479, "site_id": 151, "site_type": "city", "index_id_l3": "newcomer", "index_name_l3": "新人分", "factor_id": "remain_days", "factor_value": 0, "factor_name": "剩余加权天数", "hive_d": "2024-11-14", "datachange_lasttime": 1731557859472]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_WEIGHT, _) >> [["id": 4313, "index_id_l3": "view_img", "index_name_l3": "沿途风光图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4320, "index_id_l3": "car_img", "index_name_l3": "车辆实拍图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4327, "index_id_l3": "line_img", "index_name_l3": "玩法线路图片分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4334, "index_id_l3": "newcomer", "index_name_l3": "新人分", "weight": 3.0, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4341, "index_id_l3": "obey", "index_name_l3": "守纪分", "weight": 0.1, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4348, "index_id_l3": "coupon", "index_name_l3": "优惠分", "weight": 0.1, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4355, "index_id_l3": "complain", "index_name_l3": "投诉分", "weight": 0.5, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4362, "index_id_l3": "taken", "index_name_l3": "接单分", "weight": 0.1, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4369, "index_id_l3": "coop", "index_name_l3": "配合分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4376, "index_id_l3": "rdis", "index_name_l3": "派遣规范分", "weight": 0.1, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4383, "index_id_l3": "tconfirm", "index_name_l3": "及时确认时间分", "weight": 0.1, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4390, "index_id_l3": "imp_tag", "index_name_l3": "重要标签覆盖分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4397, "index_id_l3": "normal_tag", "index_name_l3": "普通标签覆盖分", "weight": 0.1, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4404, "index_id_l3": "prod_trans", "index_name_l3": "产品转化分", "weight": 2.2, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4411, "index_id_l3": "expo", "index_name_l3": "曝光点击分", "weight": 1.2, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4418, "index_id_l3": "imreply", "index_name_l3": "im回复分", "weight": 1.0, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4425, "index_id_l3": "imtrans", "index_name_l3": "im转化分", "weight": 2.7, "duration": "7", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4432, "index_id_l3": "grade", "index_name_l3": "等级分", "weight": 5.58, "duration": "当前", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4439, "index_id_l3": "comment", "index_name_l3": "点评分", "weight": 8.3, "duration": "60", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4446, "index_id_l3": "atv_90d", "index_name_l3": "单客价分", "weight": 4.1, "duration": "90", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4453, "index_id_l3": "order_30d", "index_name_l3": "订单量分", "weight": 3.62, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756], ["id": 4460, "index_id_l3": "sale_30d", "index_name_l3": "销售额分", "weight": 11.6, "duration": "30", "hive_d": "2024-11-06", "datachange_lasttime": 1730822562756]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_MAX_RANK, _) >> [["opsRank": 7]]
        opsRedisGateway.get(_) >> "12"
        opsRedisGateway.set(_, _, _) >> Boolean.TRUE
        opsCommonConfig.getCurrentDuration() >> "当前"
        opsCommonConfig.getExpireSeconds() >> 0

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getQueryDate() >> Pair.of("2024-11-14", "2024-11-14 12:23:30")
        when:
        def result = spy.queryData(partyId, siteId, indexName, locale)

        then: "验证返回结果里属性值是否符合预期"
        result.getFactorDetails().size() == expectedResult
        where: "表格方式验证多种分支调用场景"
        indexName         | siteId | partyId  | locale  || expectedResult
        "disciplineScore" | 151L   | 1185479L | "zh-CN" || 0
        "newcomerScore"   | 151L   | 1185479L | "zh-CN" || 3
        "salesScore"      | 151L   | 1185479L | "zh-CN" || 3
    }
}
