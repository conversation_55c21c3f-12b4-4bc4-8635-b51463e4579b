package com.ctrip.dcs.ops.infrastructure.service


import com.ctrip.dcs.ops.infrastructure.config.OpsCommonConfig
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.gateway.IMPlusVendorServiceProxy
import com.ctrip.dcs.ops.infrastructure.util.MockExcutorService
import org.apache.commons.lang3.reflect.FieldUtils
import org.apache.commons.lang3.tuple.Pair
import spock.lang.Unroll

import java.util.concurrent.CompletableFuture

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*

class DayrentScoreServiceTest extends BaseAdapterData {
    def testObj = new DayrentScoreService()
    def opsIndexScoreConfig = getOpsIndexScoreConfigTest()
    def opsOrderConfig = getOpsOrderConfigTest()
    def scoreCalThreadPool = new MockExcutorService()
    def daasGatewayV2 = Mock(DaasGatewayV2)
    def imPlusVendorServiceProxy = Mock(IMPlusVendorServiceProxy)
    def opsCommonConfig = Mock(OpsCommonConfig)

    def setup() {

        testObj.opsIndexScoreConfig = opsIndexScoreConfig
        testObj.opsOrderConfig = opsOrderConfig
        testObj.scoreCalThreadPool = scoreCalThreadPool
        FieldUtils.writeField(testObj, "imPlusVendorServiceProxy", imPlusVendorServiceProxy, true)
        FieldUtils.writeField(testObj, "opsCommonConfig", opsCommonConfig, true)
        FieldUtils.writeField(testObj, "daasGatewayV2", daasGatewayV2, true)
        FieldUtils.writeField(testObj, "scoreCalThreadPool", scoreCalThreadPool, true)
    }

    @Unroll
    def "queryDataTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL, _) >> getresult2()
        daasGatewayV2.queryDataByDaas(QUERY_OPS_SORT_WEIGHT, _) >> getresult3()
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_MAX_RANK, _) >> [["opsRank": 6]]
        daasGatewayV2.queryDataByDaas(QUERY_OPS_DETAIL_INDEX_DATA, _) >> [["id": 102854998, "date_time": "2024-11-06", "vendor_id": 1373445, "advisor_id": 430284, "site_id": 179, "site_type": "city", "index_name_l1": "经营分", "index_name_l2": "订单量分", "index_name_l3": "订单量分30d", "index_pre_abs_l3": 0.0, "index_abs_l3": 0.0, "index_score_l3": 0.0, "index_list": "订单量,虚假交易订单量", "index_id_l1": "business", "index_id_l2": "order", "index_id_l3": "order_30d", "ops_rank": 5, "hive_d": "2024-11-06", "datachange_lasttime": 1730866794394]]
        def async = CompletableFuture.supplyAsync(() -> { })
        async.get() >> new ArrayList<>()

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getQueryDate() >> Pair.of("2024-11-05", "2024-11-05 12:23:30")
//        spy.query(_, _, _, _, _, _) >> new TeamDTO()
        when:
        def result = spy.queryData(partyId, siteId)


        then: "验证返回结果里属性值是否符合预期"
        result.getScoreDetailDTOS().size() == expectedResult
        where: "表格方式验证多种分支调用场景"
        siteId | partyId || expectedResult
        123L   | "12123" || 22
    }
}
