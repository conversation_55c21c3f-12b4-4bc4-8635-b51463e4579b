package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.value.DriverDetailQueryReq
import spock.lang.Unroll

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/30 15:31
 * @Description:
 */
class DriverServiceTest extends BaseAdapterData {
    def testObj = new DriverService()
    def daasGatewayV2 = Mock(DaasGatewayV2)

    def setup() {
        testObj.daasGatewayV2 = daasGatewayV2
    }

    @Unroll
    def "queryDriverDetailTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasGatewayV2.queryDataByDaas(_, _) >> [["minDate": "2024-12-30", "hive_d":"2024-12-29", "service_point":20, "service_good_point":-10,"join_grabe_point":30,"standard_in_line_num":2L,"standard_in_line_den":3L],
                                                ["minDate": "2024-12-30", "hive_d":"2024-12-28", "service_point":30, "service_good_point":-30,"join_grabe_point":40,"standard_in_line_num":2L,"standard_in_line_den":3L]]

        when:
        def result = testObj.queryDriverDetail(req)

        then: "验证返回结果里属性值是否符合预期"
        result.getSopPassRate() == sopPassRate
        result.getDriverScoreDetails().stream().findFirst().get().fetchDayToDayRatio() == driverPointRatio
        where: "表格方式验证多种分支调用场景"
        req || sopPassRate | driverPointRatio
        new DriverDetailQueryReq(driverId: 1L, supplierId: 1L, startDate: "2024-12-29", endDate: "2024-12-29", cityId:2L) || "66.67" | -33.33
    }

}
