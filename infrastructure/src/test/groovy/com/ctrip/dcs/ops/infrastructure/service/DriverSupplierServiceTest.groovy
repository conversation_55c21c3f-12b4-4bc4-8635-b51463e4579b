package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.value.DriverResultDTO
import com.ctrip.dcs.ops.infrastructure.value.OpsRatioDTO
import org.apache.commons.lang3.reflect.FieldUtils
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDate

class DriverSupplierServiceTest extends Specification {
    def testObj = new DriverSupplierService()
    def daasGatewayV2 = Mock(DaasGatewayV2)


    def setup() {
        FieldUtils.writeField(testObj, "daasGatewayV2", daasGatewayV2, true)
    }

    @Unroll
    def "querySupplierInfoTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasGatewayV2.queryDataByDaas(_, _) >> [["id":1130746,"c_date":"2024-12-22","corp_id":"6206","corp_name":"U YOUNG TRANSPORT(THAILAND) CO.,LTD.","city_id":"359","city_name":"曼谷","standard_in_line_num":156,"standard_in_line_den":175,"ord_cnt_gold":0,"ord_cnt_silver":40,"ord_cnt_bronze":41,"trail_standard_num":149,"trail_standard_den":175,"hive_d":"2024-12-22","datachange_lasttime":1735526293651],["id":1136122,"c_date":"2024-12-23","corp_id":"6206","corp_name":"U YOUNG TRANSPORT(THAILAND) CO.,LTD.","city_id":"359","city_name":"曼谷","standard_in_line_num":118,"standard_in_line_den":143,"ord_cnt_gold":0,"ord_cnt_silver":25,"ord_cnt_bronze":22,"trail_standard_num":106,"trail_standard_den":143,"hive_d":"2024-12-23","datachange_lasttime":1735526293651],["id":1141498,"c_date":"2024-12-24","corp_id":"6206","corp_name":"U YOUNG TRANSPORT(THAILAND) CO.,LTD.","city_id":"359","city_name":"曼谷","standard_in_line_num":134,"standard_in_line_den":157,"ord_cnt_gold":0,"ord_cnt_silver":32,"ord_cnt_bronze":23,"trail_standard_num":116,"trail_standard_den":157,"hive_d":"2024-12-24","datachange_lasttime":1735526293651],["id":1146881,"c_date":"2024-12-25","corp_id":"6206","corp_name":"U YOUNG TRANSPORT(THAILAND) CO.,LTD.","city_id":"359","city_name":"曼谷","standard_in_line_num":122,"standard_in_line_den":144,"ord_cnt_gold":0,"ord_cnt_silver":30,"ord_cnt_bronze":27,"trail_standard_num":110,"trail_standard_den":144,"hive_d":"2024-12-25","datachange_lasttime":1735526293651],["id":1152257,"c_date":"2024-12-26","corp_id":"6206","corp_name":"U YOUNG TRANSPORT(THAILAND) CO.,LTD.","city_id":"359","city_name":"曼谷","standard_in_line_num":153,"standard_in_line_den":187,"ord_cnt_gold":0,"ord_cnt_silver":38,"ord_cnt_bronze":33,"trail_standard_num":143,"trail_standard_den":187,"hive_d":"2024-12-26","datachange_lasttime":1735526293651],["id":1157626,"c_date":"2024-12-27","corp_id":"6206","corp_name":"U YOUNG TRANSPORT(THAILAND) CO.,LTD.","city_id":"359","city_name":"曼谷","standard_in_line_num":182,"standard_in_line_den":214,"ord_cnt_gold":0,"ord_cnt_silver":40,"ord_cnt_bronze":45,"trail_standard_num":156,"trail_standard_den":214,"hive_d":"2024-12-27","datachange_lasttime":1735526293651],["id":1163009,"c_date":"2024-12-28","corp_id":"6206","corp_name":"U YOUNG TRANSPORT(THAILAND) CO.,LTD.","city_id":"359","city_name":"曼谷","standard_in_line_num":213,"standard_in_line_den":248,"ord_cnt_gold":0,"ord_cnt_silver":60,"ord_cnt_bronze":52,"trail_standard_num":197,"trail_standard_den":248,"hive_d":"2024-12-28","datachange_lasttime":1735526293651],["id":1168378,"c_date":"2024-12-29","corp_id":"6206","corp_name":"U YOUNG TRANSPORT(THAILAND) CO.,LTD.","city_id":"359","city_name":"曼谷","standard_in_line_num":188,"standard_in_line_den":215,"ord_cnt_gold":0,"ord_cnt_silver":30,"ord_cnt_bronze":61,"trail_standard_num":174,"trail_standard_den":215,"hive_d":"2024-12-29","datachange_lasttime":1735526293651]]

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.convertQuertDate(_, _) >>[LocalDate.parse("2024-12-25"), LocalDate.parse("2024-12-29")]
        when:
        def result = spy.querySupplierInfo(partyId, cityId, startDate, endDate)

        then: "验证返回结果里属性值是否符合预期"
        result.size() == expectedResult
        where: "表格方式验证多种分支调用场景"
        endDate   | cityId | partyId   | startDate   || expectedResult
        "endDate" | 1L     | "partyId" | "startDate" ||5
    }

    @Unroll
    def "calculatePreviousIntervalTest"() {
        given: "设定相关方法入参"
        when:
        def result = DriverSupplierService.calculatePreviousInterval(startDate, endDate)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        endDate                       | startDate                     || expectedResult
        LocalDate.parse("2024-12-29") | LocalDate.parse("2024-12-29") || [LocalDate.parse("2024-12-28"), LocalDate.parse("2024-12-28")]
        LocalDate.parse("2024-12-29") | LocalDate.parse("2024-12-27") || [LocalDate.parse("2024-12-24"), LocalDate.parse("2024-12-26")]
    }

    @Unroll
    def "convertQuertDateTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasGatewayV2.queryDataByDaas(_, _) >> [["minDate": "2024-12-29"]]

        when:
        def result = testObj.convertQuertDate(startDate, endDate)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        startDate    | endDate      || expectedResult
        "2024-12-29" | "2024-12-29" || [LocalDate.parse("2024-12-29"), LocalDate.parse("2024-12-29")]
        "2024-12-25" | "2024-12-29" || [LocalDate.parse("2024-12-25"), LocalDate.parse("2024-12-29")]
        "2024-12-29" | "2024-12-31" || [LocalDate.parse("2024-12-27"), LocalDate.parse("2024-12-29")]
    }
}
