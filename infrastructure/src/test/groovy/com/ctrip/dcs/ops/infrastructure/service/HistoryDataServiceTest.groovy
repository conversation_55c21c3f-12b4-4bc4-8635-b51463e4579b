package com.ctrip.dcs.ops.infrastructure.service


import com.ctrip.dcs.ops.infrastructure.config.OpsCommonConfig
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.gateway.IMPlusVendorServiceProxy
import com.ctrip.dcs.ops.infrastructure.value.HistoryScoreInfoDTO
import org.apache.commons.lang3.reflect.FieldUtils
import org.apache.commons.lang3.tuple.Pair
import spock.lang.Specification
import spock.lang.Unroll

class HistoryDataServiceTest extends Specification {
    def testObj = new HistoryDataService()
    def daasGatewayV2 = Mock(DaasGatewayV2)
    def imPlusVendorServiceProxy = Mock(IMPlusVendorServiceProxy)
    def opsCommonConfig = Mock(OpsCommonConfig)

    def setup() {

        FieldUtils.writeField(testObj, "imPlusVendorServiceProxy", imPlusVendorServiceProxy, true)
        FieldUtils.writeField(testObj, "opsCommonConfig", opsCommonConfig, true)
        FieldUtils.writeField(testObj, "daasGatewayV2", daasGatewayV2, true)
    }

    @Unroll
    def "queryHistoryDataListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasGatewayV2.queryDataByDaas(_, _) >> [["String": "Map"]]

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getQueryDate() >> Pair.of("2024-11-05", "2024-11-05 12:23:30")
        when:
        def result = spy.queryHistoryDataList(siteId, partyId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        siteId | partyId || expectedResult
        1L     | 1L      || [new HistoryScoreInfoDTO(score: null, date: null)]
    }
}
