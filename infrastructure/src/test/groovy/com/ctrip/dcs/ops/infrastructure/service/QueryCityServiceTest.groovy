package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV1
import spock.lang.Specification
import spock.lang.Unroll

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*

class QueryCityServiceTest extends Specification {
    def testObj = new QueryCityService()
    def opsExamPlatformGateway = Mock(DaasGatewayV1)

    def setup() {

        testObj.daasGatewayV1 = opsExamPlatformGateway
    }

    @Unroll
    def "queryCityListListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        opsExamPlatformGateway.queryDataByDaas(QUERY_CITY_LIST_PLATFORM_DF, _) >> [["assess_month": "2024-10", "use_city_id": "1"]]
        opsExamPlatformGateway.queryDataByDaas(QUERY_CITY_LIST_PLATFORM_MI, _) >> [["assess_month": "2024-10", "use_city_id": "1"]]
        opsExamPlatformGateway.queryDataByDaas(QUERY_CITY_LIST_TRIPCAR_MI, _) >> [["assess_month": "2024-10", "use_city_id": "1"]]
        opsExamPlatformGateway.queryDataByDaas(QUERY_CITY_LIST_TRIPCAR_DF, _) >> [["assess_month": "2024-10", "use_city_id": "1"]]

        when:
        def result = testObj.queryCityListList(type, partyId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        type       | partyId || expectedResult
        "platform" | "6206"  || ["1"] as Set<String>
        "tripcar"  | "6206"  || ["1"] as Set<String>
    }
}
