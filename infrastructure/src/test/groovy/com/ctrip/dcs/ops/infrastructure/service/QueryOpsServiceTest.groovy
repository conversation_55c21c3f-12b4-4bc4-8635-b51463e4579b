package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.dcs.ops.infrastructure.config.DaasMetaDataConfig
import com.ctrip.dcs.ops.infrastructure.constant.OpsExamPlatformEnum
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV1
import com.ctrip.dcs.ops.infrastructure.util.DateUtil
import spock.lang.Unroll

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*

class QueryOpsServiceTest extends BaseAdapterData {
    def testObj = new QueryOpsService()
    def opsExamPlatformGateway = Mock(DaasGatewayV1)
    def daasMetaDataConfig = Mock(DaasMetaDataConfig)
    def opsExamFieldConfig = getConfigField()
    def opsExamSummaryFieldConfig = getSummaryFiled()
    def opsSourceConfig = getSource()

    def setup() {
        testObj.daasMetaDataConfig = daasMetaDataConfig
        testObj.opsExamSummaryFieldConfig = opsExamSummaryFieldConfig
        testObj.daasGatewayV1 = opsExamPlatformGateway
        testObj.opsExamFieldConfig = opsExamFieldConfig
        testObj.opsSourceConfig = opsSourceConfig
    }

    @Unroll
    def "checkInUnExamTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        opsExamPlatformGateway.queryDataByDaas(CHECK_IS_IN_UNEXAM, ["effectiveMonth": "2024-10", "corpIdUse": "6206", "cityId": "1"]) >> [["cnt": 1]]
        opsExamPlatformGateway.queryDataByDaas(CHECK_IS_IN_UNEXAM, ["effectiveMonth": "2024-09", "corpIdUse": "6206", "cityId": "1"]) >> [["cnt": 0]]

        when:
        def result = testObj.checkInUnExam(partyId, period, cityId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        period    | cityId | partyId || expectedResult
        "2024-10" | 1L     | "6206"  || true
        "2024-09" | 1L     | "6206"  || false
    }

    @Unroll
    def "getSummaryListPairTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        opsExamPlatformGateway.queryDataByDaas(QUERY_RESULT_DETAIL_PLATFORM_DF, _) >> getresult()
        opsExamPlatformGateway.queryDataByDaas(QUERY_RESULT_DETAIL_PLATFORM_MI, _) >> getresult()
        opsExamPlatformGateway.queryDataByDaas(QUERY_RESULT_DETAIL_TRIPCAR_DF, _) >> getresult1()
        opsExamPlatformGateway.queryDataByDaas(QUERY_RESULT_DETAIL_TRIPCAR_DF, _) >> getresult1()
        daasMetaDataConfig.isLimitLastMonth() >> Boolean.TRUE
        daasMetaDataConfig.demoteNotHiveD() >> Boolean.FALSE
        daasMetaDataConfig.getDaysToDemote() >> 1
        daasMetaDataConfig.getLevelList() >> []
        daasMetaDataConfig.scaleInt() >> 1

        when:
        def result = testObj.getSummaryListPair(type, partyId, period, cityId)

        then: "验证返回结果里属性值是否符合预期"
        result.key.overallScore == expectedResult
        where: "表格方式验证多种分支调用场景"
        period                     | cityId | type                         | partyId || expectedResult
        DateUtil.getCurrentMonth() | 1L     | OpsExamPlatformEnum.PLATFORM | "6206"  || 2.6
        DateUtil.getLastMonth()    | 1L     | OpsExamPlatformEnum.PLATFORM | "6206"  || 2.4
        DateUtil.getCurrentMonth() | 1L     | OpsExamPlatformEnum.TRIPCAR  | "6206"  || 2.7
        DateUtil.getLastMonth()    | 1L     | OpsExamPlatformEnum.TRIPCAR  | "6206"  || 2.3
    }
}
