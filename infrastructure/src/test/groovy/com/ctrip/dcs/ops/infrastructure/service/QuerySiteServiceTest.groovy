package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.basebiz.geolocation.service.GetLocationInfosResponseType
import com.ctrip.basebiz.geolocation.service.LocationInfo
import com.ctrip.dcs.ops.infrastructure.config.OpsCommonConfig
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV1
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.gateway.GeoLocationServiceProxy
import com.ctrip.dcs.ops.infrastructure.gateway.IMPlusVendorServiceProxy
import com.ctrip.dcs.ops.infrastructure.value.CityDTO
import com.ctrip.dcs.ops.infrastructure.value.ProvinceInfoDTO
import com.ctrip.dcs.ops.infrastructure.value.SiteInfoDTO
import org.apache.commons.lang3.reflect.FieldUtils
import org.apache.commons.lang3.tuple.Pair
import spock.lang.Specification
import spock.lang.Unroll

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.QUERY_OPS_SITE_LIST

class QuerySiteServiceTest extends Specification {
    def testObj = new QuerySiteService()
    def daasGatewayV1 = Mock(DaasGatewayV1)
    def geoLocationServiceProxy = Mock(GeoLocationServiceProxy)
    def opsCommonConfig = Mock(OpsCommonConfig)
    def daasGatewayV2 = Mock(DaasGatewayV2)
    def imPlusVendorServiceProxy = Mock(IMPlusVendorServiceProxy)

    def setup() {

        FieldUtils.writeField(testObj, "imPlusVendorServiceProxy", imPlusVendorServiceProxy, true)
        testObj.opsCommonConfig = opsCommonConfig
        FieldUtils.writeField(testObj, "daasGatewayV2", daasGatewayV2, true)
        testObj.daasGatewayV1 = daasGatewayV1
        testObj.geoLocationServiceProxy = geoLocationServiceProxy
    }

    @Unroll
    def "querySiteListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        daasGatewayV1.queryDataByDaas(QUERY_OPS_SITE_LIST, _) >> [["country_id": 1, "province_id": 2, "city_id_list": "12-23-32"],["country_id": 1, "province_id": 2, "city_id_list": "45-56-78"]]
        geoLocationServiceProxy.getLocationInfos(_) >> new GetLocationInfosResponseType(locationInfos: [new LocationInfo(globalid: 1L, name: "11", translation: "111"),
                                                                                                        new LocationInfo(globalid: 2L, name: "22", translation: "222"),
                                                                                                        new LocationInfo(globalid: 12L, name: "33", translation: "333"),
                                                                                                        new LocationInfo(globalid: 23L, name: "44", translation: "444"),
                                                                                                        new LocationInfo(globalid: 32L, name: "55", translation: "555"),
                                                                                                        new LocationInfo(globalid: 45L, name: "66", translation: "666"),
                                                                                                        new LocationInfo(globalid: 56L, name: "77", translation: "777"),
                                                                                                        new LocationInfo(globalid: 78L, name: "88", translation: "888")])
        opsCommonConfig.getCountryDisable() >> Boolean.TRUE
        opsCommonConfig.getProvinceDisable() >> Boolean.TRUE
        opsCommonConfig.getCityWhiteList() >> [1L]
        opsCommonConfig.getCityWhiteListDisable() >> Boolean.TRUE

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getQueryDate() >> Pair.of("2024-11-05", "2024-11-05 12:23:30")
        when:
        def result = spy.querySiteList(type, partyId, locale)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        type  | partyId  | locale  || expectedResult
        "day" | "123123" | "zh-CN" || [new SiteInfoDTO(countryId:1, countryName:111, provinceList:[new ProvinceInfoDTO(provinceId:2, provinceName:222, cityDTOS:[new CityDTO(cityId:32, cityName:555, disable:true), new CityDTO(cityId:23, cityName:444, disable:true),new  CityDTO(cityId:56, cityName:777, disable:true), new CityDTO(cityId:12, cityName:333, disable:true), new CityDTO(cityId:45, cityName:666, disable:true), new CityDTO(cityId:78, cityName:888, disable:true)], disable:true)], disable:true)]

    }
}
