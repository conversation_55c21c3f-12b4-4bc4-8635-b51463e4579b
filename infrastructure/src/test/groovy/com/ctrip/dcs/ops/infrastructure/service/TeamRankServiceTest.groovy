package com.ctrip.dcs.ops.infrastructure.service

import com.ctrip.basebiz.implus.vendor.contract.GetVenAgentStatusResponseType
import com.ctrip.basebiz.implus.vendor.contract.VendorAgentStatus
import com.ctrip.dcs.ops.infrastructure.config.OpsCommonConfig
import com.ctrip.dcs.ops.infrastructure.gateway.DaasGatewayV2
import com.ctrip.dcs.ops.infrastructure.gateway.IMPlusVendorServiceProxy
import com.ctrip.dcs.ops.infrastructure.value.PageDTO
import com.ctrip.dcs.ops.infrastructure.value.TeamInfoDTO
import com.ctrip.dcs.ops.infrastructure.value.TeamInfoPageDTO
import spock.lang.Specification
import spock.lang.Unroll

import static com.ctrip.dcs.ops.infrastructure.constant.ApiConstant.*

class TeamRankServiceTest extends Specification {
    def testObj = new TeamRankService()
    def daasGatewayV2 = Mock(DaasGatewayV2)
    def imPlusVendorServiceProxy = Mock(IMPlusVendorServiceProxy)
    def opsCommonConfig = Mock(OpsCommonConfig)

    def setup() {
        testObj.daasGatewayV2 = daasGatewayV2
        testObj.imPlusVendorServiceProxy = imPlusVendorServiceProxy
        testObj.opsCommonConfig = opsCommonConfig
    }

    @Unroll
    def "queryTeamRankListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
//        daasGatewayV2.queryDataByDaas(_, _) >> [["String": "Map"]]
        opsCommonConfig.getDaysToDemote() >> 0
        daasGatewayV2.queryDataByDaas(QUERY_TOTAL_DATA_BY_PAGE, _) >> [["ops_rank": 2, "advisor_name": "advisorName", "vendor_id": 23123]]
        daasGatewayV2.queryDataByDaas(QUERY_CNT_TOTAL, _) >> [["cnt": 20]]
        daasGatewayV2.queryDataByDaas(QUERY_MAX_DATE, _) >> [["updateTime": 1730877878259, "result": "2024-11-05"]]
        imPlusVendorServiceProxy.getVenAgentStatus(_) >> new GetVenAgentStatusResponseType(statusList: ["String": new VendorAgentStatus(isWorkTime: 0)])

        when:
        def result = testObj.queryTeamRankList(partyId, siteId, pageNo, size)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        size | pageNo | siteId | partyId || expectedResult
        10   | 1      | 1L     | "123"   || new TeamInfoPageDTO(teamInfoDTOS: [new TeamInfoDTO(teamName: "advisorName", rank: 2)], pageDTO: new PageDTO(pageNo: 1, pageSize: 10, totalSize: 20, totalPages: 2))
    }
}
