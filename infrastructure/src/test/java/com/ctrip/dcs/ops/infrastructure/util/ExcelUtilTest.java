package com.ctrip.dcs.ops.infrastructure.util;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import static org.junit.Assert.*;

import com.ctrip.dcs.ops.infrastructure.value.OrderProblemResultDTO;

/**
 * ExcelUtil测试类
 */
public class ExcelUtilTest {
    
    @Test
    public void testGenerateFileName() {
        String fileName = ExcelUtil.generateFileName("123", "2024-01", 1L, "test", "novehicle");
        
        assertNotNull("文件名不应为空", fileName);
        assertTrue("文件名应包含供应商ID", fileName.contains("123"));
        assertTrue("文件名应包含周期", fileName.contains("2024-01"));
        assertTrue("文件名应包含城市ID", fileName.contains("1"));
        assertTrue("文件名应包含服务类型", fileName.contains("test"));
        assertTrue("文件名应包含问题类型", fileName.contains("novehicle"));
        assertTrue("文件名应以.xlsx结尾", fileName.endsWith(".xlsx"));
    }
    
    @Test
    public void testGenerateFileNameWithNullValues() {
        String fileName = ExcelUtil.generateFileName("123", "2024-01", null, null, null);
        
        assertNotNull("文件名不应为空", fileName);
        assertTrue("文件名应包含供应商ID", fileName.contains("123"));
        assertTrue("文件名应包含周期", fileName.contains("2024-01"));
        assertTrue("文件名应以.xlsx结尾", fileName.endsWith(".xlsx"));
    }

}
