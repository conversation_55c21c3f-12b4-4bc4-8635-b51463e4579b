<project>
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ctrip.dcs</groupId>
        <artifactId>go</artifactId>
        <version>9.16.0</version>
    </parent>

    <groupId>com.ctrip.dcs.ops</groupId>
    <artifactId>dcs-ops-platform</artifactId>
    <version>1.0.0</version>

    <packaging>pom</packaging>

    <properties>
        <java.version>21</java.version>
    </properties>

    <modules>
        <module>application</module>
        <module>infrastructure</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.sysdev</groupId>
                <artifactId>daas-client-soa</artifactId>
                <version>1.0.10</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.igt.framework</groupId>
                <artifactId>soa-common</artifactId>
                <version>6.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.tour</groupId>
                <artifactId>tour.auth.soa</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.scm</groupId>
                <artifactId>scm-sdk</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.platform.members.geolocation</groupId>
                <artifactId>geolocationservice</artifactId>
                <version>1.1.40</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.platform.basesystem.vendorservice.v1</groupId>
                <artifactId>vendorservice</artifactId>
                <version>0.6.31</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.14.5</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>ibu-shark-sdk</artifactId>
                <version>5.1.3</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.tour.tripservice</groupId>
                <artifactId>crm-backedservice-contract</artifactId>
                <version>0.0.8</version>
            </dependency>
            <dependency>
                <artifactId>commons-compress</artifactId>
                <groupId>org.apache.commons</groupId>
                <version>1.19</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>soa-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.scm</groupId>
            <artifactId>scm-sdk</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>shark-maven-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>pack-download</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>